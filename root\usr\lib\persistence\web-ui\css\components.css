/*
 * FILE          : components.css
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : Component-specific styles for PersistenceOS UI
 */

/* Buttons
   ========================================================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-weight: var(--font-weight-normal);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    border: 1px solid transparent;
    font-size: var(--font-size-base);
    line-height: 1.5;
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem var(--input-focus-shadow);
}

.btn:disabled,
.btn.disabled {
    opacity: 0.65;
    pointer-events: none;
}

.btn i,
.btn .icon {
    margin-right: 0.5rem;
}

/* Button Variants */
.btn-primary {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary);
    color: white;
    border-color: var(--secondary);
}

.btn-secondary:hover {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
}

.btn-success {
    background-color: var(--success);
    color: white;
    border-color: var(--success);
}

.btn-success:hover {
    background-color: var(--success-dark);
    border-color: var(--success-dark);
}

.btn-danger {
    background-color: var(--danger);
    color: white;
    border-color: var(--danger);
}

.btn-danger:hover {
    background-color: var(--danger-dark);
    border-color: var(--danger-dark);
}

.btn-warning {
    background-color: var(--warning);
    color: var(--foreground);
    border-color: var(--warning);
}

.btn-warning:hover {
    background-color: var(--warning-dark);
    border-color: var(--warning-dark);
}

.btn-info {
    background-color: var(--info);
    color: white;
    border-color: var(--info);
}

.btn-info:hover {
    background-color: var(--info-dark);
    border-color: var(--info-dark);
}

.btn-light {
    background-color: var(--background);
    color: var(--foreground);
    border-color: var(--border-color);
}

.btn-light:hover {
    background-color: var(--border-color);
}

.btn-link {
    background-color: transparent;
    color: var(--primary);
    border-color: transparent;
    padding-left: 0;
    padding-right: 0;
    text-decoration: none;
}

.btn-link:hover {
    text-decoration: underline;
    background-color: transparent;
    border-color: transparent;
}

/* Button Sizes */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius-sm);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
}

/* Icon Button */
.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    border-radius: 50%;
}

.btn-icon.btn-sm {
    width: 2rem;
    height: 2rem;
}

.btn-icon.btn-lg {
    width: 3rem;
    height: 3rem;
}

.btn-icon i,
.btn-icon .icon {
    margin-right: 0;
}

/* Button Group */
.btn-group {
    display: inline-flex;
    position: relative;
}

.btn-group .btn {
    position: relative;
    flex: 1 1 auto;
}

.btn-group .btn:not(:first-child) {
    margin-left: -1px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.btn-group .btn:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* Cards
   ========================================================================== */

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--card-bg);
    background-clip: border-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow);
}

.card-header {
    padding: 1rem;
    margin-bottom: 0;
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header:first-child {
    border-radius: calc(var(--border-radius) - 1px) calc(var(--border-radius) - 1px) 0 0;
}

.card-body {
    flex: 1 1 auto;
    padding: 1rem;
}

.card-footer {
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.03);
    border-top: 1px solid var(--border-color);
}

.card-footer:last-child {
    border-radius: 0 0 calc(var(--border-radius) - 1px) calc(var(--border-radius) - 1px);
}

.card-title {
    margin-bottom: 0.75rem;
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
}

.card-subtitle {
    margin-top: -0.375rem;
    margin-bottom: 0.5rem;
    color: var(--secondary);
}

.card-text:last-child {
    margin-bottom: 0;
}

.card-link {
    color: var(--primary);
    text-decoration: none;
}

.card-link:hover {
    text-decoration: underline;
}

.card-link + .card-link {
    margin-left: 1rem;
}

/* Card Variants */
.card-primary {
    border-top: 4px solid var(--primary);
}

.card-success {
    border-top: 4px solid var(--success);
}

.card-warning {
    border-top: 4px solid var(--warning);
}

.card-danger {
    border-top: 4px solid var(--danger);
}

.card-info {
    border-top: 4px solid var(--info);
}

/* Forms
   ========================================================================== */

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: inline-block;
    margin-bottom: 0.5rem;
    font-weight: var(--font-weight-normal);
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--foreground);
    background-color: var(--input-bg);
    background-clip: padding-box;
    border: 1px solid var(--input-border);
    border-radius: var(--border-radius);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: var(--foreground);
    background-color: var(--input-bg);
    border-color: var(--input-focus-border);
    outline: 0;
    box-shadow: 0 0 0 0.2rem var(--input-focus-shadow);
}

.form-control::placeholder {
    color: var(--secondary);
    opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
    background-color: var(--border-color);
    opacity: 1;
}

.form-text {
    display: block;
    margin-top: 0.25rem;
    font-size: var(--font-size-sm);
    color: var(--secondary);
}

.form-check {
    position: relative;
    display: block;
    padding-left: 1.25rem;
}

.form-check-input {
    position: absolute;
    margin-top: 0.3rem;
    margin-left: -1.25rem;
}

.form-check-label {
    margin-bottom: 0;
}

.form-check-inline {
    display: inline-flex;
    align-items: center;
    padding-left: 0;
    margin-right: 0.75rem;
}

.form-check-inline .form-check-input {
    position: static;
    margin-top: 0;
    margin-right: 0.3125rem;
    margin-left: 0;
}

/* Alerts
   ========================================================================== */

.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-heading {
    color: inherit;
}

.alert-link {
    font-weight: var(--font-weight-bold);
}

.alert-dismissible {
    padding-right: 4rem;
}

.alert-dismissible .close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.75rem 1.25rem;
    color: inherit;
}

.alert-primary {
    color: var(--primary-dark);
    background-color: rgba(var(--primary-rgb), 0.1);
    border-color: rgba(var(--primary-rgb), 0.2);
}

.alert-secondary {
    color: var(--secondary-dark);
    background-color: rgba(var(--secondary-rgb), 0.1);
    border-color: rgba(var(--secondary-rgb), 0.2);
}

.alert-success {
    color: var(--success-dark);
    background-color: rgba(var(--success-rgb), 0.1);
    border-color: rgba(var(--success-rgb), 0.2);
}

.alert-danger {
    color: var(--danger-dark);
    background-color: rgba(var(--danger-rgb), 0.1);
    border-color: rgba(var(--danger-rgb), 0.2);
}

.alert-warning {
    color: var(--warning-dark);
    background-color: rgba(var(--warning-rgb), 0.1);
    border-color: rgba(var(--warning-rgb), 0.2);
}

.alert-info {
    color: var(--info-dark);
    background-color: rgba(var(--info-rgb), 0.1);
    border-color: rgba(var(--info-rgb), 0.2);
}

/* Badges
   ========================================================================== */

.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: var(--font-weight-bold);
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius-sm);
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.badge-primary {
    color: #fff;
    background-color: var(--primary);
}

.badge-secondary {
    color: #fff;
    background-color: var(--secondary);
}

.badge-success {
    color: #fff;
    background-color: var(--success);
}

.badge-danger {
    color: #fff;
    background-color: var(--danger);
}

.badge-warning {
    color: var(--foreground);
    background-color: var(--warning);
}

.badge-info {
    color: #fff;
    background-color: var(--info);
}

.badge-light {
    color: var(--foreground);
    background-color: var(--background);
}

.badge-dark {
    color: #fff;
    background-color: var(--foreground);
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25em 0.75em;
    font-size: 0.75rem;
    font-weight: var(--font-weight-bold);
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 1rem;
}

.status-badge::before {
    content: "";
    display: inline-block;
    width: 0.5rem;
    height: 0.5rem;
    margin-right: 0.5rem;
    border-radius: 50%;
}

.status-badge.running {
    color: var(--success);
    background-color: rgba(var(--success-rgb), 0.1);
}

.status-badge.running::before {
    background-color: var(--success);
}

.status-badge.stopped {
    color: var(--secondary);
    background-color: rgba(var(--secondary-rgb), 0.1);
}

.status-badge.stopped::before {
    background-color: var(--secondary);
}

.status-badge.error {
    color: var(--danger);
    background-color: rgba(var(--danger-rgb), 0.1);
}

.status-badge.error::before {
    background-color: var(--danger);
}

.status-badge.warning {
    color: var(--warning-dark);
    background-color: rgba(var(--warning-rgb), 0.1);
}

.status-badge.warning::before {
    background-color: var(--warning);
}

.status-badge.healthy {
    color: var(--success);
    background-color: rgba(var(--success-rgb), 0.1);
}

.status-badge.healthy::before {
    background-color: var(--success);
}

.status-badge.degraded {
    color: var(--warning-dark);
    background-color: rgba(var(--warning-rgb), 0.1);
}

.status-badge.degraded::before {
    background-color: var(--warning);
}

.status-badge.critical {
    color: var(--danger);
    background-color: rgba(var(--danger-rgb), 0.1);
}

.status-badge.critical::before {
    background-color: var(--danger);
}
