#!/bin/bash
# DEPRECATED: This script is no longer used
# JavaScript files (including Vue.js) are now handled by:
# 1. RPM spec file copying from OBS SOURCES during build
# 2. config.sh comprehensive file detection during runtime
# 3. CDN fallback in the web UI if local files are not available

echo "⚠️  DEPRECATED: get-vue.sh is no longer used"
echo "JavaScript files are now handled by the consolidated approach:"
echo "1. RPM spec file copies files from OBS SOURCES during build"
echo "2. config.sh detects and copies files during runtime if needed"
echo "3. Web UI uses CDN fallback if local files are not available"
echo ""
echo "If you need to manually install Vue.js, use:"
echo "  /usr/lib/persistence/scripts/config.sh"
echo ""
exit 0