# PersistenceOS Rebuild Project Plan

## Project Overview
PersistenceOS is a full web UI administration OS based on MicroOS, using Nginx, Python, and FastAPI/Uvicorn for the web UI development. This document outlines the detailed rebuild plan to ensure a clean, accurate implementation without filler code.

## Complete Architecture and Data Flow

PersistenceOS implements a comprehensive architecture that connects the direct HTML/CSS frontend to MicroOS packages through a chain of well-defined components. This architecture enables the web UI to display system information and perform administration tasks while leveraging the full power of MicroOS.

### Architecture Overview

```
User Browser → Nginx → [Static Files OR API Requests] →
                         ├── Static Files: HTML/CSS/JS (Direct from Nginx)
                         └── API Requests: Nginx → Uvicorn → FastAPI → Shell Scripts → MicroOS Packages
```

### Complete Data Flow Chain

```
Frontend HTML/JS → FastAPI Routers → Python Utils → Shell Scripts → MicroOS Packages → Data → Frontend Display
```

### Component Roles and Responsibilities

1. **Direct HTML/CSS Frontend**
   - **Role**: Provides the user interface and handles client-side interactions
   - **Components**:
     - `login.html`: Authentication entry point
     - `index.html`: Main application container with sidebar and content sections
     - CSS files: Styling for all UI components
     - JavaScript files: Client-side logic for navigation, API communication, and UI updates
   - **Responsibilities**:
     - Presenting a clean, responsive interface
     - Handling user interactions
     - Managing client-side navigation
     - Communicating with the API
     - Updating the UI based on API responses
     - Managing authentication state

2. **Nginx**
   - **Role**: Serves static files and proxies API requests
   - **Components**:
     - `persistenceos-nginx.conf`: Configuration for static file serving and API proxying
   - **Responsibilities**:
     - Serving HTML, CSS, and JavaScript files directly
     - Proxying API requests to Uvicorn
     - Handling HTTP caching
     - Managing CORS headers
     - Providing basic security headers
     - Load balancing (if needed in the future)

3. **Python FastAPI/Uvicorn Backend**
   - **Role**: Processes API requests and communicates with system components
   - **Components**:
     - Uvicorn: ASGI server running on localhost:8000
     - FastAPI: Web framework for API endpoints
     - Routers: Modular API endpoints for different sections
     - Utils: Utility functions, including ShellScriptRunner
   - **Responsibilities**:
     - Authenticating and authorizing requests
     - Validating input data
     - Calling appropriate shell scripts
     - Parsing script output into structured data
     - Returning JSON responses
     - Handling errors and exceptions
     - Logging API activities

4. **Shell Scripts**
   - **Role**: Interface between Python and MicroOS packages
   - **Components**:
     - `system-manager.sh`: System information and management
     - `storage-manager.sh`: Storage pool and volume management
     - `network-manager.sh`: Network configuration and welcome message management
     - Other specialized scripts
   - **Responsibilities**:
     - Executing system commands using MicroOS packages
     - Formatting output for Python parsing
     - Handling command-specific logic
     - Providing error information
     - Logging script activities
     - Updating system welcome message with IP address (network-manager.sh update-motd)

5. **MicroOS Packages**
   - **Role**: Provide core system functionality
   - **Components**:
     - System utilities: procps, util-linux
     - Storage utilities: btrfsprogs, xfsprogs, lvm2
     - Network utilities: iproute2, NetworkManager
     - Virtualization: libvirt, qemu-kvm
     - Health monitoring: monitoring-plugins, prometheus-node_exporter, curl, jq
     - Backup utilities: snapper, snapper-zypp-plugin, btrfsprogs, rsync
   - **Responsibilities**:
     - Executing low-level system operations
     - Managing system resources
     - Providing command-line interfaces
     - Implementing core functionality
     - Monitoring system health
     - Creating and managing btrfs snapshots with snapper
     - Performing coordinated backups across system and VMs

### Detailed Flow Explanation

1. **Initial Access and Authentication**
   - User navigates to `http://server-ip:8080` in their browser
   - Nginx serves `login.html` directly from `/usr/share/persistenceos-ui/`
   - User enters credentials in the login form
   - JavaScript makes a POST request to `/api/auth/login`
   - Nginx proxies this request to Uvicorn/FastAPI
   - FastAPI validates credentials and returns a JWT token
   - JavaScript stores the token in localStorage
   - Browser redirects to `index.html`

2. **Main Interface Loading**
   - Nginx serves `index.html` and associated assets
   - JavaScript initializes and checks URL hash (defaults to `#dashboard`)
   - JavaScript makes a GET request to `/api/system/info` with the auth token
   - FastAPI processes the request and calls `system-manager.sh info`
   - The shell script uses MicroOS packages to gather system information
   - Data flows back through the chain to the frontend
   - JavaScript updates the dashboard with system metrics

3. **Navigation Between Sections**
   - User clicks a sidebar menu item
   - JavaScript updates the URL hash without page reload
   - JavaScript hides all content sections and shows the selected one
   - JavaScript makes an API request to load data for the selected section
   - The request flows through the chain: Nginx → FastAPI → Shell Script → MicroOS Packages
   - Data flows back and JavaScript updates the section content

4. **User Actions and API Interactions**
   - User performs an action (e.g., creates a storage pool)
   - JavaScript sends data to the appropriate API endpoint
   - FastAPI validates the input and calls the relevant shell script
   - The shell script uses MicroOS packages to perform the action
   - Result flows back through the chain to the frontend
   - JavaScript updates the UI based on the result

### Upgrade System Implementation

The upgrade system provides a seamless path for transitioning between PersistenceOS versions without reinstallation. It leverages the transactional update system of MicroOS while ensuring data integrity through snapper/btrfs snapshots.

1. **Upgrade Manager Script**
   - **Purpose**: Coordinate the entire upgrade process
   - **Implementation**:
     ```bash
     #!/bin/bash
     # upgrade-manager.sh - PersistenceOS upgrade management script

     source /usr/lib/persistence/bin/util-common.sh

     function check_updates() {
       log_info "Checking for available updates..."
       /usr/lib/persistence/bin/upgrade-check.sh
     }

     function create_pre_upgrade_snapshot() {
       log_info "Creating pre-upgrade snapshot..."
       /usr/lib/persistence/bin/backup-manager.sh create-snapshot "pre-upgrade"
     }

     function apply_system_updates() {
       log_info "Applying system updates..."
       transactional-update pkg update -y
     }

     function migrate_configuration() {
       log_info "Migrating configuration to new version..."
       /usr/lib/persistence/bin/version-migration.sh
     }

     function handle_upgrade_failure() {
       log_error "Upgrade failed, initiating rollback..."
       /usr/lib/persistence/bin/upgrade-rollback.sh
     }

     function upgrade_system() {
       log_info "Starting system upgrade process..."

       # Check for updates
       if ! check_updates; then
         log_error "No updates available or system not compatible with upgrade"
         return 1
       fi

       # Create pre-upgrade snapshot
       if ! create_pre_upgrade_snapshot; then
         log_error "Failed to create pre-upgrade snapshot"
         return 1
       fi

       # Apply system updates
       if ! apply_system_updates; then
         log_error "Failed to apply system updates"
         handle_upgrade_failure
         return 1
       fi

       # Migrate configuration
       if ! migrate_configuration; then
         log_error "Failed to migrate configuration"
         handle_upgrade_failure
         return 1
       }

       log_info "Upgrade completed successfully. Please reboot the system."
       return 0
     }

     # Command dispatcher
     case "$1" in
       check)
         check_updates
         ;;
       upgrade)
         upgrade_system
         ;;
       rollback)
         handle_upgrade_failure
         ;;
       *)
         echo "Usage: $0 {check|upgrade|rollback}"
         exit 1
         ;;
     esac
     ```

2. **Version Migration Script**
   - **Purpose**: Handle version-specific migration tasks
   - **Implementation**: Contains version-specific logic for configuration changes, database schema updates, and storage format migrations

3. **Upgrade Check Script**
   - **Purpose**: Verify update availability and system compatibility
   - **Implementation**: Checks for available updates, verifies system requirements, and ensures all prerequisites are met

4. **Upgrade Rollback Script**
   - **Purpose**: Restore system to pre-upgrade state if upgrade fails
   - **Implementation**: Leverages snapper/btrfs snapshots to roll back to the pre-upgrade system state

5. **API Integration**
   - **Purpose**: Provide web UI access to upgrade functionality
   - **Implementation**: FastAPI endpoints for checking updates, initiating upgrades, and monitoring upgrade progress

6. **Web UI Integration**
   - **Purpose**: User-friendly interface for system upgrades
   - **Implementation**: Displays available updates, upgrade progress, and results in the web UI

### Practical Example: Creating a VM with Error Handling and Loading Indicators

Let's walk through a complete flow example for creating a virtual machine, including error handling and loading indicators:

1. **User navigates to VM section**:
   - User clicks "Virtualization" in the sidebar
   - JavaScript updates URL to `#vms` and shows the VM section
   - LoadingIndicators.showLoadingOverlay('vms-section')
   - JavaScript requests `/api/vms/list` with auth token
   - Nginx → Uvicorn → FastAPI → `system-manager.sh vms list` → libvirt
   - JavaScript displays current VMs
   - LoadingIndicators.hideLoadingOverlay('vms-section')
   - If an error occurs, ErrorHandlers.handleApiError(error, 'load virtual machines')

2. **User initiates VM creation**:
   - User clicks "Create VM" button
   - JavaScript shows VM creation form
   - User enters VM details (name, CPU, memory, disk, OS)
   - User clicks "Create" button

3. **API processes VM creation with loading state and error handling**:
   ```javascript
   // Show loading state on the create button
   LoadingIndicators.showLoading('create-vm-button');

   try {
     // Send request to API
     const response = await API.createVM(vmDetails);

     // Show success notification
     ErrorHandlers.showSuccessNotification('Virtual machine created successfully');

     // Update VM list
     await loadVirtualMachines();
   } catch (error) {
     // Handle any errors that occur
     ErrorHandlers.handleApiError(error, 'create virtual machine');
   } finally {
     // Hide loading state regardless of success or failure
     LoadingIndicators.hideLoading('create-vm-button');
   }
   ```

   - Nginx → Uvicorn → FastAPI validates input
   - FastAPI calls `system-manager.sh vms create` with parameters
   - Script uses libvirt-client and qemu-kvm to create the VM
   - Script returns status information
   - FastAPI returns JSON response
   - JavaScript shows success message and updates VM list

4. **User manages the new VM with loading state and error handling**:
   - VM appears in the list with status "Stopped"
   - User clicks "Start" button for the VM
   - LoadingIndicators.showLoading('start-vm-button-' + vmId)
   - JavaScript sends POST to `/api/vms/{id}/start` with auth token
   - Nginx → Uvicorn → FastAPI → `system-manager.sh vms start {id}` → libvirt
   - If successful:
     - VM status updates to "Running"
     - ErrorHandlers.showSuccessNotification('Virtual machine started successfully')
   - If an error occurs:
     - ErrorHandlers.handleApiError(error, 'start virtual machine')
   - LoadingIndicators.hideLoading('start-vm-button-' + vmId)

This enhanced flow provides a much better user experience by:
1. Showing loading indicators during API calls
2. Preventing multiple button clicks during operations
3. Displaying clear success and error messages
4. Handling authentication errors consistently
5. Providing visual feedback throughout the process

### Benefits of This Architecture

1. **Direct Access to MicroOS Functionality**
   - Web UI can display any information available from MicroOS packages
   - All system administration capabilities of MicroOS are accessible through the UI
   - No need to reimplement functionality that already exists in MicroOS packages

2. **Clean Separation of Concerns**
   - Frontend only handles display and user interaction
   - Python handles API routing, authentication, and data transformation
   - Shell scripts handle direct interaction with MicroOS packages
   - Each layer has a clear, focused responsibility

3. **Efficient Data Flow**
   - Data flows efficiently through the chain
   - Each component adds value (authentication, validation, transformation)
   - The frontend receives exactly the data it needs in the right format

4. **Extensibility**
   - Adding new functionality is straightforward
   - New MicroOS packages can be leveraged by adding shell script commands
   - New API endpoints can expose additional functionality
   - New UI components can display or interact with this functionality

5. **Maintainability**
   - Each component can be maintained independently
   - Changes to one component have minimal impact on others
   - Clear interfaces between components make debugging easier

## Foundation Files

### PersistenceOS.kiwi - The Core Build File
The PersistenceOS.kiwi file is the **central configuration file** for building our project image using OBS SUSE Studio installer. This file defines:

1. **Base System**: SUSE MicroOS 6.1 as the foundation
2. **Package Selection**: All required packages for the system
3. **Filesystem Configuration**: Partition layout and filesystem types
4. **Boot Configuration**: GRUB2 settings and boot parameters
5. **Script Integration**: How installation scripts are executed

All components in this rebuild plan must align with and support the specifications in the PersistenceOS.kiwi file. When creating new files or modifying existing ones, always refer back to this file to ensure compatibility.

### Building with OBS SUSE Studio
Our project image will be built using the Open Build Service (OBS) SUSE Studio installer, which:

1. Uses the PersistenceOS.kiwi file as the primary build configuration
2. Processes the scripts in the order specified in the kiwi file
3. Creates a bootable ISO image with our custom configuration
4. Integrates all components into a cohesive system

When uploading files to OBS, the correct sequence and file organization are critical for a successful build.

## Naming Conventions

To ensure consistency and maintainability across the PersistenceOS project, we adopt the following naming conventions:

### Service File Naming

All SystemD service files **MUST** use the `persistenceos-` prefix (not `persistence-`).

| Service Type | Naming Pattern | Example |
|--------------|----------------|---------|
| Core Services | `persistenceos-{function}.service` | `persistenceos-api.service` |
| Setup Services | `persistenceos-{component}-setup.service` | `persistenceos-ui-setup.service` |
| Installation Services | `persistenceos-{stage}-install.service` | `persistenceos-firstboot.service` |
| Daemon Services | `persistenceos-{function}-daemon.service` | `persistenceos-health-daemon.service` |
| Debug Services | `persistenceos-{function}-debug.service` | `persistenceos-installer-debug.service` |

### Script Naming Conventions

Scripts should use prefixes based on their function:

| Script Type | Prefix | Example |
|-------------|--------|---------|
| System Management | `system-` | `system-manager.sh` |
| UI Management | `ui-` | `ui-manager.sh` |
| Installation | `install-` | `install-manager.sh` |
| Configuration | `config-` | `config-manager.sh` |
| Network | `network-` | `network-manager.sh` |
| Storage | `storage-` | `storage-manager.sh` |
| Utilities | `util-` | `util-common.sh` |

### Directory Structure Naming

| Directory Purpose | Standard Name | Path |
|-------------------|---------------|------|
| Root Directory | `persistence` | `/usr/lib/persistence` |
| Scripts | `scripts` | `/usr/lib/persistence/scripts` |
| Binaries | `bin` | `/usr/lib/persistence/bin` |
| Services | `services` | `/usr/lib/persistence/services` |
| Tools | `tools` | `/usr/lib/persistence/tools` |
| Web UI | `web-ui` | `/usr/lib/persistence/web-ui` |
| API | `api` | `/usr/lib/persistence/api` |
| VM Templates | `vm-templates` | `/usr/lib/persistence/vm-templates` |
| Health Checks | `health` | `/usr/lib/persistence/health` |
| Backup | `backup` | `/usr/lib/persistence/backup` |
| Data | `persistenceos` | `/var/lib/persistenceos` |
| Logs | `persistence` | `/var/log/persistence` |
| Backups | `backups` | `/var/lib/persistenceos/backups` |

## Core Components and Build Sequence

### Phase 1: Foundation Setup

1. **Directory Structure Setup**
   - Create the basic directory structure following PersistenceOS-Structure.md
   - Set up OBS project structure for building

2. **Common Functions Library (util-common.sh)**
   - Implement robust error handling
   - Create logging functions
   - Add utility functions for system operations
   - Implement environment detection

3. **Core Configuration Scripts**
   - **config-manager.sh**: Main system configuration script
   - **config-firewall.sh**: Firewall configuration with nftables
   - **install-packages.sh**: Package installation management

### Phase 2: Installation System

4. **Boot and Installation Scripts**
   - **install-manager.sh**: Consolidated installation script that handles all installation operations with a command-based interface. This script replaces and consolidates functionality from multiple previous scripts, including partitioning, formatting (inc. BTRFS subvolumes), OS image deployment, bootloader installation, fstab creation (UUIDs), system configuration (hostname, timezone, locale), Snapper setup, service enablement, welcome message creation, profile script creation, and interacts with `installer.conf`.
     - **Oldeditbootinstaller.sh**: Previous bootloader configuration script from the old project that had a more direct approach to bootloader installation and service configuration
     - **editbootinstaller.sh**: Bootloader configuration script that detected boot mode and configured GRUB
     - **persistenceos-install.sh**: Main installation script that coordinated the installation process
     - **install-bootloader.sh**: Bootloader installation script that installed and configured GRUB
     - **install-system.sh**: System installation script that installed the base system
     - **install-post.sh**: Post-installation configuration script that configured the system after installation
     - **install-packages.sh**: Package installation script that installed required packages
     - **install-scripts.sh**: Script installation script that installed system scripts
   - **ui-extract.sh**: Script to extract the UI tarball during installation

5. **Consolidated Service Files**
   - **persistenceos-installation.service**: Consolidated installation service that handles all installation phases (detection, environment setup, and installation)
   - **persistenceos-network.service**: Consolidated network service that handles MOTD updates, network configuration, and firewall setup
   - **persistenceos-health-monitoring.service**: Consolidated health monitoring service that runs system health checks, API health checks, and starts continuous monitoring
   - **persistenceos-backup-system.service**: Consolidated backup service that handles snapper configuration, snapshot creation, and cleanup
   - **persistenceos-backup-system.timer**: Timer for the consolidated backup service
   - **persistenceos-nftables-setup.service**: Firewall setup service
   - **persistenceos-services-init.service**: Services initialization
   - **persistenceos-firstboot.service**: First boot configuration service
   - **persistenceos-api.service**: FastAPI backend service

6. **Service Consolidation Benefits**
   - Reduced file count from 18 to 7 service files
   - Simplified service management with fewer services to enable, disable, and monitor
   - Centralized configuration with related settings in one place
   - Cleaner OBS source files with fewer files in the build system
   - Easier troubleshooting with fewer places to look for issues
   - Better logging with more detailed information about service operations
   - Improved error handling with better recovery mechanisms

7. **Installation Process Improvements**
   - **Purpose**: Enhance the reliability and robustness of the installation process
   - **Implementation**:
     - **Explicit Chroot Environment**: Properly mounts `/proc`, `/dev`, `/sys`, and `/run` for chroot operations
     - **Direct Service Configuration**: Services are directly enabled in the chroot environment
     - **Comprehensive Bootloader Installation**: Handles both UEFI and BIOS boot modes with proper fallback mechanisms
     - **Verification Steps**: Includes explicit verification steps for critical components
     - **Improved Welcome Message**: Creates a dynamic welcome message with IP address updates
     - **Standard Installation Sequence**: Follows standard Linux installation practices with logical ordering
   - **Benefits**:
     - More reliable installation process with fewer failures
     - Better error handling and recovery
     - Improved troubleshooting with detailed logs and verification reports
     - Consistent installation experience across different hardware
     - Clear welcome message with correct IP address for web interface access

8. **Critical Kernel Parameters**
   - **Purpose**: Ensure proper system boot and installation across different hardware environments
   - **Implementation**:
     - `persistenceos.install=1`: Triggers the installer services. This parameter is essential for the installation process to begin.
     - `console=tty0 console=hvc0`: Dual console configuration that supports both physical hardware and virtual machines.
     - `nomodeset`: Prevents graphics-related black screens by disabling kernel mode setting.
     - `debug rd.debug`: Enables verbose debugging output for troubleshooting (debug mode only).
   - **Benefits**:
     - Universal compatibility across physical hardware and virtual machines
     - Visible console output regardless of environment
     - Prevention of common graphics-related boot issues
     - Improved troubleshooting capabilities with debug parameters

### Phase 3: Web UI Foundation

6. **Web UI Management**
   - **ui-manager.sh**: Consolidated UI management script with multiple operations:
     - `prepare`: Prepares UI files for inclusion in OS image (replaces UI-Sources.sh)
     - `extract`: Extracts UI tarball during installation (replaces UI-Extract.sh)
     - `initialize`: Initializes web UI during first boot (replaces UI-Int.sh)
     - `reset`: Resets UI to default state (replaces UI-Reset.sh)
     - `check`: Checks for UI updates (replaces UI-Version-Check.sh)
     - `bundle`: Bundles UI assets for production (replaces bundle-ui-assets.sh)
   - **ui-migration.sh**: Script to facilitate migration from individual UI scripts

7. **Nginx Configuration**
   - **persistenceos.conf**: Main Nginx configuration
   - Proxy setup for FastAPI backend

### Phase 4: API Backend Development

8. **FastAPI Application Structure**
   - **app.py**: Main FastAPI application entry point
   - **persistenceos/package_init.py**: Package initialization file (renamed from __init__.py for OBS compatibility)
   - **persistenceos/main.py**: Core FastAPI application module
   - **persistenceos/routers/routers_init.py**: Routers package initialization (renamed from __init__.py for OBS compatibility)
   - **persistenceos/utils/utils_init.py**: Utils package initialization (renamed from __init__.py for OBS compatibility)
   - **persistenceos-api.service**: API service file

9. **API Routers**
   - **system_router.py**: System information endpoints
   - **vm_router.py**: VM management endpoints
   - **storage_router.py**: Storage management endpoints
   - **network_router.py**: Network configuration endpoints

### Phase 5: Web UI Frontend

10. **Core UI Files**
    - **login.html**: Login page
    - **index.html**: Main interface with hash-based navigation
    - **style.css**: Consolidated stylesheet
    - **components.css**: Component-specific styles for cards, buttons, forms, etc.
    - **loading-indicators.css**: Styles for loading spinners and overlays
    - **notifications.css**: Styles for error and success notifications
    - **variables.css**: CSS custom properties (variables) for consistent styling and theming
    - **normalize.css**: CSS reset file that normalizes styles across different browsers
    - **layout.css**: Layout-specific styles for grid, flexbox, and responsive design
    - **theme.css**: Theme-specific styles for light/dark mode and customization

11. **CSS Architecture**
    - **Purpose**: Provide a maintainable, scalable, and consistent styling system
    - **Implementation**:
      - **variables.css**: Foundation of the styling system with CSS custom properties
        - Color palette definitions for both light and dark themes
        - Typography settings (font families, sizes, weights)
        - Spacing and layout measurements
        - Component-specific variables
      - **normalize.css**: Cross-browser consistency baseline
        - Normalizes default styles across different browsers
        - Fixes common browser inconsistencies
        - Provides a clean foundation for custom styling
      - **style.css**: Main application styles built on the variables
      - **components.css**: Modular component styles
    - **Benefits**:
      - Consistent visual language throughout the application
      - Easy theming support (light/dark mode)
      - Simplified maintenance with centralized values
      - Better responsive design with standardized breakpoints
      - Reduced CSS duplication

12. **JavaScript Modules**
    - **auth.js**: Authentication functionality and token management
    - **api.js**: API communication with error handling and request queuing
    - **app.js**: Main application logic with routing and state management
    - **error-handlers.js**: Centralized error handling and notifications
    - **loading-indicators.js**: Loading state management and visual feedback
    - **theme-manager.js**: Theme switching between light and dark modes
    - **dashboard-widgets.js**: Dashboard widget rendering and data updates
    - **system-monitor.js**: Real-time system monitoring via WebSockets

13. **Error Handling System**
    - **Purpose**: Provide consistent error handling across the frontend
    - **Implementation**:
      - Centralized error handling module (error-handlers.js)
      - Standardized error notification system
      - Authentication error detection and handling
      - Network error handling with retry capabilities
      - Consistent error logging for debugging
      - Integration with all API calls
    - **Benefits**:
      - Improved user experience with clear error messages
      - Reduced code duplication across JavaScript files
      - Consistent error format and presentation
      - Better error recovery paths
      - Simplified debugging and troubleshooting

14. **Loading State Management**
    - **Purpose**: Provide visual feedback during asynchronous operations
    - **Implementation**:
      - Loading state management module (loading-indicators.js)
      - Button loading states with spinner animation
      - Section loading overlays for longer operations
      - Automatic loading state management for API calls
      - Consistent loading indicators across the UI
    - **Benefits**:
      - Improved user experience during API calls
      - Prevention of multiple submissions
      - Reduced perceived wait time
      - Contextual feedback for specific UI sections
      - Consistent visual language throughout the UI

### Phase 6: System Management Scripts

15. **System Management**
    - **system-manager.sh**: System management operations
    - **system-health.sh**: System health monitoring
    - **persistenceos-health-daemon.service**: Health checker service

16. **Storage Management**
    - **storage-manager.sh**: Storage management operations
    - **storage-snapshots.sh**: Snapshot management

17. **Network Management**
    - **network-manager.sh**: Network configuration
    - **update-motd.sh**: Updates the Message of the Day with system information
    - **persistenceos-network.service**: Consolidated network service that updates the MOTD with IP address, runs additional network configuration tasks, and configures the firewall

18. **Utility Scripts**
    - **fix-network.sh**: Network troubleshooting and repair (manual utility script)

19. **Health Check System**
    - **health-manager.sh**: Comprehensive health check management script with monitoring capabilities
    - **health-check-api.sh**: API health monitoring with endpoint verification
    - **health-check-system.sh**: System resource monitoring (CPU, memory, disk, services)
    - **health-check-storage.sh**: Storage health monitoring with SMART and filesystem checks
    - **health-check-network.sh**: Network connectivity monitoring and interface status
    - **health-check-vm.sh**: Virtual machine health monitoring and guest agent integration
    - **health-check.sh**: Utility script for system health diagnostics (non-essential for installation)
    - **health-fix.sh**: Utility script for automated system issue resolution (non-essential for installation)
    - **service-health.sh**: Utility script for service monitoring and management (non-essential for installation)
    - **vm-health.sh**: Utility script for VM health monitoring (non-essential for installation)
    - **persistenceos-health-monitoring.service**: Consolidated health monitoring service that runs system health checks, API health checks, and starts continuous monitoring

20. **Backup System**
    - **backup-manager.sh**: Comprehensive backup management script leveraging snapper and btrfs
    - **backup-system.sh**: System configuration backup using btrfs snapshots
    - **backup-vm.sh**: Virtual machine backup with libvirt and btrfs snapshots
    - **backup-storage.sh**: Storage configuration backup with snapper integration
    - **backup-restore.sh**: Restore from btrfs snapshots
    - **persistenceos-backup-system.service**: Consolidated backup service that handles snapper configuration, snapshot creation, and cleanup
    - **persistenceos-backup-system.timer**: Timer for scheduled backups

21. **Upgrade System**
    - **upgrade-manager.sh**: Comprehensive upgrade management script with check, upgrade, rollback, and status commands
    - **version-migration.sh**: Version-specific migration tasks with automatic script discovery
    - **upgrade-check.sh**: Update availability and compatibility verification with system requirements checking
    - **upgrade-rollback.sh**: Rollback to previous version if upgrade fails using btrfs snapshots
    - **persistenceos-upgrade.service**: System upgrade service for transactional updates
    - **persistenceos-upgrade-check.service**: Scheduled update check service
    - **persistenceos-upgrade-check.timer**: Timer for scheduled update checks
    - **upgrade_router.py**: API endpoints for system upgrades with progress monitoring
    - **migrations/**: Directory containing version-specific migration scripts

## Future Implementations

These components represent planned future enhancements to extend PersistenceOS functionality beyond the core implementation.

1. **Advanced Networking (SDN and Network Virtualization)**
   - **network-sdn-manager.sh**: Management of SDN controllers and configurations
   - **network-virtualization.sh**: Creation and management of virtual networks
   - **persistenceos-sdn-controller.service**: Service for running SDN controller
   - **persistenceos-network-virtualization.service**: Service for virtual network management
   - **network-topology.js**: Frontend visualization of network topology
   - **sdn_router.py**: API endpoints for SDN management
   - **Integration with**: Open vSwitch, OVN, and libvirt virtual networking

2. **Testing Framework**
   - **test-runner.sh**: Automated test execution script
   - **unit-tests/**: Directory containing unit tests for scripts
   - **api-tests/**: Directory containing API integration tests
   - **ui-tests/**: Directory containing UI tests
   - **test-report-generator.sh**: Creates test reports in HTML format

3. **Clustering and High Availability**
   - **cluster-manager.sh**: Management of multi-node clusters
   - **ha-manager.sh**: High availability configuration and monitoring
   - **node-sync.sh**: Synchronization between cluster nodes
   - **persistenceos-cluster.service**: Cluster management service
   - **persistenceos-ha-monitor.service**: High availability monitoring service
   - **cluster_router.py**: API endpoints for cluster management

4. **Enhanced Container Support**
   - **container-manager.sh**: Advanced container management
   - **container-network.sh**: Container-specific networking
   - **container-storage.sh**: Optimized storage for containers
   - **persistenceos-container.service**: Container management service
   - **container_router.py**: API endpoints for container management

## Kernel Parameters and Console Configuration

### Importance of Kernel Parameters

Kernel parameters play a critical role in the PersistenceOS boot and installation process. They control how the system initializes, where output is directed, and which services are triggered. Understanding these parameters is essential for troubleshooting installation issues and ensuring compatibility across different hardware environments.

1. **Installation Trigger Parameter**
   - **`persistenceos.install=1`**: This parameter is the primary trigger for the installation process. When present in the kernel command line, it signals to the system that it should run in installation mode.
   - **Implementation**: This parameter is detected by the installer services (`persistenceos-installer-environment.service` and `persistenceos-installer.service`) through their `ConditionKernelCommandLine=persistenceos.install=1` directive.
   - **Impact**: Without this parameter, the installer services will not start, resulting in a normal boot instead of installation.

2. **Console Configuration Parameters**
   - **`console=tty0`**: Directs console output to the primary display device for physical hardware.
   - **`console=hvc0`**: Directs console output to the hypervisor console for virtual machines.
   - **`console=ttyS0,115200n8`**: Directs console output to the serial console for virtual machines with serial console access.
   - **Implementation**: These parameters are included in the GRUB menu entries and in the `kernelcmdline` attribute of the PersistenceOS.kiwi file.
   - **Impact**: Using the wrong console parameter can result in a black screen during installation, as output is being sent to a console that isn't visible to the user.

3. **Graphics Compatibility Parameter**
   - **`nomodeset`**: Disables kernel mode setting, which can prevent graphics-related black screens on hardware with incompatible graphics drivers.
   - **Implementation**: This parameter is included in the GRUB menu entries for both regular and debug installation modes.
   - **Impact**: Without this parameter, some systems may experience black screens after the initial kernel load due to graphics driver incompatibilities.

4. **Debugging Parameters**
   - **`debug`**: Enables kernel debugging output.
   - **`rd.debug`**: Enables initramfs debugging output.
   - **Implementation**: These parameters are included in the failsafe GRUB menu entries.
   - **Impact**: These parameters provide verbose output that can help diagnose boot and installation issues.

### Dual Console Strategy

PersistenceOS implements a dual console strategy to ensure compatibility across both physical hardware and virtual machines:

1. **Universal Compatibility**
   - By including both `console=tty0` and `console=hvc0` in the kernel parameters, PersistenceOS ensures that console output is visible regardless of whether the system is running on physical hardware or in a virtual machine.
   - This approach eliminates the need for separate images or configurations for different environments.

2. **Fallback Mechanism**
   - If one console type isn't available (e.g., `tty0` in a VM without graphics), the system will automatically use the other console.
   - This provides a robust fallback mechanism that ensures users can always see boot and installation messages.

3. **VM-Specific Optimization**
   - For virtual machines with serial console access, the VM-specific GRUB menu entry includes `console=ttyS0,115200n8` for optimal compatibility with hypervisors that provide serial console access.

4. **Implementation in GRUB Menu Entries**
   - Regular Installation: `console=tty0 console=hvc0 nomodeset`
   - Debug Installation: `console=tty0 console=hvc0 nomodeset debug rd.debug`
   - VM-Specific Installation: `console=hvc0 console=ttyS0,115200n8`

This comprehensive approach to console configuration ensures that PersistenceOS provides a seamless installation experience across all supported environments, from physical servers to various virtualization platforms.

## Detailed Implementation Plan

### 1. common.sh (Foundation)

```bash
#!/bin/bash
#================
# FILE          : common.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Common functions for PersistenceOS scripts
#================

# Version
COMMON_VERSION="1.0.0"

# Define paths
PERSISTENCE_VERSION="6.1"
PERSISTENCE_ROOT="/usr/lib/persistence"
PERSISTENCE_SCRIPTS="${PERSISTENCE_ROOT}/scripts"
PERSISTENCE_SERVICES="${PERSISTENCE_ROOT}/services"
PERSISTENCE_BIN="${PERSISTENCE_ROOT}/bin"
PERSISTENCE_TOOLS="${PERSISTENCE_ROOT}/tools"
PERSISTENCE_WEB_UI="${PERSISTENCE_ROOT}/web-ui"
PERSISTENCE_API="${PERSISTENCE_ROOT}/api"
PERSISTENCE_VAR="/var/lib/persistence"
PERSISTENCE_LOG="/var/log/persistence"
PERSISTENCE_MARKER="/var/lib/persistenceos"
NGINX_CONF_DIR="/etc/nginx"

# Default log file
COMMON_LOG_FILE="${PERSISTENCE_LOG}/common.log"

# Logging function
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "${COMMON_LOG_FILE}")"

    # Log to file
    echo "[${timestamp}] [${level}] ${message}" >> "${COMMON_LOG_FILE}"

    # Log to console with colors
    case "${level}" in
        "INFO")
            echo -e "\033[0;34m[${level}]\033[0m ${message}"
            ;;
        "SUCCESS")
            echo -e "\033[0;32m[${level}]\033[0m ${message}"
            ;;
        "WARNING")
            echo -e "\033[0;33m[${level}]\033[0m ${message}"
            ;;
        "ERROR")
            echo -e "\033[0;31m[${level}]\033[0m ${message}"
            ;;
        "DEBUG")
            echo -e "\033[0;35m[${level}]\033[0m ${message}"
            ;;
        *)
            echo "[${level}] ${message}"
            ;;
    esac
}

# Check if running as root
check_root() {
    if [ "$(id -u)" -ne 0 ]; then
        log "ERROR" "This script must be run as root"
        return 1
    fi
    return 0
}

# Safe systemctl wrapper
safe_systemctl() {
    if command -v systemctl &> /dev/null; then
        systemctl "$@"
        return $?
    else
        log "WARNING" "systemctl not available, skipping: systemctl $*"
        return 0
    fi
}

# Check if running in build environment
is_build_environment() {
    # Check for common build environment indicators
    if [ -d "/image" ] || [ -f "/.kiwi-generation" ]; then
        return 0
    fi
    return 1
}

# Create directory if it doesn't exist
ensure_directory() {
    local dir="$1"
    if [ ! -d "${dir}" ]; then
        mkdir -p "${dir}"
        log "INFO" "Created directory: ${dir}"
    fi
}

# Check if a package is installed
is_package_installed() {
    local package="$1"
    if command -v rpm &> /dev/null; then
        if rpm -q "${package}" &> /dev/null; then
            return 0
        fi
    fi
    return 1
}

# Initialize logging
init_logging() {
    ensure_directory "$(dirname "${COMMON_LOG_FILE}")"
    log "INFO" "Initialized logging to ${COMMON_LOG_FILE}"
    log "INFO" "PersistenceOS common functions loaded (version ${COMMON_VERSION})"
}

# Initialize common functions
init_logging
```

### 2. nftables-setup.sh (Firewall Management)

```bash
#!/bin/bash
#================
# FILE          : nftables-setup.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Configure nftables firewall for PersistenceOS
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Source common functions
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
if [ -f "${SCRIPT_DIR}/common.sh" ]; then
    source "${SCRIPT_DIR}/common.sh"
else
    echo "Error: common.sh not found in ${SCRIPT_DIR}"
    exit 1
fi

# Define log file
LOG_FILE="${PERSISTENCE_LOG}/nftables-setup.log"
export COMMON_LOG_FILE="${LOG_FILE}"

# Check if running as root
if ! check_root; then
    log "ERROR" "This script must be run as root"
    exit 1
fi

# Create base nftables configuration
create_base_config() {
    log "INFO" "Creating base nftables configuration"

    # Create the base nftables configuration file
    cat > /etc/nftables.conf << EOF
#!/usr/sbin/nft -f

flush ruleset

table inet filter {
    chain input {
        type filter hook input priority 0; policy drop;

        # Allow established/related connections
        ct state established,related accept

        # Allow loopback
        iif lo accept

        # Allow ICMP and IGMP
        ip protocol icmp accept
        ip6 nexthdr icmpv6 accept

        # Allow SSH (port 22)
        tcp dport 22 accept

        # Allow Web UI (port 8080)
        tcp dport 8080 accept

        # Allow libvirt services
        tcp dport 16509 accept  # libvirt
        tcp dport 5900-5999 accept  # VNC

        # Drop invalid connections
        ct state invalid drop

        # Log and drop everything else
        log prefix "nftables input dropped: " limit rate 3/minute
        drop
    }

    chain forward {
        type filter hook forward priority 0; policy drop;

        # Allow established/related connections
        ct state established,related accept

        # Allow VM traffic
        iifname "virbr*" accept
        oifname "virbr*" accept

        # Drop invalid connections
        ct state invalid drop

        # Log and drop everything else
        log prefix "nftables forward dropped: " limit rate 3/minute
        drop
    }

    chain output {
        type filter hook output priority 0; policy accept;
    }
}

table ip nat {
    chain prerouting {
        type nat hook prerouting priority -100; policy accept;
    }

    chain postrouting {
        type nat hook postrouting priority 100; policy accept;

        # Masquerade VM traffic
        oifname "eth*" ip saddr *************/24 masquerade
    }
}
EOF

    # Make the file executable
    chmod +x /etc/nftables.conf

    log "SUCCESS" "Base nftables configuration created"
}

# Enable and start nftables service
enable_nftables_service() {
    log "INFO" "Enabling nftables service"

    # Enable the service
    safe_systemctl enable nftables

    # Start the service
    safe_systemctl start nftables

    log "SUCCESS" "Nftables service enabled and started"
}

# Main function
main() {
    log "INFO" "Starting nftables setup (version ${VERSION})"

    # Check if nftables package is installed
    if ! command -v nft &> /dev/null; then
        log "ERROR" "nftables package is not installed"
        exit 1
    fi

    # Create base configuration
    create_base_config

    # Enable and start the service
    enable_nftables_service

    log "SUCCESS" "Nftables setup completed successfully"
}

# Run main function
main
exit $?
```

### 3. install-packages.sh (Package Installation)

```bash
#!/bin/bash
#================
# FILE          : install-packages.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Install required packages for PersistenceOS
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Source common functions
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
if [ -f "${SCRIPT_DIR}/common.sh" ]; then
    source "${SCRIPT_DIR}/common.sh"
else
    echo "Error: common.sh not found in ${SCRIPT_DIR}"
    exit 1
fi

# Define log file
LOG_FILE="${PERSISTENCE_LOG}/install-packages.log"
export COMMON_LOG_FILE="${LOG_FILE}"

# Check if running as root
if ! check_root; then
    log "ERROR" "This script must be run as root"
    exit 1
fi

# Function to install packages using transactional-update
install_packages() {
    local packages="$1"
    log "INFO" "Installing packages: ${packages}"

    # Use transactional-update to install packages
    transactional-update --no-selfupdate pkg install $packages

    if [ $? -eq 0 ]; then
        log "SUCCESS" "Packages installed successfully"
        return 0
    else
        log "ERROR" "Failed to install packages"
        return 1
    fi
}

# Main function
main() {
    log "INFO" "Starting PersistenceOS package installation (version ${VERSION})"

    # Web UI packages
    log "INFO" "Installing Web UI packages..."
    install_packages "nginx python311-base python311-fastapi python311-uvicorn python311-psutil python311-pydantic python311-typing-extensions"

    # Virtualization packages
    log "INFO" "Installing Virtualization packages..."
    install_packages "libvirt-daemon libvirt-client qemu-kvm qemu-tools libvirt-daemon-driver-qemu libvirt-daemon-driver-storage-core libvirt-daemon-driver-network bridge-utils dnsmasq"

    # Storage packages
    log "INFO" "Installing Storage packages..."
    install_packages "btrfsprogs xfsprogs lvm2 mdadm snapper libsnapper7 e2fsprogs cryptsetup"

    # Network packages
    log "INFO" "Installing Network packages..."
    install_packages "NetworkManager iproute2 iputils nftables"

    # System packages
    log "INFO" "Installing System packages..."
    install_packages "systemd tukit tukid zypper vim-small less bash-completion sudo shadow procps"

    # Bootloader packages
    log "INFO" "Installing Bootloader packages..."
    install_packages "grub2 grub2-i386-pc grub2-x86_64-efi efibootmgr grub2-branding-opensuse perl-Bootloader"

    # Container packages
    log "INFO" "Installing Container packages..."
    install_packages "podman slirp4netns fuse-overlayfs"

    # Additional utilities
    log "INFO" "Installing Additional utilities..."
    install_packages "smartmontools nfs-kernel-server samba parted curl wget tar gzip bzip2 xz unzip grep sed awk findutils diffutils patch gawk timezone ca-certificates openssl"

    log "SUCCESS" "PersistenceOS package installation completed successfully"
    log "INFO" "A system reboot is required to complete the installation"
}

# Run main function
main
exit $?
```

### 4. images.sh (Post-Installation Configuration)

```bash
#!/bin/bash
#================
# FILE          : images.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Post-installation script for PersistenceOS
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Global variables
PERSISTENCE_VERSION="6.1.0"
LOG_FILE="/var/log/persistence-images.log"

# Logging function
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "${LOG_FILE}")"

    # Log to file
    echo "[${timestamp}] [${level}] ${message}" >> "${LOG_FILE}"

    # Log to console with colors
    case "${level}" in
        "INFO")
            echo -e "\033[0;34m[${level}]\033[0m ${message}"
            ;;
        "SUCCESS")
            echo -e "\033[0;32m[${level}]\033[0m ${message}"
            ;;
        "WARNING")
            echo -e "\033[0;33m[${level}]\033[0m ${message}"
            ;;
        "ERROR")
            echo -e "\033[0;31m[${level}]\033[0m ${message}"
            ;;
        *)
            echo "[${level}] ${message}"
            ;;
    esac
}

# Check if running in build environment
is_build_environment() {
    # Check for common build environment indicators
    if [ -d "/image" ] || [ -f "/.kiwi-generation" ]; then
        return 0
    fi
    return 1
}

# Configure storage pools
configure_storage_pools() {
    log "INFO" "Configuring storage pools"

    # Check if we're in a build environment
    if is_build_environment; then
        log "INFO" "Detected build environment, creating placeholder storage configuration"

        # Create directory for storage pools
        mkdir -p /var/lib/persistence/storage-pools

        # Create a default storage pool configuration
        cat > /var/lib/persistence/storage-pools/default.conf << EOF
# Default storage pool configuration
NAME=default
TYPE=dir
PATH=/var/lib/libvirt/images
AUTOSTART=yes
EOF

        log "SUCCESS" "Created placeholder storage configuration"
    else
        # Configure libvirt storage pools
        if command -v virsh &> /dev/null; then
            # Check if default pool exists
            if ! virsh pool-info default &> /dev/null; then
                # Create default storage pool
                virsh pool-define-as --name default --type dir --target /var/lib/libvirt/images
                virsh pool-build default
                virsh pool-start default
                virsh pool-autostart default
                log "SUCCESS" "Created default storage pool"
            else
                log "INFO" "Default storage pool already exists"
            fi
        else
            log "WARNING" "virsh command not available, skipping storage pool configuration"
        fi
    fi
}

# Configure system settings
configure_system_settings() {
    log "INFO" "Configuring system settings"

    # Check if we're in a build environment
    if is_build_environment; then
        log "INFO" "Detected build environment, using fallback methods for system settings"

        # Set timezone to UTC using fallback method
        ln -sf /usr/share/zoneinfo/UTC /etc/localtime
        log "SUCCESS" "Timezone set to UTC (fallback method)"

        # Create a record of NTP configuration for first boot
        mkdir -p /var/lib/persistence/config
        echo "NTP=true" > /var/lib/persistence/config/ntp-config.txt
        log "INFO" "Created NTP configuration for first boot"
    else
        # Set timezone to UTC
        if command -v timedatectl &> /dev/null; then
            timedatectl set-timezone UTC
            log "SUCCESS" "Timezone set to UTC"
        else
            ln -sf /usr/share/zoneinfo/UTC /etc/localtime
            log "SUCCESS" "Timezone set to UTC (fallback method)"
        fi

        # Enable NTP
        if command -v timedatectl &> /dev/null; then
            timedatectl set-ntp true
            log "SUCCESS" "NTP enabled"
        else
            log "WARNING" "timedatectl not available, skipping NTP configuration"
        fi
    fi
}

# Configure system documentation
configure_system_documentation() {
    log "INFO" "Configuring system documentation"

    # Create documentation directory
    mkdir -p /usr/share/doc/persistenceos

    # Create README file
    cat > /usr/share/doc/persistenceos/README.md << EOF
# PersistenceOS

Version: ${PERSISTENCE_VERSION}

PersistenceOS is a specialized hypervisor and NAS operating system based on SUSE Micro Leap 6.1, designed to provide robust virtualization capabilities with advanced storage management and snapshot functionality.

## Features

- Dual filesystem strategy: btrfs for system and snapshots, XFS for VM storage
- Modern web UI for system management
- Advanced snapshot capabilities
- Robust virtualization with KVM/QEMU
- Transactional updates for system stability

## Management

The web UI is accessible at http://[your-server-ip]:8080

Default login credentials:
- Username: root
- Password: linux

## Documentation

For more information, visit the PersistenceOS documentation at:
https://persistence-os.org/docs
EOF

    log "SUCCESS" "System documentation configured"
}

# Main function
main() {
    log "INFO" "Starting PersistenceOS post-installation (version ${VERSION})"

    # Configure storage pools
    configure_storage_pools

    # Configure system settings
    configure_system_settings

    # Configure system documentation
    configure_system_documentation

    log "SUCCESS" "PersistenceOS post-installation completed successfully"
}

# Run main function
main
exit $?
```

### 5. ui-manager.sh (Consolidated UI Management)

```bash
#!/bin/bash
#================
# FILE          : ui-manager.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Consolidated UI management script for PersistenceOS
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Source common functions
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
if [ -f "${SCRIPT_DIR}/util-common.sh" ]; then
    source "${SCRIPT_DIR}/util-common.sh"
else
    echo "Error: util-common.sh not found in ${SCRIPT_DIR}"
    exit 1
fi

# Define log file
LOG_FILE="${PERSISTENCE_LOG}/ui-manager.log"
export COMMON_LOG_FILE="${LOG_FILE}"

# Check if running as root
if ! check_root; then
    log "ERROR" "This script must be run as root"
    exit 1
fi

# Define UI paths
UI_TARBALL="/image/persistenceos-ui.tar.gz"
UI_DEST_DIR="${PERSISTENCE_WEB_UI}"
UI_SHARE_DIR="/usr/share/persistenceos-ui"
UI_VERSION_FILE="${UI_SHARE_DIR}/version.json"
UI_MARKER_FILE="${PERSISTENCE_MARKER}/ui-setup-complete"

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS] COMMAND"
    echo ""
    echo "Commands:"
    echo "  prepare    Prepare UI files for inclusion in OS image (former UI-Sources.sh)"
    echo "  extract    Extract UI tarball during installation (former UI-Extract.sh)"
    echo "  initialize Initialize web UI during first boot (former UI-Int.sh)"
    echo "  reset      Reset UI to default state (former UI-Reset.sh)"
    echo "  check      Check for UI updates (former UI-Version-Check.sh)"
    echo "  bundle     Bundle UI assets for production (former bundle-ui-assets.sh)"
    echo ""
    echo "Options:"
    echo "  --help, -h     Show this help message"
    echo "  --version, -v  Show version information"
    echo "  --force, -f    Force operation even if already completed"
    echo "  --verbose      Show detailed output"
    echo ""
    echo "Examples:"
    echo "  $0 prepare     Prepare UI files for inclusion in OS image"
    echo "  $0 initialize  Initialize web UI during first boot"
    echo "  $0 reset       Reset UI to default state"
}

# Main function with command-based structure
main() {
    # Default values
    FORCE_OPERATION="false"
    VERBOSE="false"

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --help|-h)
                show_usage
                exit 0
                ;;
            --version|-v)
                echo "ui-manager.sh version ${VERSION}"
                echo "Part of PersistenceOS ${PERSISTENCE_VERSION}"
                exit 0
                ;;
            --force|-f)
                FORCE_OPERATION="true"
                shift
                ;;
            --verbose)
                VERBOSE="true"
                shift
                ;;
            prepare|extract|initialize|reset|check|bundle)
                COMMAND="$1"
                shift
                ;;
            *)
                log "ERROR" "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # Check if command is provided
    if [ -z "${COMMAND}" ]; then
        log "ERROR" "No command specified"
        show_usage
        exit 1
    fi

    log "INFO" "Starting UI manager (version ${VERSION}, command: ${COMMAND})"

    # Execute command based on input
    case "${COMMAND}" in
        prepare)
            # Implementation of UI-Sources.sh functionality
            log "INFO" "Preparing UI files for inclusion in OS image"
            # ... implementation details ...
            ;;
        extract)
            # Implementation of UI-Extract.sh functionality
            log "INFO" "Extracting UI tarball"
            # ... implementation details ...
            ;;
        initialize)
            # Implementation of UI-Int.sh functionality
            log "INFO" "Initializing web UI"
            # ... implementation details ...
            ;;
        reset)
            # Implementation of UI-Reset.sh functionality
            log "INFO" "Resetting UI to default state"
            # ... implementation details ...
            ;;
        check)
            # Implementation of UI-Version-Check.sh functionality
            log "INFO" "Checking for UI updates"
            # ... implementation details ...
            ;;
        bundle)
            # Implementation of bundle-ui-assets.sh functionality
            log "INFO" "Bundling UI assets for production"
            # ... implementation details ...
            ;;
        *)
            log "ERROR" "Unknown command: ${COMMAND}"
            show_usage
            exit 1
            ;;
    esac

    log "SUCCESS" "UI manager completed successfully"
    return 0
}

# Run main function
main "$@"
exit $?
```

### 6. persistenceos-ui-setup.service (UI Setup Service)

```ini
[Unit]
Description=PersistenceOS UI Setup Service
After=network.target nginx.service
ConditionPathExists=!/var/lib/persistenceos/ui-setup-complete

[Service]
Type=oneshot
ExecStart=/usr/lib/persistence/scripts/ui-manager.sh initialize
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
```

### 7. Service Naming Conventions

All SystemD service files in PersistenceOS **MUST** follow these naming conventions:

1. **Core Services**: `persistenceos-{name}.service`
   - Example: `persistenceos-api.service`

2. **Setup Services**: `persistenceos-{name}-setup.service`
   - Example: `persistenceos-ui-setup.service`

3. **Installation Services**: `persistenceos-{name}-install.service`
   - Example: `persistenceos-installer.service`

4. **Daemon Services**: `persistenceos-{name}-daemon.service`
   - Example: `persistenceos-health-daemon.service`

5. **Debug Services**: `persistenceos-{name}-debug.service`
   - Example: `persistenceos-api-debug.service`

Services that need to be renamed to follow this convention:
- `persistence-nftables-setup.service` → `persistenceos-nftables-setup.service`
- `persistence-services-init.service` → `persistenceos-services-init.service`
- `persistence-firewall-setup.service` → `persistenceos-firewall-setup.service`
- `persistence-post-install.service` → `persistenceos-post-install.service`
- `persistence-webserver.service` → `persistenceos-webserver.service`
- `health-checker-daemon.service` → `persistenceos-health-daemon.service`

### 8. Service Template System

To address redundant configuration in service files and ensure consistency, PersistenceOS implements a service template system. This system generates standardized service files from common templates, reducing maintenance overhead and preventing inconsistencies.

#### 1. Service Template Generator

The `service-generator.sh` script is the core of the service template system:

```bash
#!/bin/bash
# Usage: service-generator.sh [OPTIONS] SERVICE_TYPE SERVICE_NAME EXEC_COMMAND
# Example: service-generator.sh core api "/usr/bin/python3.11 -m uvicorn main:app --host 127.0.0.1 --port 8000"
```

#### 2. Service Types and Templates

The system defines standard templates for different service types:

1. **Core Services** (`core`):
   ```ini
   [Unit]
   Description=PersistenceOS {Name} Service
   After=network.target

   [Service]
   Type=simple
   ExecStart={exec_command}
   Restart=on-failure
   RestartSec=10

   [Install]
   WantedBy=multi-user.target
   ```

2. **Setup Services** (`setup`):
   ```ini
   [Unit]
   Description=PersistenceOS {Name} Setup Service
   After=network.target
   ConditionPathExists=!/var/lib/persistenceos/{name}-setup-complete

   [Service]
   Type=oneshot
   ExecStart={exec_command}
   RemainAfterExit=yes

   [Install]
   WantedBy=multi-user.target
   ```

3. **Installation Services** (`install`):
   ```ini
   [Unit]
   Description=PersistenceOS {Name} Installation Service
   DefaultDependencies=no
   After=systemd-vconsole-setup.service
   Before=<EMAIL>
   ConditionKernelCommandLine=persistenceos.install

   [Service]
   Type=oneshot
   ExecStart={exec_command}
   RemainAfterExit=yes

   [Install]
   WantedBy=multi-user.target
   ```

4. **Daemon Services** (`daemon`):
   ```ini
   [Unit]
   Description=PersistenceOS {Name} Daemon Service
   After=network.target

   [Service]
   Type=simple
   ExecStart={exec_command}
   Restart=on-failure
   RestartSec=10

   [Install]
   WantedBy=multi-user.target
   ```

5. **Debug Services** (`debug`):
   ```ini
   [Unit]
   Description=PersistenceOS {Name} Debug Service
   After=network.target

   [Service]
   Type=oneshot
   ExecStart={exec_command}
   RemainAfterExit=yes

   [Install]
   WantedBy=multi-user.target
   ```

#### 3. Service Configuration Options

The service generator supports various configuration options:

- `--after`: Services to start after (comma-separated)
- `--requires`: Services required (comma-separated)
- `--wants`: Services wanted (comma-separated)
- `--type`: Service type (simple, oneshot, forking, etc.)
- `--user`: User to run as (default: root)
- `--restart`: Restart policy (default depends on service type)

#### 4. Service Validation

The `validate-services.sh` script ensures all service files comply with the defined standards:

- Verifies naming conventions
- Checks for required sections and options
- Validates dependencies
- Ensures consistent formatting
- Reports non-compliant services

#### 5. Service Relationship Management

Service relationships are defined in a centralized configuration file (`service-dependencies.json`):

```json
{
  "persistenceos-api": {
    "after": ["network.target", "persistenceos-services-init.service"],
    "requires": [],
    "wants": []
  },
  "persistenceos-ui-setup": {
    "after": ["network.target", "nginx.service"],
    "requires": [],
    "wants": []
  }
}
```

#### 6. Benefits of Service Templates

- **Consistency**: All services follow the same structure and conventions
- **Reduced Duplication**: Common configurations defined once
- **Easier Maintenance**: Changes to service behavior can be made in templates
- **Validation**: Automated checks ensure compliance with standards
- **Documentation**: Templates serve as documentation for service types
- **Centralized Dependencies**: Service relationships managed in one place

### 9. Script Consolidation Strategy

To reduce script proliferation and improve maintainability, PersistenceOS adopts a script consolidation strategy. This approach reduces the number of individual scripts by combining related functionality into consolidated manager scripts with command-based interfaces.

#### 1. UI Management Consolidation

The `ui-manager.sh` script consolidates all UI-related functionality:

```
UI-Sources.sh         → ui-manager.sh prepare
UI-Extract.sh         → ui-manager.sh extract
UI-Int.sh             → ui-manager.sh initialize
UI-Reset.sh           → ui-manager.sh reset
UI-Version-Check.sh   → ui-manager.sh check
bundle-ui-assets.sh   → ui-manager.sh bundle
```

#### 2. System Management Consolidation

The `system-manager.sh` script consolidates system management functionality:

```
persistence-system.sh → system-manager.sh status
system-health.sh      → system-manager.sh health
update-system.sh      → system-manager.sh update
system-backup.sh      → system-manager.sh backup
system-restore.sh     → system-manager.sh restore
```

#### 3. Storage Management Consolidation

The `storage-manager.sh` script consolidates storage management functionality:

```
persistence-storage.sh  → storage-manager.sh status
persistence-snapshots.sh → storage-manager.sh snapshot
storage-pools.sh        → storage-manager.sh pools
storage-volumes.sh      → storage-manager.sh volumes
```

#### 4. Network Management Consolidation

The `network-manager.sh` script consolidates network management functionality:

```
network-config.sh     → network-manager.sh config
network-firewall.sh   → network-manager.sh firewall
```

Note: `fix-network.sh` is now maintained as a separate utility script for manual network troubleshooting rather than being consolidated into network-manager.sh.

#### 5. Consolidated Script Structure

All consolidated scripts follow a consistent structure:

1. **Command-Based Interface**: Each script accepts a command as the first argument
2. **Help and Version Information**: All scripts provide `--help` and `--version` options
3. **Consistent Error Handling**: Standardized error handling and logging
4. **Shared Common Functions**: All scripts use common.sh for shared functionality
5. **Backward Compatibility**: Wrapper scripts or symlinks for backward compatibility

#### 6. Migration Strategy

The migration to consolidated scripts will be phased:

1. **Phase 1**: Create consolidated scripts with full functionality
2. **Phase 2**: Create wrapper scripts for backward compatibility
3. **Phase 3**: Update service files to use consolidated scripts
4. **Phase 4**: Deprecate individual scripts
5. **Phase 5**: Remove individual scripts and wrappers

#### 7. Benefits of Consolidation

- **Reduced Code Duplication**: Common functionality shared across commands
- **Consistent Interface**: Standardized command structure and options
- **Improved Maintainability**: Fewer files to maintain and update
- **Better Documentation**: Centralized help and documentation
- **Simplified Dependency Management**: Fewer interdependencies between scripts

### 10. Hybrid Approach: Python API Layer with MicroOS Shell Script Integration

PersistenceOS adopts a hybrid approach that leverages the strengths of both Python and shell scripts. This strategy uses Python for the Web UI API layer while maintaining shell scripts for direct integration with MicroOS native packages installed via KIWI. This approach ensures we maintain compatibility with the MicroOS ecosystem while providing a modern, maintainable API that works seamlessly with our existing direct HTML/CSS frontend.

#### 1. Architecture Overview

The hybrid architecture consists of three main layers:

1. **Frontend Layer**: Direct HTML, CSS, and JavaScript web interface (preserving our existing UI design)
2. **API Layer**: Python-based FastAPI endpoints that handle requests from the frontend
3. **System Integration Layer**: Shell scripts that directly leverage MicroOS packages installed via KIWI

This approach provides several advantages:
- **Preserves Existing UI**: Maintains full compatibility with our direct HTML/CSS frontend design
- **Direct Access to MicroOS Packages**: Shell scripts directly use packages installed via KIWI configuration
- **Preservation of MicroOS Features**: Maintains compatibility with transactional update system and other MicroOS-specific features
- **Clean API Layer**: Python provides a structured, RESTful API for the web UI
- **Separation of Concerns**: Each layer has a clear responsibility and focus

#### 2. Python API Layer Implementation with Shell Script Integration

The Python API layer serves as a bridge between the web UI and the shell scripts that leverage MicroOS packages installed via KIWI. This implementation uses a standardized approach for calling shell scripts and processing their output:

```python
from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import HTTPBasic, HTTPBasicCredentials
import subprocess
import logging
import json
from typing import List, Dict, Any, Optional

# Import shell integration utility
from utils.shell_integration import ShellScriptRunner
from utils.auth import get_current_user

# Configure logging
logger = logging.getLogger("persistenceos-api.storage")

# Create router
router = APIRouter(prefix="/api/storage", tags=["storage"])

# Create shell script runner for storage-manager.sh
# This script directly uses MicroOS packages installed via KIWI (btrfsprogs, xfsprogs, lvm2)
storage_runner = ShellScriptRunner('/usr/lib/persistence/bin/storage-manager.sh')

@router.get("/pools", response_model=List[Dict[str, Any]])
def get_storage_pools(current_user: dict = Depends(get_current_user)) -> List[Dict[str, Any]]:
    """
    Get all storage pools using storage-manager.sh which leverages
    MicroOS packages (btrfsprogs, xfsprogs, lvm2) installed via KIWI.
    """
    try:
        logger.info("Getting storage pools using storage-manager.sh")

        # Call the shell script that directly uses MicroOS packages installed via KIWI
        result = storage_runner.run_command('list-pools')

        if result.returncode != 0:
            logger.error(f"Failed to get storage pools: {result.stderr}")
            raise HTTPException(status_code=500, detail=f"Failed to get storage pools: {result.stderr}")

        # Parse the pipe-delimited output into structured data
        pools = []
        for line in result.stdout.strip().split('\n'):
            if line and not line.startswith('#'):
                parts = line.split('|')
                if len(parts) >= 4:
                    pool = {
                        "name": parts[0].strip(),
                        "type": parts[1].strip(),
                        "status": parts[2].strip(),
                        "capacity": parts[3].strip(),
                        "usage_percent": int(parts[4].strip().rstrip('%')) if len(parts) > 4 else 0
                    }
                    logger.debug(f"Parsed pool: {pool}")
                    pools.append(pool)

        logger.info(f"Found {len(pools)} storage pools")
        return pools
    except Exception as e:
        logger.exception(f"Error getting storage pools: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get storage pools: {str(e)}")

@router.post("/pools", status_code=201)
def create_storage_pool(
    pool: Dict[str, Any],
    current_user: dict = Depends(get_current_user)
) -> Dict[str, str]:
    """
    Create a new storage pool using storage-manager.sh which leverages
    MicroOS packages (btrfsprogs, xfsprogs, lvm2) installed via KIWI.
    """
    try:
        name = pool.get("name")
        pool_type = pool.get("type")
        device = pool.get("device")

        if not all([name, pool_type, device]):
            raise HTTPException(status_code=400, detail="Missing required parameters")

        logger.info(f"Creating {pool_type} pool '{name}' on device {device}")

        # Call the shell script that directly uses MicroOS packages installed via KIWI
        result = storage_runner.run_command('create-pool', name, pool_type, device)

        if result.returncode != 0:
            logger.error(f"Failed to create pool: {result.stderr}")
            raise HTTPException(status_code=500, detail=f"Failed to create pool: {result.stderr}")

        logger.info(f"Successfully created {pool_type} pool '{name}'")
        return {"message": f"Storage pool '{name}' created successfully"}
    except Exception as e:
        logger.exception(f"Error creating storage pool: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create storage pool: {str(e)}")
```

This implementation demonstrates how the Python API layer calls shell scripts that directly leverage MicroOS packages installed via KIWI. The `ShellScriptRunner` utility provides a standardized interface for calling shell scripts and processing their output, with proper error handling and logging.

#### 3. Shell Script Integration with MicroOS Packages from KIWI

Shell scripts provide direct access to MicroOS packages installed via KIWI configuration. These scripts are designed to leverage the specific packages defined in the PersistenceOS.kiwi file, ensuring compatibility with the MicroOS ecosystem:

```bash
#!/bin/bash
# storage-manager.sh - Directly leverage MicroOS packages installed via KIWI

# Source common functions and environment
source /usr/lib/persistence/scripts/common.sh

# Define log file
LOG_FILE="/var/log/persistence/storage-manager.log"

# Log function for consistent output
log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Get command and arguments
command="$1"
shift

# Execute command using MicroOS packages installed via KIWI
case "$command" in
    list-pools)
        # Use btrfsprogs package installed via KIWI
        log_info "Listing storage pools using btrfs tools from MicroOS"
        btrfs filesystem show | grep -v "^$" | awk -F: '{print $1 "|btrfs|healthy|" $2 "|30%"}'

        # Use lvm2 package installed via KIWI
        log_info "Listing LVM pools using lvm2 tools from MicroOS"
        lvs --noheadings -o vg_name,vg_attr,vg_size,vg_free | awk '{print $1 "|lvm|healthy|" $3 "|" int(($3-$4)/$3*100) "%"}'
        ;;

    create-pool)
        # Implementation using MicroOS packages installed via KIWI
        pool_name="$1"
        pool_type="$2"
        device="$3"

        log_info "Creating $pool_type pool '$pool_name' on device $device"

        case "$pool_type" in
            btrfs)
                # Use btrfsprogs package installed via KIWI
                log_info "Using mkfs.btrfs from btrfsprogs package"
                mkfs.btrfs -L "$pool_name" "$device"
                ;;
            xfs)
                # Use xfsprogs package installed via KIWI
                log_info "Using mkfs.xfs from xfsprogs package"
                mkfs.xfs -L "$pool_name" "$device"
                ;;
            lvm)
                # Use lvm2 package installed via KIWI
                log_info "Using LVM tools from lvm2 package"
                pvcreate "$device"
                vgcreate "$pool_name" "$device"
                ;;
            *)
                log_error "Unsupported pool type: $pool_type"
                echo "Unsupported pool type: $pool_type"
                exit 1
                ;;
        esac
        ;;
    # Other commands...
esac
```

This approach ensures that the shell scripts directly use the MicroOS packages installed via KIWI, maintaining compatibility with the transactional update system and other MicroOS-specific features.

#### 4. Integration Points by Section with MicroOS Packages from KIWI

Each section of the web UI integrates with specific MicroOS packages installed via KIWI configuration. This table shows the complete integration chain from frontend to MicroOS packages:

1. **System Overview**:
   - **Frontend Component**: System dashboard cards and metrics
   - **Python Router**: `system_router.py` - Handles API requests and transforms data
   - **Shell Script**: `system-manager.sh` - Directly accesses MicroOS packages
   - **MicroOS Packages from KIWI**:
     - `systemd` - For service management
     - `procps` - For process information
     - `util-linux` - For system utilities
     - `lm_sensors` - For hardware monitoring
   - **Example Operations**:
     - Get CPU usage using `top` from procps
     - Manage services using `systemctl` from systemd
     - Get disk usage using `df` from util-linux
     - Monitor temperatures using `sensors` from lm_sensors

2. **Virtualization**:
   - **Frontend Component**: VM management interface
   - **Python Router**: `vm_router.py` - Handles API requests and transforms data
   - **Shell Script**: `system-manager.sh` - Directly accesses MicroOS packages
   - **MicroOS Packages from KIWI**:
     - `libvirt-daemon` - Core virtualization service
     - `qemu-kvm` - VM hypervisor
     - `libvirt-client` - Command-line tools
     - `dnsmasq` - Network services for VMs
   - **Example Operations**:
     - List VMs using `virsh list` from libvirt-client
     - Start/stop VMs using `virsh` commands
     - Create VMs using `virt-install`
     - Configure VM networking using dnsmasq

3. **Storage**:
   - **Frontend Component**: Storage pool and volume management
   - **Python Router**: `storage_router.py` - Handles API requests and transforms data
   - **Shell Script**: `storage-manager.sh` - Directly accesses MicroOS packages
   - **MicroOS Packages from KIWI**:
     - `btrfsprogs` - For btrfs filesystem operations
     - `xfsprogs` - For XFS filesystem operations
     - `lvm2` - For logical volume management
     - `e2fsprogs` - For ext4 filesystem operations
   - **Example Operations**:
     - Create btrfs filesystem using `mkfs.btrfs`
     - Manage LVM volumes using `lvcreate`, `lvs`
     - Format XFS volumes using `mkfs.xfs`
     - Check filesystem health using filesystem-specific tools

4. **Snapshots**:
   - **Frontend Component**: Snapshot creation and management
   - **Python Router**: `snapshots_router.py` - Handles API requests and transforms data
   - **Shell Script**: `storage-snapshots.sh` - Directly accesses MicroOS packages
   - **MicroOS Packages from KIWI**:
     - `snapper` - For btrfs snapshot management
     - `btrfsprogs` - For btrfs operations
     - `libvirt-client` - For VM snapshot operations
   - **Example Operations**:
     - Create system snapshots using `snapper`
     - Create btrfs snapshots using `btrfs subvolume snapshot`
     - Create VM snapshots using `virsh snapshot-create`
     - List snapshots using package-specific commands

5. **Network**:
   - **Frontend Component**: Network configuration interface
   - **Python Router**: `network_router.py` - Handles API requests and transforms data
   - **Shell Script**: `network-manager.sh` - Directly accesses MicroOS packages
   - **MicroOS Packages from KIWI**:
     - `NetworkManager` - For network configuration
     - `iproute2` - For network interface management
     - `nftables` - For firewall configuration
     - `bridge-utils` - For network bridging
   - **Example Operations**:
     - Configure interfaces using `nmcli` from NetworkManager
     - View network status using `ip` from iproute2
     - Configure firewall using `nft` from nftables
     - Set up bridges using `brctl` from bridge-utils

6. **Settings**:
   - **Frontend Component**: System settings and updates
   - **Python Router**: `settings_router.py` - Handles API requests and transforms data
   - **Shell Script**: `system-manager.sh` - Directly accesses MicroOS packages
   - **MicroOS Packages from KIWI**:
     - `transactional-update` - For system updates
     - `systemd` - For service management
     - `supportutils` - For system diagnostics
     - `openssl` - For security operations
   - **Example Operations**:
     - Update system using `transactional-update`
     - Manage services using `systemctl`
     - Generate system reports using `supportconfig`
     - Manage certificates using `openssl`

#### 5. Data Flow Architecture

The data flow in this hybrid architecture follows this pattern:

1. **User Interaction**: User interacts with the web UI
2. **API Request**: Frontend makes a request to the appropriate API endpoint
3. **Python Processing**: Python router handles the request and calls the appropriate shell script
4. **Shell Script Execution**: Shell script executes using MicroOS native packages
5. **Data Transformation**: Python transforms the shell script output into structured JSON
6. **Response**: API returns the structured data to the frontend
7. **UI Update**: Frontend updates the UI with the received data

#### 6. Benefits of the Hybrid Approach with MicroOS Packages from KIWI

1. **Direct Access to MicroOS Packages Installed via KIWI**:
   - Shell scripts directly use MicroOS packages defined in PersistenceOS.kiwi
   - No need for additional package installation or compatibility layers
   - Leverages the exact same tools and utilities that MicroOS provides
   - Ensures consistent behavior with standard MicroOS operations
   - Examples:
     - Using `btrfs` commands directly from the btrfsprogs package
     - Using `virsh` commands directly from the libvirt-client package
     - Using `systemctl` directly from the systemd package

2. **Preservation of MicroOS Features**:
   - Maintains full compatibility with transactional update system
   - Preserves immutable system design principles
   - Works seamlessly with MicroOS snapshot and rollback capabilities
   - Respects MicroOS security model and constraints
   - Examples:
     - Using `transactional-update` for package management
     - Respecting read-only filesystem constraints
     - Working with MicroOS boot environment

3. **Modern API Design with Python**:
   - Clean, RESTful API endpoints using FastAPI
   - Structured JSON data responses for frontend consumption
   - Comprehensive error handling and logging
   - Automatic API documentation with Swagger UI
   - Type validation with Pydantic models
   - Examples:
     - Transforming pipe-delimited shell output to structured JSON
     - Providing consistent error responses
     - Implementing proper HTTP status codes

4. **Clear Separation of Concerns**:
   - **Frontend Layer**: Pure HTML, CSS, and JavaScript for user interface
   - **API Layer**: Python for request handling and data transformation
   - **System Integration Layer**: Shell scripts for direct MicroOS package access
   - Each layer has a well-defined responsibility and interface
   - Examples:
     - Frontend makes API requests to Python endpoints
     - Python calls shell scripts and transforms their output
     - Shell scripts directly use MicroOS packages

5. **Enhanced Maintainability and Extensibility**:
   - Standardized interfaces between components
   - Consistent error handling and logging across layers
   - Modular design allows for component-level updates
   - Clear documentation of integration points
   - Examples:
     - Adding new API endpoints without changing shell scripts
     - Enhancing shell scripts without modifying Python code
     - Updating the UI without affecting backend components

6. **Optimal Use of Technologies**:
   - Shell scripts for what they're best at (system integration)
   - Python for what it's best at (API development, data processing)
   - JavaScript for what it's best at (UI rendering, user interaction)
   - Each technology applied to its strengths
   - Examples:
     - Shell scripts for direct command execution
     - Python for structured data handling
     - JavaScript for dynamic UI updates

7. **Reduced Development Risk**:
   - Builds on proven components rather than reinventing everything
   - Allows for incremental improvement and testing
   - Provides fallback options if issues arise
   - Preserves investment in existing shell scripts
   - Examples:
     - Gradual migration from legacy scripts to Python API
     - Testing individual components independently
     - Rolling back changes at the component level

#### 7. Implementation Strategy for the Hybrid Approach

The implementation of this hybrid approach with MicroOS packages from KIWI follows these detailed steps:

1. **Identify MicroOS Packages and Integration Points**:
   - Review PersistenceOS.kiwi to identify all available MicroOS packages
   - Map each UI section to specific MicroOS packages from KIWI
   - Document the commands and utilities provided by each package
   - Define the integration points between frontend, API, and shell scripts
   - Example:
     ```
     # Storage section uses these MicroOS packages from KIWI:
     - btrfsprogs: For btrfs filesystem operations
     - xfsprogs: For XFS filesystem operations
     - lvm2: For logical volume management
     ```

2. **Create Shell Script Wrappers for MicroOS Packages**:
   - Develop shell scripts that directly use MicroOS packages installed via KIWI
   - Implement a command-based structure for easy API integration
   - Ensure consistent output formats (pipe-delimited) for Python parsing
   - Add comprehensive logging and error handling
   - Example:
     ```bash
     # storage-manager.sh directly uses btrfsprogs from KIWI
     case "$command" in
         list-pools)
             # Use btrfs command from btrfsprogs package
             btrfs filesystem show | format_output
             ;;
     esac
     ```

3. **Implement Python API Layer with FastAPI**:
   - Create FastAPI application structure with routers for each section
   - Develop the ShellScriptRunner utility for standardized shell script integration
   - Implement API endpoints that call the appropriate shell scripts
   - Add data transformation from shell script output to structured JSON
   - Example:
     ```python
     # storage_router.py calls storage-manager.sh which uses MicroOS packages
     @router.get("/pools")
     def get_storage_pools():
         result = storage_runner.run_command('list-pools')
         # Transform output to JSON
     ```

4. **Integrate with Existing Direct HTML/CSS Frontend**:
   - Create JavaScript modules for API communication without changing the existing UI structure
   - Connect existing UI components to the API while preserving the current design
   - Add user interaction handlers to existing buttons and controls
   - Ensure proper error handling and loading states
   - Example:
     ```javascript
     // api.js makes requests to Python API endpoints without changing HTML/CSS structure
     const API = {
         // System overview data (for the dashboard)
         getSystemInfo: async () => {
             const response = await fetch('/api/system/info');
             return response.json();
         },

         // Storage pools data (for the Storage section)
         getStoragePools: async () => {
             const response = await fetch('/api/storage/pools');
             return response.json();
         }
     };

     // Connect existing UI elements to API
     document.querySelector('#storage-menu').addEventListener('click', async () => {
         const pools = await API.getStoragePools();
         updateStoragePoolsUI(pools); // Updates existing UI elements
     });
     ```

5. **Implement Comprehensive Error Handling**:
   - Add error handling in shell scripts with proper exit codes
   - Implement error handling in Python with HTTP status codes
   - Add frontend error handling with user-friendly messages
   - Create logging at all layers for debugging
   - Example:
     ```python
     try:
         result = storage_runner.run_command('list-pools')
         if result.returncode != 0:
             raise HTTPException(status_code=500, detail=result.stderr)
     except Exception as e:
         logger.exception(f"Error: {str(e)}")
         raise HTTPException(status_code=500, detail=str(e))
     ```

6. **Test End-to-End Integration**:
   - Verify shell scripts correctly use MicroOS packages from KIWI
   - Test Python API endpoints with various inputs
   - Validate frontend correctly displays data from the API
   - Test error handling across all layers
   - Example test case:
     ```
     1. Create a storage pool using the UI
     2. Verify the API calls storage-manager.sh
     3. Confirm storage-manager.sh uses mkfs.btrfs from btrfsprogs
     4. Validate the pool appears in the UI
     ```

7. **Document the Integration Chain**:
   - Create detailed documentation of the integration points
   - Document which MicroOS packages are used by each shell script
   - Provide examples of the data flow from UI to MicroOS packages and back
   - Update PersistenceOS-Structure.md with the hybrid approach details

This hybrid approach gives us the best of both worlds - we directly leverage the MicroOS packages installed via KIWI through shell scripts while providing a clean, modern API for the web UI using Python. This ensures compatibility with the MicroOS ecosystem while enabling a modern, maintainable web UI.

#### 8. Single Authentication with Dynamic Navigation for Direct HTML/CSS Frontend

A critical advantage of this hybrid approach is its perfect compatibility with our existing direct HTML/CSS frontend design, particularly our single authentication with dynamic navigation flow. This ensures users authenticate only once and can navigate freely between sections without re-authentication.

1. **Authentication and Navigation Flow**:
   - **Initial Authentication** (One-time process):
     - User visits `login.html` and enters credentials
     - JavaScript validates credentials via API call to `/api/auth/login`
     - Upon successful authentication, auth token is stored in localStorage
     - Browser redirects to `index.html` (only full page navigation in the flow)
     - `index.html` loads with Overview/Dashboard as default section

   - **Section Navigation** (Dynamic, no re-authentication):
     - User clicks sidebar menu item (Storage, Virtualization, etc.)
     - JavaScript updates URL hash (e.g., from `#dashboard` to `#storage`)
     - Content area updates dynamically without page reload
     - API requests include stored auth token from initial login
     - No re-authentication required when switching sections
     - Session persists until explicit logout or token expiration

2. **Preserving the Existing UI Design**:
   - The hybrid approach requires no changes to the HTML structure or CSS styling
   - All existing UI components (sidebar, cards, buttons, progress bars) remain unchanged
   - The visual design and user experience remain consistent
   - Example: The dashboard with system metrics, VM cards, and storage pools maintains its current layout and styling

3. **Authentication Implementation**:
   ```javascript
   // In login.js - Handles one-time authentication
   document.getElementById('login-form').addEventListener('submit', async function(event) {
       event.preventDefault();

       const username = document.getElementById('username').value;
       const password = document.getElementById('password').value;

       try {
           // Call authentication API
           const response = await fetch('/api/auth/login', {
               method: 'POST',
               headers: {
                   'Content-Type': 'application/json'
               },
               body: JSON.stringify({ username, password })
           });

           if (response.ok) {
               const data = await response.json();

               // Store auth token in localStorage for persistent session
               localStorage.setItem('auth_token', data.token);

               // Redirect to index.html (only full page navigation in the flow)
               window.location.href = 'index.html';
           } else {
               // Show login error
               document.getElementById('login-error').textContent = 'Invalid username or password';
           }
       } catch (error) {
           console.error('Login error:', error);
           document.getElementById('login-error').textContent = 'An error occurred during login';
       }
   });
   ```

4. **Dynamic Navigation Implementation**:
   ```javascript
   // In app.js - Handles dynamic section navigation without re-authentication
   document.addEventListener('DOMContentLoaded', function() {
       // Set up navigation event listeners
       const menuItems = document.querySelectorAll('.sidebar .menu-item');

       menuItems.forEach(item => {
           item.addEventListener('click', function(event) {
               // Get the target section from data attribute or id
               const targetSection = this.getAttribute('data-target') || this.id.replace('-menu', '');

               // Update URL hash without page reload
               window.location.hash = targetSection;

               // Handle the navigation (function defined below)
               navigateToSection(targetSection);
           });
       });

       // Handle navigation to a specific section
       function navigateToSection(sectionId) {
           // Hide all sections
           const allSections = document.querySelectorAll('.content-section');
           allSections.forEach(section => {
               section.style.display = 'none';
           });

           // Show the target section
           const targetSection = document.getElementById(sectionId + '-section');
           if (targetSection) {
               targetSection.style.display = 'block';

               // Load data for the section using stored auth token
               loadSectionData(sectionId);
           }

           // Update active state in sidebar
           menuItems.forEach(item => {
               item.classList.remove('active');
               if (item.getAttribute('data-target') === sectionId ||
                   item.id === sectionId + '-menu') {
                   item.classList.add('active');
               }
           });
       }

       // Initialize with default section (dashboard) or from URL hash
       const initialSection = window.location.hash.substring(1) || 'dashboard';
       navigateToSection(initialSection);

       // Listen for hash changes (browser back/forward)
       window.addEventListener('hashchange', function() {
           const hash = window.location.hash.substring(1) || 'dashboard';
           navigateToSection(hash);
       });
   });
   ```

5. **API Communication with Stored Authentication**:
   ```javascript
   // In api.js - Handles API communication with stored authentication token
   const API = {
       // Get auth token from localStorage (set during login)
       getAuthToken: function() {
           return localStorage.getItem('auth_token');
       },

       // Generic fetch method with authentication
       fetch: async function(endpoint, options = {}) {
           const token = this.getAuthToken();

           // Add authentication header to all requests
           const headers = {
               'Content-Type': 'application/json',
               'Authorization': `Bearer ${token}`,
               ...(options.headers || {})
           };

           try {
               const response = await fetch(endpoint, {
                   ...options,
                   headers
               });

               // Handle authentication errors (redirect to login if token expired)
               if (response.status === 401) {
                   localStorage.removeItem('auth_token');
                   window.location.href = 'login.html';
                   return null;
               }

               return response;
           } catch (error) {
               console.error(`API error for ${endpoint}:`, error);
               throw error;
           }
       },

       // Section-specific API methods that use the stored token
       getSystemInfo: async function() {
           const response = await this.fetch('/api/system/info');
           return response.json();
       },

       getVirtualMachines: async function() {
           const response = await this.fetch('/api/vms/list');
           return response.json();
       }

       // Additional API methods for other sections...
   };
   ```

6. **Installation Configuration for Navigation Flow**:
   - **Nginx Configuration** (set up during installation):
     ```nginx
     # /etc/nginx/conf.d/persistenceos-nginx.conf
     server {
         listen 8080;
         server_name localhost;

         # Root directory for static files
         root /usr/share/persistenceos-ui;

         # Default entry point is login.html
         index login.html;

         # Serve static files directly
         location / {
             try_files $uri $uri/ =404;

             # Disable caching for HTML files to prevent stale UI
             location ~* \.html$ {
                 add_header Cache-Control "no-store, no-cache, must-revalidate";
             }

             # Limited caching for CSS/JS (1 hour)
             location ~* \.(css|js)$ {
                 add_header Cache-Control "public, max-age=3600";
             }
         }

         # Proxy API requests to FastAPI backend
         location /api/ {
             proxy_pass http://localhost:8000;
             proxy_set_header Host $host;
             proxy_set_header X-Real-IP $remote_addr;
             proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
             proxy_set_header X-Forwarded-Proto $scheme;

             # Add CORS headers
             add_header 'Access-Control-Allow-Origin' '*';
             add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
             add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
         }
     }
     ```

   - **FastAPI Authentication Endpoint** (configured during installation):
     ```python
     # /usr/lib/persistence/api/routers/auth.py
     from fastapi import APIRouter, HTTPException, Depends
     from fastapi.security import OAuth2PasswordRequestForm
     from datetime import datetime, timedelta
     from jose import jwt, JWTError
     import os

     router = APIRouter(prefix="/api/auth", tags=["authentication"])

     # Secret key generated during installation
     SECRET_KEY = os.getenv("JWT_SECRET_KEY", "generated-during-installation")
     ALGORITHM = "HS256"
     ACCESS_TOKEN_EXPIRE_MINUTES = 1440  # 24 hours

     @router.post("/login")
     async def login(form_data: OAuth2PasswordRequestForm = Depends()):
         # Validate credentials (in production, check against system users)
         if form_data.username == "root" and form_data.password == "linux":
             # Create access token with expiration
             access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
             expire = datetime.utcnow() + access_token_expires

             token_data = {
                 "sub": form_data.username,
                 "exp": expire
             }

             # Create JWT token
             access_token = jwt.encode(token_data, SECRET_KEY, algorithm=ALGORITHM)

             return {"token": access_token, "token_type": "bearer"}
         else:
             raise HTTPException(status_code=401, detail="Invalid credentials")
     ```

   - **JavaScript Files Configuration** (set up during installation):
     - All JavaScript files are bundled and minified during installation
     - Files are placed in `/usr/share/persistenceos-ui/js/`
     - Proper script tags are added to HTML files
     - Example bundle command in installation script:
       ```bash
       # Bundle JavaScript files during installation
       cat /usr/share/persistenceos-ui/js/api.js \
           /usr/share/persistenceos-ui/js/auth.js \
           /usr/share/persistenceos-ui/js/app.js > /usr/share/persistenceos-ui/js/bundle.js
       ```

   - **HTML Configuration** (set up during installation):
     - login.html includes login.js for authentication
     - index.html includes bundle.js for navigation and API communication
     - Both files are configured to work with the authentication and navigation flow
     - Example HTML configuration:
       ```html
       <!-- login.html -->
       <!DOCTYPE html>
       <html>
       <head>
           <title>PersistenceOS - Login</title>
           <link rel="stylesheet" href="css/style.css">
       </head>
       <body class="login-page">
           <div class="login-container">
               <form id="login-form">
                   <!-- Login form fields -->
                   <div id="login-error" class="error-message"></div>
               </form>
           </div>
           <script src="js/login.js"></script>
       </body>
       </html>

       <!-- index.html -->
       <!DOCTYPE html>
       <html>
       <head>
           <title>PersistenceOS</title>
           <link rel="stylesheet" href="css/style.css">
       </head>
       <body>
           <div class="sidebar">
               <!-- Sidebar menu items -->
           </div>
           <div class="content">
               <!-- Content sections with IDs matching hash navigation -->
               <div id="dashboard-section" class="content-section">
                   <!-- Dashboard content -->
               </div>
               <div id="vms-section" class="content-section">
                   <!-- VMs content -->
               </div>
               <!-- Other content sections -->
           </div>
           <script src="js/bundle.js"></script>
       </body>
       </html>
       ```

   - **Installation Script Entries** (in config.sh):
     ```bash
     # Set up web UI navigation in config.sh
     echo "Setting up web UI navigation..."

     # Generate JWT secret key for authentication
     JWT_SECRET=$(openssl rand -hex 32)
     echo "JWT_SECRET_KEY=\"$JWT_SECRET\"" >> /etc/persistenceos/api.env

     # Configure Nginx for proper static file serving and API proxying
     cp /usr/lib/persistence/config/persistenceos-nginx.conf /etc/nginx/conf.d/

     # Ensure JavaScript files are properly bundled
     mkdir -p /usr/share/persistenceos-ui/js
     cat /usr/lib/persistence/ui/js/api.js \
         /usr/lib/persistence/ui/js/auth.js \
         /usr/lib/persistence/ui/js/app.js > /usr/share/persistenceos-ui/js/bundle.js
     cp /usr/lib/persistence/ui/js/login.js /usr/share/persistenceos-ui/js/

     # Set proper permissions
     chmod 644 /usr/share/persistenceos-ui/js/*.js

     # Restart Nginx to apply configuration
     systemctl restart nginx

     echo "Web UI navigation setup complete."
     ```

This approach ensures we can leverage the power of Python and MicroOS packages while maintaining our carefully designed direct HTML/CSS frontend, providing a seamless user experience with single authentication and dynamic navigation between sections.

## Package Requirements from PersistenceOS.kiwi

Based on the PersistenceOS.kiwi file, the following packages are required for the project:

### Core System Packages
- kernel-default
- systemd
- systemd-sysvinit
- systemd-default-settings
- dracut-kiwi-oem-repart
- dracut-kiwi-oem-dump

### Web UI Packages
- nginx
- python311-base
- python311-fastapi
- python311-uvicorn

### Virtualization Packages
- libvirt-daemon
- libvirt-client
- libvirt-daemon-config-network
- libvirt-daemon-qemu
- qemu-kvm
- qemu-tools
- dnsmasq

### Storage Management Packages
- btrfsprogs
- xfsprogs
- lvm2
- e2fsprogs
- snapper
- snapper-zypp-plugin

### Network Management Packages
- NetworkManager
- NetworkManager-cli
- iproute2
- nftables

### System Utilities
- vim-small
- less
- bash-completion
- supportutils
- parted
- perl-Bootloader
- openssl

### Container Support
- podman
- slirp4netns
- fuse-overlayfs
- toolbox

### Hardware Support
- usbutils
- pciutils
- lm_sensors
- smartmontools
- kbd

## OBS-Specific Files

Before starting the build process, it's essential to understand the OBS-specific files required for a successful build:

### 1. _constraints (Hardware Requirements)

The `_constraints` file defines the hardware requirements for the OBS build service. This ensures that the build environment has sufficient resources to complete the build process.

```xml
<constraints>
  <hardware>
    <disk>
      <size unit="G">40</size>
    </disk>
    <memory>
      <size unit="G">8</size>
    </memory>
    <physicalmemory>
      <size unit="G">8</size>
    </physicalmemory>
  </hardware>
  <overwrite>
    <conditions>
      <arch>aarch64</arch>
    </conditions>
    <hardware>
      <processors>4</processors>
    </hardware>
  </overwrite>
</constraints>
```

### 2. _service (OBS Service Configuration)

The `_service` file configures the OBS service integration. For PersistenceOS, we use a minimal service file:

```xml
<services>
  <!-- Empty service file to avoid service errors -->
</services>
```

### 3. persistence-os.spec (RPM Specification)

The `persistence-os.spec` file defines how the package is built and installed. This is critical for proper integration with the OBS build system:

```spec
Name:           persistence-os
Version:        6.1.0
Release:        0
Summary:        PersistenceOS - Advanced Storage Management and Snapshot Functionality
License:        GPL-3.0
Group:          System/Management
URL:            https://persistence-os.org
Source0:        PersistenceOS.kiwi
Source1:        config.sh
Source2:        images.sh
Source3:        editbootinstaller.sh
Source4:        UI-Sources.sh
Source5:        UI-Extract.sh
Source6:        UI-Int.sh
Source7:        persistenceos-ui-setup.service
Source8:        persistenceos-nginx.conf
Source9:        persistence-webserver.py
Source10:       persistence-webserver.service
Source11:       persistenceos-ui.tar.gz
BuildRequires:  kiwi
BuildRequires:  kiwi-tools
Requires:       kiwi-tools
BuildRoot:      %{_tmppath}/%{name}-%{version}-build

%description
PersistenceOS is a specialized hypervisor and NAS operating system based on SUSE Micro Leap 6.1,
designed to provide robust virtualization capabilities with advanced storage management and
snapshot functionality.

%install
# Install files into the buildroot
mkdir -p %{buildroot}/usr/share/kiwi/image/PersistenceOS
cp %{SOURCE0} %{buildroot}/usr/share/kiwi/image/PersistenceOS/
cp %{SOURCE1} %{buildroot}/usr/share/kiwi/image/PersistenceOS/
cp %{SOURCE2} %{buildroot}/usr/share/kiwi/image/PersistenceOS/
cp %{SOURCE3} %{buildroot}/usr/share/kiwi/image/PersistenceOS/

# Create directories for scripts and services
mkdir -p %{buildroot}/usr/lib/persistence/scripts
cp %{SOURCE4} %{buildroot}/usr/lib/persistence/scripts/
cp %{SOURCE5} %{buildroot}/usr/lib/persistence/scripts/
cp %{SOURCE6} %{buildroot}/usr/lib/persistence/scripts/

# Create directory for systemd services
mkdir -p %{buildroot}/usr/lib/systemd/system
cp %{SOURCE7} %{buildroot}/usr/lib/systemd/system/
cp %{SOURCE10} %{buildroot}/usr/lib/systemd/system/

# Create directory for Nginx configuration
mkdir -p %{buildroot}/etc/nginx/conf.d
cp %{SOURCE8} %{buildroot}/etc/nginx/conf.d/

# Copy web server script
cp %{SOURCE9} %{buildroot}/usr/lib/persistence/scripts/

# Create directory for UI tarball
mkdir -p %{buildroot}/image
cp %{SOURCE11} %{buildroot}/image/persistenceos-ui.tar.gz

%files
%defattr(-,root,root)
/usr/share/kiwi/image/PersistenceOS/
/usr/lib/persistence/scripts/
/usr/lib/systemd/system/persistenceos-ui-setup.service
/usr/lib/systemd/system/persistence-webserver.service
/etc/nginx/conf.d/persistenceos-nginx.conf
/image/persistenceos-ui.tar.gz

%post
# Enable services
if [ $1 -eq 1 ] ; then
    # Initial installation
    systemctl enable persistenceos-ui-setup.service
    systemctl enable persistence-webserver.service
    systemctl enable nginx.service
fi
```

## Build Sequence for OBS

### Detailed OBS Project Setup

1. **Create OBS Project**
   - Log in to the Open Build Service (OBS) web interface
   - Click on "Home Project" in the top navigation
   - Click "Create Project" button
   - Enter "PersistenceOS" as the project name
   - Add a description: "PersistenceOS - Hypervisor & NAS Management OS"
   - Set the project to "Public" for visibility
   - Click "Create Project" to confirm

2. **Configure Project Repositories**
   - Navigate to your newly created PersistenceOS project
   - Click on the "Repositories" tab
   - Click "Add repositories" button
   - Select "SUSE Linux Enterprise 15" as the base distribution
   - Select "MicroOS 6.1" as the repository
   - Click "Add selected repositories" button
   - Ensure the repository is set as "Standard" type
   - Add any additional repositories needed for dependencies

3. **Upload OBS-Specific Files**
   - Click on the "Sources" tab in your project
   - Upload `_constraints` file for hardware requirements
   - Upload `_service` file for OBS service integration
   - Upload `persistence-os.spec` file for package building

4. **Upload PersistenceOS.kiwi File**
   - Click "Add file" button
   - Select "Upload file" option
   - Browse and select your PersistenceOS.kiwi file
   - Click "Save" to upload the file
   - Verify the file appears in the source list

5. **Upload Common Scripts**
   - Click "Add file" button again
   - Upload common.sh as the foundation script
   - Ensure the file permissions are set correctly (executable)

6. **Core Scripts**
   - Upload config.sh
   - Upload nftables-setup.sh
   - Upload editbootinstaller.sh
   - Upload images.sh
   - Upload persistenceos-install.sh
   - Upload install-packages.sh
   - Upload install-scripts.sh

7. **Service Files**
   - Upload persistenceos-installer.service
   - Upload persistence-nftables-setup.service
   - Upload persistenceos-api.service
   - Upload persistenceos-firstboot.service

8. **Web UI Files**
   - Upload login.html, index.html, style.css
   - Upload JavaScript modules
   - Upload UI-Int.sh, UI-Version-Check.sh
   - Upload UI-Extract.sh
   - Upload persistenceos-ui.tar.gz (created by UI-Sources.sh)

9. **API Backend**
   - Upload app.py, persistenceos/package_init.py, persistenceos/main.py, persistenceos/routers/routers_init.py, persistenceos/utils/utils_init.py, and API routers
   - Upload API configuration files

10. **System Management Scripts**
    - Upload persistence-system.sh
    - Upload health-checker.sh
    - Upload persistence-storage.sh
    - Upload persistence-snapshots.sh

11. **Final Build**
    - Verify all files are uploaded
    - Trigger OBS build
    - Test resulting image

## API Backend Implementation

### 1. app.py and persistenceos/main.py (FastAPI Application)

```python
#!/usr/bin/env python3
#================
# FILE          : app.py
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Main FastAPI application for PersistenceOS
#================

import os
import logging
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPBasic, HTTPBasicCredentials
import secrets
import uvicorn
import subprocess
from typing import Dict, List, Optional, Any

# Import routers
from routers import system, vms, storage, network

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("/var/log/persistence/api.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("persistenceos-api")

# Create FastAPI app
app = FastAPI(
    title="PersistenceOS API",
    description="API for PersistenceOS administration",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBasic()

def get_current_username(credentials: HTTPBasicCredentials = Depends(security)):
    """Validate credentials and return username if valid."""
    # In a production environment, this should use a more secure method
    # For now, we're using the root/linux credentials as specified
    correct_username = "root"
    correct_password = "linux"

    is_correct_username = secrets.compare_digest(credentials.username, correct_username)
    is_correct_password = secrets.compare_digest(credentials.password, correct_password)

    if not (is_correct_username and is_correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Basic"},
        )

    return credentials.username

# Include routers
app.include_router(
    system.router,
    prefix="/api/system",
    tags=["system"],
    dependencies=[Depends(get_current_username)]
)

app.include_router(
    vms.router,
    prefix="/api/vms",
    tags=["vms"],
    dependencies=[Depends(get_current_username)]
)

app.include_router(
    storage.router,
    prefix="/api/storage",
    tags=["storage"],
    dependencies=[Depends(get_current_username)]
)

app.include_router(
    network.router,
    prefix="/api/network",
    tags=["network"],
    dependencies=[Depends(get_current_username)]
)

@app.get("/api/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "ok", "version": "1.0.0"}

@app.get("/api/version")
async def version():
    """Get API version."""
    return {"version": "1.0.0"}

# Run the application if executed directly
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        log_level="info"
    )
```

### 2. system.py (System Router)

```python
#!/usr/bin/env python3
#================
# FILE          : system.py
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : System information and management API endpoints
#================

import os
import subprocess
import logging
import psutil
from fastapi import APIRouter, HTTPException, Depends
from typing import Dict, List, Optional, Any
from pydantic import BaseModel

# Configure logging
logger = logging.getLogger("persistenceos-api.system")

# Create router
router = APIRouter()

# Models
class SystemInfo(BaseModel):
    hostname: str
    kernel: str
    os_version: str
    uptime: str
    cpu_count: int
    memory_total: int
    memory_used: int
    memory_free: int
    disk_total: int
    disk_used: int
    disk_free: int

class SystemStatus(BaseModel):
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    load_avg: List[float]
    processes: int

# Helper functions
def run_command(command: List[str]) -> str:
    """Run a command and return its output."""
    try:
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e}")
        raise HTTPException(status_code=500, detail=f"Command failed: {e}")

# Endpoints
@router.get("/info", response_model=SystemInfo)
async def get_system_info():
    """Get system information."""
    try:
        # Get hostname
        hostname = run_command(["hostname"])

        # Get kernel version
        kernel = run_command(["uname", "-r"])

        # Get OS version
        os_version = "PersistenceOS 6.1"
        if os.path.exists("/etc/os-release"):
            with open("/etc/os-release", "r") as f:
                for line in f:
                    if line.startswith("PRETTY_NAME="):
                        os_version = line.split("=")[1].strip().strip('"')
                        break

        # Get uptime
        uptime = run_command(["uptime", "-p"])

        # Get CPU count
        cpu_count = psutil.cpu_count()

        # Get memory info
        memory = psutil.virtual_memory()
        memory_total = memory.total
        memory_used = memory.used
        memory_free = memory.available

        # Get disk info
        disk = psutil.disk_usage("/")
        disk_total = disk.total
        disk_used = disk.used
        disk_free = disk.free

        return SystemInfo(
            hostname=hostname,
            kernel=kernel,
            os_version=os_version,
            uptime=uptime,
            cpu_count=cpu_count,
            memory_total=memory_total,
            memory_used=memory_used,
            memory_free=memory_free,
            disk_total=disk_total,
            disk_used=disk_used,
            disk_free=disk_free
        )
    except Exception as e:
        logger.error(f"Error getting system info: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting system info: {e}")

@router.get("/status", response_model=SystemStatus)
async def get_system_status():
    """Get system status."""
    try:
        # Get CPU percent
        cpu_percent = psutil.cpu_percent(interval=1)

        # Get memory percent
        memory_percent = psutil.virtual_memory().percent

        # Get disk percent
        disk_percent = psutil.disk_usage("/").percent

        # Get load average
        load_avg = psutil.getloadavg()

        # Get process count
        processes = len(psutil.pids())

        return SystemStatus(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            disk_percent=disk_percent,
            load_avg=list(load_avg),
            processes=processes
        )
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting system status: {e}")

@router.post("/reboot")
async def reboot_system():
    """Reboot the system."""
    try:
        # Run reboot command
        subprocess.Popen(["systemctl", "reboot"])
        return {"message": "System is rebooting"}
    except Exception as e:
        logger.error(f"Error rebooting system: {e}")
        raise HTTPException(status_code=500, detail=f"Error rebooting system: {e}")

@router.post("/shutdown")
async def shutdown_system():
    """Shutdown the system."""
    try:
        # Run shutdown command
        subprocess.Popen(["systemctl", "poweroff"])
        return {"message": "System is shutting down"}
    except Exception as e:
        logger.error(f"Error shutting down system: {e}")
        raise HTTPException(status_code=500, detail=f"Error shutting down system: {e}")
```

## Web UI Implementation

### 1. login.html

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login</title>
    <link rel="stylesheet" href="static/css/style.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-header">
            <img src="static/img/logo.png" alt="PersistenceOS Logo" class="login-logo">
            <h1>PersistenceOS</h1>
            <p>Hypervisor & NAS Management</p>
        </div>
        <div class="login-form">
            <form id="login-form">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn-primary">Login</button>
                </div>
                <div id="login-error" class="error-message"></div>
            </form>
        </div>
        <div class="login-footer">
            <p>PersistenceOS 6.1 &copy; 2024</p>
        </div>
    </div>
    <script src="static/js/auth.js"></script>
</body>
</html>
```

### 2. index.html (Partial)

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Dashboard</title>
    <link rel="stylesheet" href="static/css/style.css">
</head>
<body>
    <div class="app-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="static/img/logo.png" alt="PersistenceOS Logo" class="sidebar-logo">
                <h2>PersistenceOS</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#dashboard" class="active"><i class="icon-dashboard"></i> Dashboard</a></li>
                    <li><a href="#virtualization"><i class="icon-vm"></i> Virtualization</a></li>
                    <li><a href="#storage"><i class="icon-storage"></i> Storage</a></li>
                    <li><a href="#network"><i class="icon-network"></i> Network</a></li>
                    <li><a href="#settings"><i class="icon-settings"></i> Settings</a></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button id="logout-btn" class="btn-logout"><i class="icon-logout"></i> Logout</button>
            </div>
        </div>
        <div class="main-content">
            <header class="top-bar">
                <div class="page-title">
                    <h1 id="page-title">Overview</h1>
                </div>
                <div class="user-menu">
                    <span id="username-display">root</span>
                    <button id="refresh-btn" class="btn-refresh"><i class="icon-refresh"></i></button>
                </div>
            </header>
            <div class="content-area">
                <!-- Dashboard Section -->
                <section id="dashboard-section" class="content-section active">
                    <div class="dashboard-grid">
                        <div class="dashboard-card system-info">
                            <div class="card-header">
                                <h3>System Information</h3>
                                <button class="btn-refresh" data-target="system-info"><i class="icon-refresh"></i></button>
                            </div>
                            <div class="card-content">
                                <div class="info-item">
                                    <span class="info-label">Hostname:</span>
                                    <span id="hostname" class="info-value">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">OS Version:</span>
                                    <span id="os-version" class="info-value">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Kernel:</span>
                                    <span id="kernel" class="info-value">Loading...</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Uptime:</span>
                                    <span id="uptime" class="info-value">Loading...</span>
                                </div>
                            </div>
                        </div>
                        <!-- More dashboard cards... -->
                    </div>
                </section>

                <!-- Other sections will be added here... -->
            </div>
        </div>
    </div>
    <script src="static/js/api.js"></script>
    <script src="static/js/app.js"></script>
</body>
</html>
```

## Installation Process Improvements

### Bootloader Installation Enhancements

To improve the bootloader installation process, we will implement the following enhancements:

1. **Modular Bootloader Installation**
   - Rename `editbootinstaller.sh` to `install-bootloader.sh` for consistency
   - Create separate functions for UEFI and BIOS installation
   - Implement a more robust boot UUID detection mechanism
   - Centralize GRUB menu entry creation in a single function
   - Add better error handling and recovery mechanisms
   - Provide a dry-run option for testing without making changes
   - Address MicroOS-specific requirements:
     - Integrate with transactional-update system
     - Handle read-only root filesystem properly
     - Configure for btrfs snapshot support
     - Include MicroOS-specific boot parameters
     - Ensure proper integration with MicroOS boot process

2. **Chroot Environment Improvements**
   - Create a more robust chroot environment setup function
   - Implement proper error handling for all chroot operations
   - Add a cleanup function that ensures all filesystems are unmounted even in case of errors
   - Implement a validation step to verify the chroot environment before executing commands
   - Add logging for all chroot operations to aid in debugging

3. **Script Consolidation**
   - Consolidate all installation scripts into a single `install-manager.sh` script
   - Implement a command-based interface with the following commands:
     - `prepare`: Prepare target device (partitioning and formatting)
     - `bootloader`: Install bootloader to target device
     - `system`: Install base system
     - `post`: Perform post-installation configuration
     - `packages`: Install additional packages
     - `scripts`: Install system scripts
     - `all`: Perform complete installation (all steps above)
     - `status`: Show installation status
     - `cleanup`: Clean up installation environment
   - Ensure proper state management between installation steps
   - Add comprehensive logging and error handling

### MicroOS-Specific Bootloader Implementation

To properly handle MicroOS-specific bootloader requirements, the following functions should be implemented in `install-bootloader.sh`:

```bash
# Handle transactional update system
install_bootloader_transactional() {
    local target_dir="${1:-/mnt}"
    local dry_run="${2:-false}"

    log_info "Installing bootloader using transactional-update"

    if [ "${dry_run}" = "true" ]; then
        log_info "[DRY RUN] Would execute: transactional-update --no-selfupdate grub.install"
        return 0
    fi

    # Check if transactional-update is available
    if ! run_in_target "command -v transactional-update" &>/dev/null; then
        log_warning "transactional-update not found, falling back to direct installation"
        return 2  # Return code 2 indicates fallback needed
    fi

    # Use transactional-update for bootloader installation
    run_in_target "transactional-update --no-selfupdate grub.install"
    local result=$?

    if [ ${result} -eq 0 ]; then
        log_success "Bootloader installed using transactional-update"
    else
        log_error "Failed to install bootloader using transactional-update"
    fi

    return ${result}
}

# Handle read-only root filesystem
check_and_remount_root() {
    local target_dir="${1:-/mnt}"

    log_info "Checking if root filesystem is read-only"

    # Check if filesystem is mounted read-only
    if mount | grep "${target_dir} " | grep -q "ro,"; then
        log_info "Root filesystem is mounted read-only, remounting as read-write"

        # Remount as read-write
        mount -o remount,rw "${target_dir}" || {
            log_error "Failed to remount root filesystem as read-write"
            return 1
        }

        log_success "Root filesystem remounted as read-write"
        return 0
    fi

    log_info "Root filesystem is already mounted read-write"
    return 0
}

# Configure for btrfs snapshot support
configure_snapshot_support() {
    local target_dir="${1:-/mnt}"
    local dry_run="${2:-false}"

    log_info "Configuring bootloader for btrfs snapshot support"

    # Check if snapper is available
    if ! run_in_target "command -v snapper" &>/dev/null; then
        log_warning "Snapper not found, snapshot support may be limited"
    fi

    # Configure GRUB for snapshot support
    local grub_default="${target_dir}/etc/default/grub"

    if [ ! -f "${grub_default}" ]; then
        log_error "GRUB default configuration not found at ${grub_default}"
        return 1
    fi

    if [ "${dry_run}" = "true" ]; then
        log_info "[DRY RUN] Would add SUSE_BTRFS_SNAPSHOT_BOOTING=\"true\" to ${grub_default}"
    else
        # Add snapshot booting support if not already present
        if ! grep -q "SUSE_BTRFS_SNAPSHOT_BOOTING" "${grub_default}"; then
            echo 'SUSE_BTRFS_SNAPSHOT_BOOTING="true"' >> "${grub_default}"
            log_success "Added SUSE_BTRFS_SNAPSHOT_BOOTING=\"true\" to GRUB configuration"
        else
            # Ensure it's set to true
            sed -i 's/SUSE_BTRFS_SNAPSHOT_BOOTING=.*/SUSE_BTRFS_SNAPSHOT_BOOTING="true"/' "${grub_default}"
            log_success "Updated SUSE_BTRFS_SNAPSHOT_BOOTING to \"true\" in GRUB configuration"
        fi
    fi

    return 0
}

# Configure MicroOS-specific boot parameters
configure_microos_boot_parameters() {
    local target_dir="${1:-/mnt}"
    local dry_run="${2:-false}"

    log_info "Configuring MicroOS-specific boot parameters"

    local grub_default="${target_dir}/etc/default/grub"

    if [ ! -f "${grub_default}" ]; then
        log_error "GRUB default configuration not found at ${grub_default}"
        return 1
    fi

    if [ "${dry_run}" = "true" ]; then
        log_info "[DRY RUN] Would update kernel parameters and timeout in ${grub_default}"
    else
        # Update kernel parameters
        if grep -q "GRUB_CMDLINE_LINUX_DEFAULT=" "${grub_default}"; then
            # Add MicroOS-specific parameters if not already present
            if ! grep -q "rd.systemd.show_status=auto" "${grub_default}"; then
                sed -i 's/GRUB_CMDLINE_LINUX_DEFAULT="/GRUB_CMDLINE_LINUX_DEFAULT="mitigations=auto quiet splash rd.systemd.show_status=auto rd.udev.log_priority=3 /' "${grub_default}"
                log_success "Added MicroOS-specific kernel parameters to GRUB configuration"
            fi
        else
            # Create the parameter if it doesn't exist
            echo 'GRUB_CMDLINE_LINUX_DEFAULT="mitigations=auto quiet splash rd.systemd.show_status=auto rd.udev.log_priority=3"' >> "${grub_default}"
            log_success "Created MicroOS-specific kernel parameters in GRUB configuration"
        fi

        # Set appropriate timeout
        if grep -q "GRUB_TIMEOUT=" "${grub_default}"; then
            sed -i 's/GRUB_TIMEOUT=.*/GRUB_TIMEOUT=5/' "${grub_default}"
        else
            echo 'GRUB_TIMEOUT=5' >> "${grub_default}"
        fi
        log_success "Set GRUB timeout to 5 seconds"
    fi

    return 0
}
```

These functions should be integrated into the main `install_bootloader` function to ensure proper handling of MicroOS-specific requirements.

### Installation Manager Implementation

The new `installation-manager.sh` script will provide a unified interface for all installation operations:

```bash
# Usage examples
installation-manager.sh bootloader  # Configure bootloader
installation-manager.sh system      # Perform system installation
installation-manager.sh post        # Perform post-installation configuration
installation-manager.sh packages    # Install required packages
installation-manager.sh scripts     # Install system scripts
installation-manager.sh all         # Perform complete installation process
```

This approach offers several advantages:
- Simplified installation process with a single entry point
- Consistent error handling and logging across all installation steps
- Better dependency management between installation steps
- Easier maintenance and updates
- Support for both interactive and non-interactive modes
- Dry-run capability for testing without making changes

## Conclusion

This rebuild plan provides a comprehensive guide for recreating PersistenceOS with clean, accurate code. By following the build sequence and implementing the components as described, you'll be able to create a fully functional web UI administration OS based on MicroOS using Nginx, Python, and FastAPI/Uvicorn.

The plan focuses on:
1. Building a solid foundation with common functions and core scripts
2. Implementing a proper nftables firewall configuration
3. Creating a clean, modern web UI with hash-based navigation
4. Developing a robust API backend with FastAPI
5. Ensuring all components work together seamlessly

By following this approach, you'll avoid filler code and create a purpose-built system that meets the specific requirements of PersistenceOS.

## Critical Installation Scripts

### 1. config.sh (System Configuration)

```bash
#!/bin/bash
#================
# FILE          : config.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Main configuration script for PersistenceOS installation
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Define paths
PERSISTENCE_VERSION="6.1"
PERSISTENCE_ROOT="/usr/lib/persistence"
PERSISTENCE_SCRIPTS="${PERSISTENCE_ROOT}/scripts"
PERSISTENCE_SERVICES="${PERSISTENCE_ROOT}/services"
PERSISTENCE_BIN="${PERSISTENCE_ROOT}/bin"
PERSISTENCE_TOOLS="${PERSISTENCE_ROOT}/tools"
PERSISTENCE_WEB_UI="${PERSISTENCE_ROOT}/web-ui"
PERSISTENCE_API="${PERSISTENCE_ROOT}/api"
PERSISTENCE_VAR="/var/lib/persistence"
PERSISTENCE_LOG="/var/log/persistence"

# Logging function
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Log to console with colors
    case "${level}" in
        "INFO")
            echo -e "\033[0;34m[${level}]\033[0m ${message}"
            ;;
        "SUCCESS")
            echo -e "\033[0;32m[${level}]\033[0m ${message}"
            ;;
        "WARNING")
            echo -e "\033[0;33m[${level}]\033[0m ${message}"
            ;;
        "ERROR")
            echo -e "\033[0;31m[${level}]\033[0m ${message}"
            ;;
        "DEBUG")
            echo -e "\033[0;35m[${level}]\033[0m ${message}"
            ;;
        *)
            echo "[${level}] ${message}"
            ;;
    esac
}

# Safe systemctl wrapper
safe_systemctl() {
    if command -v systemctl &> /dev/null; then
        systemctl "$@"
        return $?
    else
        log "WARNING" "systemctl not available, skipping: systemctl $*"
        return 0
    fi
}

# Create directory if it doesn't exist
ensure_directory() {
    local dir="$1"
    if [ ! -d "${dir}" ]; then
        mkdir -p "${dir}"
        log "INFO" "Created directory: ${dir}"
    fi
}

# Setup directory structure
setup_directories() {
    log "INFO" "Setting up directory structure"

    # Create main directories
    ensure_directory "${PERSISTENCE_ROOT}"
    ensure_directory "${PERSISTENCE_SCRIPTS}"
    ensure_directory "${PERSISTENCE_SERVICES}"
    ensure_directory "${PERSISTENCE_BIN}"
    ensure_directory "${PERSISTENCE_TOOLS}"
    ensure_directory "${PERSISTENCE_WEB_UI}"
    ensure_directory "${PERSISTENCE_API}"
    ensure_directory "${PERSISTENCE_VAR}"
    ensure_directory "${PERSISTENCE_LOG}"

    # Set permissions
    chmod -R 755 "${PERSISTENCE_ROOT}"

    log "SUCCESS" "Directory structure set up"
}

# Copy scripts from source to destination
copy_scripts() {
    log "INFO" "Copying scripts"

    # Source directory for scripts
    local source_dir="/image/scripts"

    # Check if source directory exists
    if [ ! -d "${source_dir}" ]; then
        log "WARNING" "Source directory not found: ${source_dir}"
        return 0
    fi

    # Copy scripts
    cp -r "${source_dir}"/* "${PERSISTENCE_SCRIPTS}/"

    # Make scripts executable
    chmod -R 755 "${PERSISTENCE_SCRIPTS}"

    log "SUCCESS" "Scripts copied"
}

# Copy service files
copy_services() {
    log "INFO" "Copying service files"

    # Source directory for services
    local source_dir="/image/services"

    # Check if source directory exists
    if [ ! -d "${source_dir}" ]; then
        log "WARNING" "Source directory not found: ${source_dir}"
        return 0
    fi

    # Copy services
    cp -r "${source_dir}"/* "${PERSISTENCE_SERVICES}/"

    # Copy services to systemd directory
    cp "${PERSISTENCE_SERVICES}"/*.service /usr/lib/systemd/system/

    log "SUCCESS" "Service files copied"
}

# Setup nftables firewall
setup_firewall() {
    log "INFO" "Setting up nftables firewall"

    # Check if nftables script exists
    if [ -f "${PERSISTENCE_SCRIPTS}/nftables-setup.sh" ]; then
        # Make script executable
        chmod +x "${PERSISTENCE_SCRIPTS}/nftables-setup.sh"

        # Run the script
        "${PERSISTENCE_SCRIPTS}/nftables-setup.sh"
    else
        log "WARNING" "nftables setup script not found"
    fi

    log "SUCCESS" "Firewall setup completed"
}

# Setup web UI
setup_web_ui() {
    log "INFO" "Setting up web UI"

    # Check if UI setup script exists
    if [ -f "${PERSISTENCE_SCRIPTS}/UI-Int.sh" ]; then
        # Make script executable
        chmod +x "${PERSISTENCE_SCRIPTS}/UI-Int.sh"

        # Run the script
        "${PERSISTENCE_SCRIPTS}/UI-Int.sh"
    else
        log "WARNING" "UI setup script not found"
    fi

    log "SUCCESS" "Web UI setup completed"
}

# Enable required services
enable_services() {
    log "INFO" "Enabling required services"

    # Enable nftables
    safe_systemctl enable nftables

    # Enable nginx
    safe_systemctl enable nginx

    # Enable libvirtd
    safe_systemctl enable libvirtd

    # Enable persistenceos-api
    safe_systemctl enable persistenceos-api

    # Enable health checker
    if [ -f "/usr/lib/systemd/system/health-checker-daemon.service" ]; then
        safe_systemctl enable health-checker-daemon
    fi

    log "SUCCESS" "Services enabled"
}

# Main function
main() {
    log "INFO" "Starting PersistenceOS configuration (version ${VERSION})"

    # Setup directory structure
    setup_directories

    # Copy scripts
    copy_scripts

    # Copy service files
    copy_services

    # Setup firewall
    setup_firewall

    # Setup web UI
    setup_web_ui

    # Enable services
    enable_services

    log "SUCCESS" "PersistenceOS configuration completed successfully"
}

# Run main function
main
exit $?
```

### 2. install-bootloader.sh (Bootloader Installation)

```bash
#!/bin/bash
#================
# FILE          : install-bootloader.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Configure bootloader for PersistenceOS installation
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Logging function
log() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Log to console with colors
    case "${level}" in
        "INFO")
            echo -e "\033[0;34m[${level}]\033[0m ${message}"
            ;;
        "SUCCESS")
            echo -e "\033[0;32m[${level}]\033[0m ${message}"
            ;;
        "WARNING")
            echo -e "\033[0;33m[${level}]\033[0m ${message}"
            ;;
        "ERROR")
            echo -e "\033[0;31m[${level}]\033[0m ${message}"
            ;;
        "DEBUG")
            echo -e "\033[0;35m[${level}]\033[0m ${message}"
            ;;
        *)
            echo "[${level}] ${message}"
            ;;
    esac
}

# Source common functions
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
source "${SCRIPT_DIR}/util-common.sh"

# Enhanced chroot environment setup with validation and error handling
setup_chroot_environment() {
    local target_dir="${1:-/mnt}"
    log_info "Setting up chroot environment at ${target_dir}"

    # Validate target directory
    if [ ! -d "${target_dir}" ]; then
        log_error "Target directory ${target_dir} does not exist"
        return 1
    fi

    # Check if target has a valid root filesystem
    if [ ! -d "${target_dir}/etc" ] || [ ! -d "${target_dir}/bin" ]; then
        log_warning "Target directory ${target_dir} may not be a valid root filesystem"
    fi

    # Mount virtual filesystems with error handling
    local mount_points=("proc" "sys" "dev" "dev/pts" "run")
    local mount_types=("proc" "sysfs" "devtmpfs" "devpts" "tmpfs")
    local mount_options=("" "" "" "gid=5,mode=620" "")
    local mount_sources=("proc" "sys" "dev" "devpts" "run")

    for i in "${!mount_points[@]}"; do
        local mount_point="${target_dir}/${mount_points[$i]}"
        local mount_type="${mount_types[$i]}"
        local mount_option="${mount_options[$i]}"
        local mount_source="${mount_sources[$i]}"

        # Create mount point if it doesn't exist
        mkdir -p "${mount_point}"

        # Check if already mounted
        if ! mount | grep -q "${mount_point}"; then
            if [ -n "${mount_option}" ]; then
                mount -t "${mount_type}" -o "${mount_option}" "${mount_source}" "${mount_point}" || {
                    log_error "Failed to mount ${mount_source} on ${mount_point}"
                    return 1
                }
            else
                mount -t "${mount_type}" "${mount_source}" "${mount_point}" || {
                    log_error "Failed to mount ${mount_source} on ${mount_point}"
                    return 1
                }
            fi
            log_success "Mounted ${mount_source} on ${mount_point}"
        else
            log_info "${mount_point} already mounted"
        fi
    done

    # Bind mount resolv.conf for network resolution in chroot
    if [ -f /etc/resolv.conf ]; then
        cp -f /etc/resolv.conf "${target_dir}/etc/resolv.conf"
        log_info "Copied resolv.conf to chroot environment"
    fi

    log_success "Chroot environment setup complete"
    return 0
}

# Cleanup chroot environment with proper error handling
cleanup_chroot_environment() {
    local target_dir="${1:-/mnt}"
    log_info "Cleaning up chroot environment at ${target_dir}"

    # Unmount in reverse order to avoid dependency issues
    local mount_points=("run" "dev/pts" "dev" "sys" "proc")

    # Register trap to ensure cleanup even on script failure
    trap 'log_warning "Cleanup interrupted, attempting to continue..."' ERR

    for mount_point in "${mount_points[@]}"; do
        local full_path="${target_dir}/${mount_point}"

        # Check if mounted before attempting to unmount
        if mount | grep -q "${full_path}"; then
            log_info "Unmounting ${full_path}"

            # Try normal unmount first
            if ! umount "${full_path}" 2>/dev/null; then
                # If normal unmount fails, try lazy unmount
                log_warning "Normal unmount failed for ${full_path}, trying lazy unmount"
                umount -l "${full_path}" || {
                    log_error "Failed to unmount ${full_path} (lazy unmount)"
                    # Continue with other unmounts despite failure
                }
            else
                log_success "Unmounted ${full_path}"
            fi
        else
            log_info "${full_path} not mounted, skipping"
        fi
    done

    # Remove trap
    trap - ERR

    # Verify all mounts are cleaned up
    local remaining_mounts=$(mount | grep "${target_dir}" | wc -l)
    if [ "${remaining_mounts}" -gt 0 ]; then
        log_warning "${remaining_mounts} mount points still exist under ${target_dir}"
        mount | grep "${target_dir}" | awk '{print $3}' | while read -r mount_point; do
            log_warning "Still mounted: ${mount_point}"
        done
    else
        log_success "All mount points under ${target_dir} have been unmounted"
    fi

    return 0
}

# Enhanced function to run commands in the target system via chroot with validation and error handling
run_in_target() {
    local cmd="$1"
    local error_msg="${2:-Command failed in target}"
    local success_msg="${3:-Command executed successfully in target}"
    local target_dir="${4:-/mnt}"

    # Validate target directory
    if [ ! -d "${target_dir}" ]; then
        log_error "Target directory ${target_dir} does not exist"
        return 1
    fi

    # Check if target has a valid root filesystem
    if [ ! -d "${target_dir}/etc" ] || [ ! -d "${target_dir}/bin" ]; then
        log_warning "Target directory ${target_dir} may not be a valid root filesystem"
    fi

    # Check if bash exists in the target
    if [ ! -x "${target_dir}/bin/bash" ]; then
        log_error "Bash not found in target system at ${target_dir}/bin/bash"
        return 1
    }

    # Log the command
    log_info "Running in target: $cmd"

    # Execute the command with timeout for safety
    timeout 300 chroot "${target_dir}" /bin/bash -c "$cmd"
    local exit_code=$?

    # Check the result
    if [ $exit_code -eq 0 ]; then
        log_success "${success_msg}"
        return 0
    elif [ $exit_code -eq 124 ]; then
        log_error "Command timed out after 300 seconds: $cmd"
        return 124
    else
        log_error "${error_msg} (exit code: $exit_code)"
        log_debug "Failed command: $cmd"
        return $exit_code
    fi
}

# Function to run commands in the target system with dry-run option
run_in_target_dry_run() {
    local cmd="$1"
    local dry_run="${2:-false}"
    local target_dir="${3:-/mnt}"

    if [ "$dry_run" = "true" ]; then
        log_info "[DRY RUN] Would execute in target: $cmd"
        return 0
    else
        run_in_target "$cmd" "Command failed in target" "Command executed successfully in target" "$target_dir"
        return $?
    fi
}

# Detect boot mode (UEFI or BIOS)
detect_boot_mode() {
    if [ -d /sys/firmware/efi ]; then
        log_info "UEFI boot mode detected"
        echo "uefi"
    else
        log_info "BIOS boot mode detected"
        echo "bios"
    fi
}

# Detect boot UUID with multiple fallback methods
detect_boot_uuid() {
    local target_dir="${1:-/mnt}"
    local boot_uuid=""

    # Method 1: Try to get UUID from /etc/fstab
    if [ -f "${target_dir}/etc/fstab" ]; then
        boot_uuid=$(grep -E '\s/boot\s' "${target_dir}/etc/fstab" | grep -o 'UUID=[a-f0-9\-]*' | cut -d= -f2)
    fi

    # Method 2: If not found, try to get from mounted filesystems
    if [ -z "${boot_uuid}" ]; then
        local boot_device=$(mount | grep "on ${target_dir}/boot " | cut -d' ' -f1)
        if [ -n "${boot_device}" ]; then
            boot_uuid=$(blkid -s UUID -o value "${boot_device}")
        fi
    fi

    # Method 3: If still not found, try to get from root partition
    if [ -z "${boot_uuid}" ]; then
        local root_device=$(mount | grep "on ${target_dir} " | cut -d' ' -f1)
        if [ -n "${root_device}" ]; then
            boot_uuid=$(blkid -s UUID -o value "${root_device}")
        fi
    }

    # Log the result
    if [ -n "${boot_uuid}" ]; then
        log_success "Boot UUID detected: ${boot_uuid}"
    else
        log_warning "Failed to detect boot UUID"
    fi

    echo "${boot_uuid}"
}

# Install GRUB for UEFI
install_grub_uefi() {
    local target_dir="${1:-/mnt}"
    local dry_run="${2:-false}"

    log_info "Installing GRUB for UEFI"

    # Verify EFI directory exists
    if [ ! -d "${target_dir}/boot/efi" ]; then
        log_error "EFI directory not found at ${target_dir}/boot/efi"
        return 1
    fi

    # Install GRUB for UEFI
    run_in_target_dry_run "grub2-install --target=x86_64-efi --efi-directory=/boot/efi --bootloader-id=PersistenceOS --recheck" "${dry_run}" "${target_dir}"
    local result=$?

    if [ ${result} -eq 0 ]; then
        log_success "GRUB installed for UEFI"
    else
        log_error "Failed to install GRUB for UEFI"
    fi

    return ${result}
}

# Install GRUB for BIOS
install_grub_bios() {
    local target_dir="${1:-/mnt}"
    local dry_run="${2:-false}"

    log_info "Installing GRUB for BIOS"

    # Get the target disk
    local target_disk=$(mount | grep "on ${target_dir} " | cut -d' ' -f1 | sed 's/[0-9]*$//')

    if [ -z "${target_disk}" ]; then
        log_error "Failed to detect target disk"
        return 1
    fi

    log_info "Target disk: ${target_disk}"

    # Install GRUB for BIOS
    run_in_target_dry_run "grub2-install --target=i386-pc ${target_disk}" "${dry_run}" "${target_dir}"
    local result=$?

    if [ ${result} -eq 0 ]; then
        log_success "GRUB installed for BIOS"
    else
        log_error "Failed to install GRUB for BIOS"
    fi

    return ${result}
}

# Create GRUB menu entries with MicroOS-specific options
create_grub_menu_entries() {
    local target_dir="${1:-/mnt}"
    local boot_uuid="${2}"
    local dry_run="${3:-false}"

    log_info "Creating GRUB menu entries with MicroOS-specific options"

    # Create custom GRUB entries file
    local grub_custom_file="${target_dir}/etc/grub.d/40_persistenceos"

    if [ "${dry_run}" = "true" ]; then
        log_info "[DRY RUN] Would create custom GRUB entries in ${grub_custom_file}"
    else
        cat > "${grub_custom_file}" << EOF
#!/bin/sh
exec tail -n +3 \$0
# This file provides custom menu entries for PersistenceOS
# It is automatically generated by install-bootloader.sh

menuentry "PersistenceOS" {
    search --no-floppy --fs-uuid --set=root ${boot_uuid}
    linux /boot/vmlinuz root=UUID=${boot_uuid} mitigations=auto quiet splash rd.systemd.show_status=auto rd.udev.log_priority=3
    initrd /boot/initrd
}

menuentry "PersistenceOS (Safe Mode)" {
    search --no-floppy --fs-uuid --set=root ${boot_uuid}
    linux /boot/vmlinuz root=UUID=${boot_uuid} nomodeset systemd.show_status=1 rd.udev.log_priority=3
    initrd /boot/initrd
}

menuentry "PersistenceOS (Transactional Update)" {
    search --no-floppy --fs-uuid --set=root ${boot_uuid}
    linux /boot/vmlinuz root=UUID=${boot_uuid} mitigations=auto quiet splash rd.systemd.show_status=auto rd.udev.log_priority=3 transactional-update
    initrd /boot/initrd
}

menuentry "PersistenceOS (Recovery)" {
    search --no-floppy --fs-uuid --set=root ${boot_uuid}
    linux /boot/vmlinuz root=UUID=${boot_uuid} systemd.unit=rescue.target
    initrd /boot/initrd
}

menuentry "Install PersistenceOS" {
    search --no-floppy --fs-uuid --set=root ${boot_uuid}
    linux /boot/vmlinuz root=UUID=${boot_uuid} persistenceos-install
    initrd /boot/initrd
}
EOF

        # Make the file executable
        chmod +x "${grub_custom_file}"
        log_success "Custom GRUB entries created with MicroOS-specific options"
    fi

    # Note: We don't run grub2-mkconfig here as it's now done in the main install_bootloader function

    return 0
}

# Main bootloader installation function
install_bootloader() {
    local target_dir="${1:-/mnt}"
    local dry_run="${2:-false}"

    log_info "Starting bootloader installation"

    # Check if root filesystem is read-only and remount if necessary
    check_and_remount_root "${target_dir}" || {
        log_error "Failed to handle read-only root filesystem"
        return 1
    }

    # Setup chroot environment
    setup_chroot_environment "${target_dir}" || {
        log_error "Failed to setup chroot environment"
        return 1
    }

    # Detect boot mode
    local boot_mode=$(detect_boot_mode)

    # Detect boot UUID
    local boot_uuid=$(detect_boot_uuid "${target_dir}")

    # Try to install bootloader using transactional-update first
    local install_result=0
    install_bootloader_transactional "${target_dir}" "${dry_run}"
    install_result=$?

    # If transactional-update is not available or failed, fall back to direct installation
    if [ ${install_result} -eq 2 ] || [ ${install_result} -ne 0 ]; then
        log_info "Falling back to direct bootloader installation"

        # Install GRUB based on boot mode
        if [ "${boot_mode}" = "uefi" ]; then
            install_grub_uefi "${target_dir}" "${dry_run}"
            install_result=$?
        else
            install_grub_bios "${target_dir}" "${dry_run}"
            install_result=$?
        fi
    fi

    # Configure for btrfs snapshot support if installation was successful
    if [ ${install_result} -eq 0 ]; then
        configure_snapshot_support "${target_dir}" "${dry_run}"
        local snapshot_result=$?

        if [ ${snapshot_result} -ne 0 ]; then
            log_warning "Failed to configure snapshot support, continuing anyway"
        fi
    fi

    # Configure MicroOS-specific boot parameters if installation was successful
    if [ ${install_result} -eq 0 ]; then
        configure_microos_boot_parameters "${target_dir}" "${dry_run}"
        local params_result=$?

        if [ ${params_result} -ne 0 ]; then
            log_warning "Failed to configure MicroOS-specific boot parameters, continuing anyway"
        fi
    }

    # Create GRUB menu entries if installation was successful
    if [ ${install_result} -eq 0 ]; then
        create_grub_menu_entries "${target_dir}" "${boot_uuid}" "${dry_run}"
        local menu_result=$?

        if [ ${menu_result} -ne 0 ]; then
            log_error "Failed to create GRUB menu entries"
            install_result=${menu_result}
        }
    fi

    # Update GRUB configuration if installation was successful
    if [ ${install_result} -eq 0 ]; then
        log_info "Updating GRUB configuration"
        run_in_target_dry_run "grub2-mkconfig -o /boot/grub2/grub.cfg" "${dry_run}" "${target_dir}"
        local config_result=$?

        if [ ${config_result} -ne 0 ]; then
            log_error "Failed to update GRUB configuration"
            install_result=${config_result}
        }
    fi

    # Cleanup chroot environment
    cleanup_chroot_environment "${target_dir}"

    if [ ${install_result} -eq 0 ]; then
        log_success "Bootloader installation completed successfully"
    else
        log_error "Bootloader installation failed with exit code ${install_result}"
    fi

    return ${install_result}
}

# Configure system settings
configure_system() {
    log "INFO" "Configuring system settings"

    # Set hostname
    echo "persistenceos" > /mnt/etc/hostname

    # Configure network
    cat > /mnt/etc/sysconfig/network/ifcfg-eth0 << EOF
BOOTPROTO='dhcp'
STARTMODE='auto'
EOF

    # Enable services
    run_in_target "systemctl enable sshd"
    run_in_target "systemctl enable nftables"
    run_in_target "systemctl enable nginx"
    run_in_target "systemctl enable libvirtd"
    run_in_target "systemctl enable persistenceos-installer"

    log "SUCCESS" "System settings configured"
}

# Setup overlayfs for root filesystem
setup_overlayfs() {
    log "INFO" "Setting up overlayfs for root filesystem"

    # Create necessary directories
    mkdir -p /mnt/etc/overlayfs.conf.d

    # Configure overlayfs
    cat > /mnt/etc/overlayfs.conf.d/persistenceos.conf << EOF
# PersistenceOS overlayfs configuration
[Directories]
# Directories to exclude from overlayfs
ExcludePaths=/var/lib/persistence
EOF

    log "SUCCESS" "Overlayfs configured"
}

# Setup health monitoring
setup_health_monitoring() {
    log "INFO" "Setting up health monitoring"

    # Create health checker service
    cat > /mnt/usr/lib/systemd/system/health-checker-daemon.service << EOF
[Unit]
Description=PersistenceOS Health Checker Daemon
After=network.target

[Service]
Type=simple
ExecStart=/usr/lib/persistence/scripts/health-checker.sh
Restart=always
RestartSec=60

[Install]
WantedBy=multi-user.target
EOF

    # Enable the service
    run_in_target "systemctl enable health-checker-daemon"

    log "SUCCESS" "Health monitoring set up"
}

# Parse command line arguments
parse_args() {
    local dry_run=false
    local target_dir="/mnt"

    while [[ $# -gt 0 ]]; do
        case "$1" in
            --dry-run)
                dry_run=true
                shift
                ;;
            --target-dir=*)
                target_dir="${1#*=}"
                shift
                ;;
            --target-dir)
                target_dir="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    echo "${dry_run}|${target_dir}"
}

# Show help information
show_help() {
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --dry-run              Run in dry-run mode without making changes"
    echo "  --target-dir=DIR       Specify target directory (default: /mnt)"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --dry-run                   # Run in dry-run mode"
    echo "  $0 --target-dir=/mnt/target    # Use custom target directory"
}

# Main function
main() {
    log_info "Starting bootloader installation (version ${VERSION})"

    # Parse command line arguments
    local args=$(parse_args "$@")
    local dry_run=$(echo "${args}" | cut -d'|' -f1)
    local target_dir=$(echo "${args}" | cut -d'|' -f2)

    log_info "Target directory: ${target_dir}"
    if [ "${dry_run}" = "true" ]; then
        log_info "Running in dry-run mode"
    fi

    # Check if running as root
    if [ "$(id -u)" -ne 0 ]; then
        log_error "This script must be run as root"
        exit 1
    fi

    # Install bootloader
    install_bootloader "${target_dir}" "${dry_run}"
    local result=$?

    # Configure system settings if bootloader installation was successful
    if [ ${result} -eq 0 ]; then
        configure_system "${target_dir}" "${dry_run}"
        result=$?
    fi

    # Setup overlayfs if previous steps were successful
    if [ ${result} -eq 0 ]; then
        setup_overlayfs "${target_dir}" "${dry_run}"
        result=$?
    fi

    # Setup health monitoring if previous steps were successful
    if [ ${result} -eq 0 ]; then
        setup_health_monitoring "${target_dir}" "${dry_run}"
        result=$?
    fi

    if [ ${result} -eq 0 ]; then
        log_success "Bootloader installation completed successfully"
    else
        log_error "Bootloader installation failed with exit code ${result}"
    fi

    return ${result}
}

# Run main function with all arguments
main "$@"
exit $?
```

### 3. persistenceos-installer.service (Installation Service)

```ini
[Unit]
Description=PersistenceOS Installer Service
After=network.target
ConditionPathExists=!/var/lib/persistenceos

[Service]
Type=oneshot
ExecStart=/usr/lib/persistence/scripts/config.sh
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
```

### 4. persistenceos-api.service (API Service)

```ini
[Unit]
Description=PersistenceOS API Service
After=network.target nginx.service
Requires=nginx.service

[Service]
Type=simple
User=root
WorkingDirectory=/usr/lib/persistence/api
ExecStart=/usr/bin/python3.11 -m uvicorn main:app --host 127.0.0.1 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

## Testing PersistenceOS

Since you cannot test PersistenceOS on your current machine, here's a guide for testing it on VMs or other machines:

### 1. VM Testing Setup

1. **Create a VM with the following specifications**:
   - At least 2 CPU cores
   - 4GB RAM minimum (8GB recommended)
   - 20GB disk space minimum
   - Network adapter in bridged mode

2. **Boot from the PersistenceOS ISO**:
   - Use the ISO generated by OBS
   - Select "Installation" from the boot menu

3. **Installation Process**:
   - The installer will automatically partition the disk
   - It will install the system and configure the bootloader
   - After installation, the system will reboot

4. **First Boot**:
   - On first boot, the system will automatically configure itself
   - The persistenceos-installer.service will run config.sh
   - This will set up the web UI, API, and other components

5. **Access the Web UI**:
   - Open a web browser on another machine
   - Navigate to http://VM_IP_ADDRESS:8080
   - Login with username: root, password: linux

### 2. Testing Checklist

1. **System Functionality**:
   - Verify the system boots properly
   - Check that all services are running (`systemctl status nginx libvirtd persistenceos-api`)
   - Verify network connectivity

2. **Web UI Functionality**:
   - Test login functionality
   - Verify dashboard displays system information
   - Test navigation between sections
   - Verify real-time updates of system metrics

3. **Virtualization**:
   - Create a test VM
   - Start, stop, and manage the VM
   - Test VM console access

4. **Storage Management**:
   - Create and manage storage pools
   - Test snapshot functionality
   - Verify storage metrics

5. **Network Configuration**:
   - Test network interface configuration
   - Verify firewall settings
   - Test network diagnostics

### 3. Troubleshooting

If you encounter issues during testing:

1. **Check Logs**:
   - System logs: `journalctl -xe`
   - Nginx logs: `/var/log/nginx/persistenceos-error.log`
   - API logs: `/var/log/persistence/api.log`
   - Installation logs: `/var/log/persistence/common.log`

2. **Common Issues**:
   - **Web UI not accessible**: Check Nginx status and configuration
   - **API errors**: Verify Python dependencies and API service status
   - **VM creation fails**: Check libvirt configuration and permissions
   - **Boot issues**: Verify bootloader installation in install-bootloader.sh

3. **Recovery Options**:
   - Boot into recovery mode from GRUB menu
   - Use transactional-update to rollback to previous snapshot
   - Access system via SSH for remote troubleshooting


## Script Consolidation

### UI Script Consolidation

The UI-related scripts have been consolidated into a single `ui-manager.sh` script with multiple operations. This consolidation provides several benefits:

1. **Reduced Code Duplication**: Eliminates redundant code for directory creation, permission setting, etc.
2. **Consistent Error Handling**: Centralizes error handling and logging for all UI operations
3. **Simplified Maintenance**: Changes to UI structure only need to be updated in one file
4. **Clearer Dependencies**: All UI operations use the same path definitions and utility functions
5. **Easier Debugging**: Consolidated logging makes it easier to trace UI-related issues

The consolidated script replaces the following individual scripts:
- `UI-Sources.sh` → `ui-manager.sh prepare`
- `UI-Extract.sh` → `ui-manager.sh extract`
- `UI-Int.sh` → `ui-manager.sh initialize`
- `UI-Reset.sh` → `ui-manager.sh reset`
- `UI-Version-Check.sh` → `ui-manager.sh check`
- `bundle-ui-assets.sh` → `ui-manager.sh bundle`

A dedicated `ui-migration.sh` script facilitates the transition from the individual scripts to the consolidated script, creating wrapper scripts for backward compatibility and updating service files.

### Migration Strategy

To implement the standardized naming conventions and script consolidation, follow this migration strategy:

1. **Service Files Migration**:
   - Create new service files with standardized names
   - Update references in scripts and configuration
   - Test new service files
   - Remove old service files after successful testing

2. **Scripts Migration**:
   - Create symbolic links from old names to new names
   - Update all references to use new names
   - Test functionality with new names
   - Remove symbolic links after successful testing

3. **Configuration Files Migration**:
   - Create new configuration files with standardized names
   - Update references in scripts and services
   - Test with new configuration files
   - Remove old configuration files after successful testing

4. **UI Scripts Consolidation**:
   - Implement the consolidated `ui-manager.sh` script
   - Run the `ui-migration.sh` script to create wrapper scripts
   - Update service files to use the new consolidated script
   - Test all UI operations thoroughly
   - Remove the individual UI scripts after successful testing

### Service Renaming Map

| Current Name | Standardized Name |
|--------------|-------------------|
| `persistence-nftables-setup.service` | `persistenceos-nftables-setup.service` |
| `persistence-services-init.service` | `persistenceos-services-init.service` |
| `persistence-firewall-setup.service` | `persistenceos-firewall-setup.service` |
| `persistence-post-install.service` | `persistenceos-post-install.service` |
| `persistence-webserver.service` | `persistenceos-webserver.service` |
| `health-checker-daemon.service` | `persistenceos-health-daemon.service` |

### Script Renaming Map

| Current Name | Standardized Name |
|--------------|-------------------|
| `common.sh` | `util-common.sh` |
| `config.sh` | `config-manager.sh` |
| `nftables-setup.sh` | `config-firewall.sh` |
| `editbootinstaller.sh` | `install-bootloader.sh` |
| `images.sh` | `install-post.sh` |
| `persistenceos-install.sh` | `install-system.sh` |
| `persistence-system.sh` | `system-manager.sh` |
| `persistence-storage.sh` | `storage-manager.sh` |
| `persistence-snapshots.sh` | `storage-snapshots.sh` |
| `health-checker.sh` | `system-health.sh` |
| `fix-network.sh` | `network-manager.sh` |
| `UI-Sources.sh` | `ui-prepare.sh` |
| `UI-Int.sh` | `ui-initialize.sh` |
| `UI-Extract.sh` | `ui-extract.sh` |
| `UI-Reset.sh` | `ui-reset.sh` |
| `UI-Version-Check.sh` | `ui-version.sh` |
| `bundle-ui-assets.sh` | `ui-bundle.sh` |
| `update-ui-version.sh` | `ui-update.sh` |
| `pre-build-update.sh` | `build-prepare.sh` |

### API File Renaming Map

| Current Name | Standardized Name |
|--------------|-------------------|
| `system.py` | `system_router.py` |
| `vms.py` | `vm_router.py` |
| `storage.py` | `storage_router.py` |
| `network.py` | `network_router.py` |
| `settings.py` | `settings_router.py` |

### Configuration File Renaming Map

| Current Name | Standardized Name |
|--------------|-------------------|
| `persistenceos-nginx.conf` | `persistenceos.conf` |

## Validation Script

Create a validation script (`validate-naming.sh`) to check for naming convention compliance:

```bash
#!/bin/bash
# validate-naming.sh - Check naming convention compliance

# Check service files
echo "Checking service files..."
for service in /usr/lib/systemd/system/persistence-*.service; do
    if [ -f "$service" ]; then
        echo "WARNING: Non-compliant service name: $service"
        echo "         Should be renamed to: ${service/persistence-/persistenceos-}"
    fi
done

# Check script files
echo "Checking script files..."
# Add script validation logic

# Check configuration files
echo "Checking configuration files..."
# Add configuration validation logic

echo "Naming convention validation complete."
```

## REBUILD PROJECT RECAP

### OBS-Specific Files
- **PersistenceOS.kiwi** - Central configuration file for the OBS build
- **_constraints** - Hardware requirements for OBS build service
- **_service** - OBS service configuration file for automatic tarball generation
- **persistence-os.spec** - RPM specification file that expects Source0: %{name}-%{version}.tar.xz

#### OBS Service System for Tarball Generation

The `_service` file can be configured in different ways to generate source tarballs, depending on your needs:

##### Option 1: Automatic Tarball Generation (Development Builds)

For regular development builds, you can use the OBS service system to automatically generate the source tarball during the build process:

```xml
<services>
  <!-- Use OBS services to automatically create the source tarball -->
  <service name="tar" mode="buildtime">
    <param name="file">persistence-os-6.1.0.tar</param>
    <param name="include">scripts</param>
    <param name="include">services</param>
    <param name="include">tools</param>
    <param name="include">web-ui</param>
    <param name="include">python</param>
    <param name="include">PersistenceOS.kiwi</param>
  </service>
  <service name="recompress" mode="buildtime">
    <param name="file">persistence-os-6.1.0.tar</param>
    <param name="compression">xz</param>
  </service>
</services>
```

This configuration:
1. Uses the `tar` service to automatically create a tarball named `persistence-os-6.1.0.tar` containing all the specified directories
2. Uses the `recompress` service to compress this tarball to `persistence-os-6.1.0.tar.xz`
3. The resulting tarball matches the `Source0: %{name}-%{version}.tar.xz` line in the spec file
4. Eliminates the need to manually create and upload a source tarball

##### Option 2: Controlled Tarball Generation (Release Builds)

For official releases and reproducible builds, a more controlled approach is recommended:

```xml
<services>
  <!-- Use OBS services to create the source tarball, but with manual control -->
  <service name="tar_scm" mode="disabled">
    <param name="scm">tar</param>
    <param name="url">.</param>
    <param name="filename">persistence-os</param>
    <param name="version">6.1.0</param>
    <param name="exclude">.git</param>
    <param name="exclude">_service</param>
    <param name="exclude">_constraints</param>
    <param name="exclude">persistence-os.spec</param>
  </service>
  <service name="recompress" mode="disabled">
    <param name="file">*.tar</param>
    <param name="compression">xz</param>
  </service>
  <service name="set_version" mode="disabled"/>
</services>
```

With this configuration:
1. Services are set to `mode="disabled"`, meaning they won't run automatically during the build
2. You create the tarball manually: `tar -cJf persistence-os-6.1.0.tar.xz scripts services tools web-ui python PersistenceOS.kiwi`
3. You create an `.obsinfo` file with metadata about the tarball:
   ```
   name: persistence-os
   version: 6.1.0
   mtime: 1715061600
   commit: 6.1.0
   ```
4. You upload both the tarball and `.obsinfo` file to OBS:
   ```
   osc add persistence-os-6.1.0.tar.xz
   osc add persistence-os-6.1.0.obsinfo
   osc commit -m "Add manually created tarball with obsinfo"
   ```
5. This gives you more control over exactly what goes into the release tarball
6. Builds become more reproducible since the same tarball is used for all builds

The `.obsinfo` file is crucial when manually uploading tarballs with disabled services, as it provides OBS with the necessary metadata about your source files.

##### Choosing the Right Approach

- **For Development**: Use Option 1 with `mode="buildtime"` for convenience
- **For Releases**: Use Option 2 with `mode="disabled"` for control and reproducibility
- **For CI/CD Pipelines**: Consider a hybrid approach where tarballs are generated in a controlled environment

This flexibility allows you to balance automation and control based on your specific needs.

### Foundation Scripts
- **util-common.sh** - Common functions library used by all scripts
- **config-manager.sh** - Main system configuration script that runs during image creation

### Installation Scripts
- **install-manager.sh** - Consolidated installation script with command-based interface for all installation tasks

### System Management Scripts
- **config-firewall.sh** - Firewall configuration with nftables
- **system-manager.sh** - System management operations
- **storage-manager.sh** - Storage management operations
- **storage-snapshots.sh** - Snapshot management
- **network-manager.sh** - Network configuration
- **system-health.sh** - System health monitoring

### Web UI Management Scripts
- **ui-manager.sh** - Consolidated UI management script with multiple operations
- **ui-migration.sh** - Script to facilitate migration from individual UI scripts

### Service Files
- **persistenceos-installer.service** - Installation service
- **persistenceos-nftables-setup.service** - Firewall setup service
- **persistenceos-services-init.service** - Services initialization
- **persistenceos-firstboot.service** - First boot configuration service
- **persistenceos-api.service** - FastAPI backend service
- **persistenceos-health-daemon.service** - Health checker service

### API Backend Files
- **app.py** - Main FastAPI application entry point
- **persistenceos/package_init.py** - Package initialization file (renamed from __init__.py for OBS compatibility)
- **persistenceos/main.py** - Core FastAPI application module
- **persistenceos/routers/routers_init.py** - Routers package initialization (renamed from __init__.py for OBS compatibility)
- **persistenceos/utils/utils_init.py** - Utils package initialization (renamed from __init__.py for OBS compatibility)
- **routers/system_router.py** - System information endpoints
- **routers/vm_router.py** - VM management endpoints
- **routers/storage_router.py** - Storage management endpoints
- **routers/network_router.py** - Network configuration endpoints
- **routers/settings_router.py** - System settings endpoints

### Web UI Frontend Files
- **persistenceos.conf** - Nginx configuration for the web UI
- **login.html** - Login page
- **index.html** - Main interface with hash-based navigation
- **style.css** - Consolidated stylesheet
- **js/auth.js** - Authentication functionality
- **js/api.js** - API communication
- **js/app.js** - Main application logic

### Build Process Files
- **build-prepare.sh** - Script that runs before the build process
- **ui-manager.sh bundle** - Asset bundling command for UI
- **ui-manager.sh prepare** - UI preparation command for build process
