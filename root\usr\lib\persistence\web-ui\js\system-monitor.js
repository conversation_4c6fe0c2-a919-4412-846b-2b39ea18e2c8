/**
 * PersistenceOS Web UI - System Monitor
 * 
 * This module handles real-time system monitoring.
 * It updates metrics and status indicators via WebSockets or polling.
 */

// Self-executing function to create a module scope
(function() {
  'use strict';

  // Configuration
  const CONFIG = {
    // Polling interval in milliseconds (fallback if WebSockets not available)
    POLLING_INTERVAL: 5000,
    
    // WebSocket endpoint
    WS_ENDPOINT: 'ws://localhost:8000/api/ws/system',
    
    // API endpoints
    API_ENDPOINTS: {
      SYSTEM_STATUS: '/api/system/status',
      CPU_USAGE: '/api/system/metrics/cpu',
      MEMORY_USAGE: '/api/system/metrics/memory',
      DISK_USAGE: '/api/system/metrics/disk',
      NETWORK_USAGE: '/api/system/metrics/network',
      SERVICES: '/api/system/services',
      VM_STATUS: '/api/vms/status'
    },
    
    // Maximum data points to keep in history
    MAX_HISTORY_POINTS: 60
  };

  // WebSocket connection
  let wsConnection = null;

  // Polling interval ID
  let pollingIntervalId = null;

  // Metric history
  const metricHistory = {
    cpu: [],
    memory: [],
    disk: [],
    network: {
      rx: [],
      tx: []
    }
  };

  // System status
  let systemStatus = {
    status: 'unknown',
    uptime: 0,
    load: [0, 0, 0],
    cpu: {
      usage: 0,
      cores: 0
    },
    memory: {
      total: 0,
      used: 0,
      free: 0,
      percent: 0
    },
    disk: {
      total: 0,
      used: 0,
      free: 0,
      percent: 0
    },
    network: {
      interfaces: [],
      rx_rate: 0,
      tx_rate: 0
    },
    services: [],
    vms: {
      total: 0,
      running: 0,
      stopped: 0,
      paused: 0
    }
  };

  // Event listeners
  const eventListeners = {
    'update': [],
    'status-change': [],
    'error': []
  };

  /**
   * Initialize the system monitor
   * Sets up WebSocket connection or polling
   */
  function initialize() {
    console.log('Initializing system monitor');
    
    // Try to establish WebSocket connection
    if (window.WebSocket) {
      connectWebSocket();
    } else {
      console.warn('WebSockets not supported, falling back to polling');
      startPolling();
    }
    
    // Load initial system status
    loadSystemStatus();
    
    console.log('System monitor initialized');
  }

  /**
   * Connect to the WebSocket endpoint
   */
  function connectWebSocket() {
    try {
      wsConnection = new WebSocket(CONFIG.WS_ENDPOINT);
      
      wsConnection.onopen = function() {
        console.log('WebSocket connection established');
        // Stop polling if it was started as a fallback
        stopPolling();
      };
      
      wsConnection.onmessage = function(event) {
        try {
          const data = JSON.parse(event.data);
          handleUpdate(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          triggerEvent('error', { message: 'Error parsing WebSocket message', error });
        }
      };
      
      wsConnection.onerror = function(error) {
        console.error('WebSocket error:', error);
        triggerEvent('error', { message: 'WebSocket error', error });
        // Fall back to polling
        startPolling();
      };
      
      wsConnection.onclose = function() {
        console.log('WebSocket connection closed');
        wsConnection = null;
        // Fall back to polling
        startPolling();
      };
    } catch (error) {
      console.error('Error establishing WebSocket connection:', error);
      triggerEvent('error', { message: 'Error establishing WebSocket connection', error });
      // Fall back to polling
      startPolling();
    }
  }

  /**
   * Start polling for system status updates
   */
  function startPolling() {
    if (pollingIntervalId === null) {
      console.log('Starting polling for system status updates');
      pollingIntervalId = setInterval(loadSystemStatus, CONFIG.POLLING_INTERVAL);
    }
  }

  /**
   * Stop polling for system status updates
   */
  function stopPolling() {
    if (pollingIntervalId !== null) {
      console.log('Stopping polling for system status updates');
      clearInterval(pollingIntervalId);
      pollingIntervalId = null;
    }
  }

  /**
   * Load system status from API
   */
  function loadSystemStatus() {
    API.get(CONFIG.API_ENDPOINTS.SYSTEM_STATUS)
      .then(data => {
        handleUpdate(data);
      })
      .catch(error => {
        console.error('Error loading system status:', error);
        triggerEvent('error', { message: 'Error loading system status', error });
      });
  }

  /**
   * Handle system status update
   * @param {Object} data - The system status data
   */
  function handleUpdate(data) {
    // Check if status has changed
    const previousStatus = systemStatus.status;
    
    // Update system status
    systemStatus = data;
    
    // Update metric history
    updateMetricHistory(data);
    
    // Trigger update event
    triggerEvent('update', systemStatus);
    
    // Trigger status change event if status has changed
    if (previousStatus !== systemStatus.status) {
      triggerEvent('status-change', {
        previous: previousStatus,
        current: systemStatus.status
      });
    }
    
    // Update UI elements
    updateUIElements();
  }

  /**
   * Update metric history with new data
   * @param {Object} data - The system status data
   */
  function updateMetricHistory(data) {
    // Add timestamp to data
    const timestamp = new Date().getTime();
    
    // Update CPU history
    metricHistory.cpu.push({
      timestamp,
      value: data.cpu.usage
    });
    
    // Update memory history
    metricHistory.memory.push({
      timestamp,
      value: data.memory.percent
    });
    
    // Update disk history
    metricHistory.disk.push({
      timestamp,
      value: data.disk.percent
    });
    
    // Update network history
    metricHistory.network.rx.push({
      timestamp,
      value: data.network.rx_rate
    });
    
    metricHistory.network.tx.push({
      timestamp,
      value: data.network.tx_rate
    });
    
    // Trim history to maximum length
    if (metricHistory.cpu.length > CONFIG.MAX_HISTORY_POINTS) {
      metricHistory.cpu.shift();
    }
    
    if (metricHistory.memory.length > CONFIG.MAX_HISTORY_POINTS) {
      metricHistory.memory.shift();
    }
    
    if (metricHistory.disk.length > CONFIG.MAX_HISTORY_POINTS) {
      metricHistory.disk.shift();
    }
    
    if (metricHistory.network.rx.length > CONFIG.MAX_HISTORY_POINTS) {
      metricHistory.network.rx.shift();
    }
    
    if (metricHistory.network.tx.length > CONFIG.MAX_HISTORY_POINTS) {
      metricHistory.network.tx.shift();
    }
  }

  /**
   * Update UI elements with current system status
   */
  function updateUIElements() {
    // Update system status indicator
    updateStatusIndicator();
    
    // Update system metrics
    updateSystemMetrics();
    
    // Update service status
    updateServiceStatus();
    
    // Update VM status
    updateVMStatus();
  }

  /**
   * Update system status indicator
   */
  function updateStatusIndicator() {
    const statusIndicator = document.getElementById('system-status-indicator');
    if (statusIndicator) {
      // Remove all status classes
      statusIndicator.classList.remove('status-healthy', 'status-warning', 'status-critical', 'status-unknown');
      
      // Add appropriate status class
      switch (systemStatus.status) {
        case 'healthy':
          statusIndicator.classList.add('status-healthy');
          statusIndicator.textContent = 'System Healthy';
          break;
        case 'warning':
          statusIndicator.classList.add('status-warning');
          statusIndicator.textContent = 'System Warning';
          break;
        case 'critical':
          statusIndicator.classList.add('status-critical');
          statusIndicator.textContent = 'System Critical';
          break;
        default:
          statusIndicator.classList.add('status-unknown');
          statusIndicator.textContent = 'System Status Unknown';
      }
    }
  }

  /**
   * Update system metrics displays
   */
  function updateSystemMetrics() {
    // Update CPU usage
    const cpuUsage = document.getElementById('cpu-usage-value');
    if (cpuUsage) {
      cpuUsage.textContent = `${systemStatus.cpu.usage.toFixed(1)}%`;
    }
    
    const cpuBar = document.getElementById('cpu-usage-bar');
    if (cpuBar) {
      cpuBar.style.width = `${systemStatus.cpu.usage}%`;
    }
    
    // Update memory usage
    const memoryUsage = document.getElementById('memory-usage-value');
    if (memoryUsage) {
      memoryUsage.textContent = `${systemStatus.memory.percent.toFixed(1)}%`;
    }
    
    const memoryBar = document.getElementById('memory-usage-bar');
    if (memoryBar) {
      memoryBar.style.width = `${systemStatus.memory.percent}%`;
    }
    
    // Update disk usage
    const diskUsage = document.getElementById('disk-usage-value');
    if (diskUsage) {
      diskUsage.textContent = `${systemStatus.disk.percent.toFixed(1)}%`;
    }
    
    const diskBar = document.getElementById('disk-usage-bar');
    if (diskBar) {
      diskBar.style.width = `${systemStatus.disk.percent}%`;
    }
    
    // Update system load
    const systemLoad = document.getElementById('system-load-value');
    if (systemLoad) {
      systemLoad.textContent = systemStatus.load.join(', ');
    }
    
    // Update uptime
    const uptime = document.getElementById('uptime-value');
    if (uptime) {
      uptime.textContent = formatUptime(systemStatus.uptime);
    }
  }

  /**
   * Format uptime in a human-readable format
   * @param {number} seconds - Uptime in seconds
   * @returns {string} Formatted uptime
   */
  function formatUptime(seconds) {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    let result = '';
    if (days > 0) {
      result += `${days}d `;
    }
    if (hours > 0 || days > 0) {
      result += `${hours}h `;
    }
    result += `${minutes}m`;
    
    return result;
  }

  /**
   * Add event listener
   * @param {string} event - The event name
   * @param {Function} callback - The callback function
   */
  function addEventListener(event, callback) {
    if (eventListeners[event]) {
      eventListeners[event].push(callback);
    }
  }

  /**
   * Remove event listener
   * @param {string} event - The event name
   * @param {Function} callback - The callback function
   */
  function removeEventListener(event, callback) {
    if (eventListeners[event]) {
      const index = eventListeners[event].indexOf(callback);
      if (index !== -1) {
        eventListeners[event].splice(index, 1);
      }
    }
  }

  /**
   * Trigger event
   * @param {string} event - The event name
   * @param {*} data - The event data
   */
  function triggerEvent(event, data) {
    if (eventListeners[event]) {
      eventListeners[event].forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${event} event listener:`, error);
        }
      });
    }
  }

  /**
   * Get current system status
   * @returns {Object} The current system status
   */
  function getSystemStatus() {
    return { ...systemStatus };
  }

  /**
   * Get metric history
   * @param {string} metric - The metric name
   * @returns {Array} The metric history
   */
  function getMetricHistory(metric) {
    switch (metric) {
      case 'cpu':
        return [...metricHistory.cpu];
      case 'memory':
        return [...metricHistory.memory];
      case 'disk':
        return [...metricHistory.disk];
      case 'network.rx':
        return [...metricHistory.network.rx];
      case 'network.tx':
        return [...metricHistory.network.tx];
      default:
        return [];
    }
  }

  // Public API
  window.SystemMonitor = {
    initialize: initialize,
    getSystemStatus: getSystemStatus,
    getMetricHistory: getMetricHistory,
    addEventListener: addEventListener,
    removeEventListener: removeEventListener
  };

  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', initialize);
})();
