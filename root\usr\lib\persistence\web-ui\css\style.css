/* PersistenceOS Consolidated Dashboard Styles */

/* Theme Variables */
:root {
    --primary: #0066cc;
    --primary-dark: #004c99;
    --primary-light: #4d94ff;
    --secondary: #6a6e73;
    --success: #3e8635;
    --info: #2b9af3;
    --warning: #f0ab00;
    --danger: #c9190b;
    --light: #f5f5f5;
    --dark: #151515;
    --bg-light: #ffffff;
    --bg-dark: #0a3d62;
    --bg-sidebar: #0a3d62; /* Dark blue like in the image */
    --bg-header: #f8f9fa;
    --bg-card: #ffffff;
    --border: #d2d2d2;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-light: #ffffff;
    --shadow: rgba(0, 0, 0, 0.1);

    /* Layout Variables */
    --sidebar-width: 250px;
    --header-height: 60px;
    --card-border-radius: 8px;
    --content-padding: 20px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light);
    color: var(--text-primary);
}

a {
    text-decoration: none;
    color: var(--primary);
}

/* App Container */
.app-container {
    display: flex;
    width: 100%;
    height: 100%;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    height: 100%;
    background-color: var(--bg-sidebar);
    color: var(--text-light);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.sidebar-header .logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 10px;
}

.sidebar-header .hamburger-menu {
    color: #3498db; /* Light blue for the hamburger icon */
    font-size: 24px;
    margin-bottom: 10px;
}

.sidebar-header h1 {
    color: white;
    font-size: 20px;
    font-weight: 500;
    margin: 0;
}

.sidebar-header .logo-icon {
    color: white;
    font-size: 24px;
    margin-top: 10px;
}

/* Navigation */
.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 20px 0;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 5px;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 15px;
    color: white;
    text-decoration: none;
    transition: background-color 0.2s;
    border-left: 3px solid transparent;
}

.nav-item a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-item.active a {
    background-color: rgba(255, 255, 255, 0.2);
    border-left-color: #3498db; /* Light blue border for active item */
}

.nav-item a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Sidebar Footer */
.sidebar-footer {
    margin-top: auto;
    padding: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* User Info */
.user-info {
    display: flex;
    align-items: center;
    margin-left: 10px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
}

.avatar-text {
    color: white;
    font-weight: bold;
    font-size: 18px;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-details span {
    font-weight: 500;
    color: white;
}

.btn-link {
    color: #4d94ff; /* Light blue for the logout link */
    text-decoration: none;
    background: none;
    border: none;
    padding: 0;
    font-size: 14px;
    cursor: pointer;
    text-align: left;
}

.btn-link:hover {
    text-decoration: underline;
}

/* System Status Indicator */
.system-status {
    display: flex;
    align-items: center;
    color: white;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 10px;
}

.status-indicator.healthy {
    background-color: #4CAF50; /* Green for healthy */
}

.status-indicator.warning {
    background-color: var(--warning);
}

.status-indicator.error {
    background-color: var(--danger);
}

/* Main Content */
.main-content {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f0f2f5; /* Light gray background like TrueNAS Scale */
}

/* Header */
.header {
    height: var(--header-height);
    background-color: var(--bg-light);
    border-bottom: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--content-padding);
    box-shadow: 0 2px 4px var(--shadow);
}

.header h2 {
    font-size: 24px;
    font-weight: 500;
}

/* Bold heading style */
.bold-heading {
    font-weight: 800;
    font-size: 30px;
    color: #0a3d62;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

/* Content Container */
.content-container {
    flex: 1;
    padding: var(--content-padding);
    overflow-y: auto;
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-title {
    flex: 1;
}

.section-actions {
    display: flex;
    gap: 10px;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    width: 100%;
}

/* Cards */
.card {
    background-color: var(--bg-card);
    border-radius: var(--card-border-radius);
    box-shadow: 0 2px 8px var(--shadow);
    overflow: hidden;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.system-overview-card {
    grid-column: span 2;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border);
    background-color: var(--bg-header);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 18px;
    font-weight: 500;
}

.card-actions {
    display: flex;
    gap: 10px;
}

.card-body {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* System Metrics */
.system-metrics {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.metric {
    display: flex;
    align-items: center;
    gap: 10px;
}

.metric-label {
    width: 120px;
    font-weight: 500;
}

.progress-bar {
    flex: 1;
    height: 10px;
    background-color: var(--light);
    border-radius: 5px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: var(--primary);
    border-radius: 5px;
}

.metric-value {
    width: 50px;
    text-align: right;
    font-weight: 500;
}

/* Status Counters */
.status-counters {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
}

.counter {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
}

.counter-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
}

.counter-label {
    font-size: 14px;
    color: var(--text-secondary);
}

.counter.running .counter-value,
.counter.healthy .counter-value {
    color: var(--success);
}

.counter.stopped .counter-value {
    color: var(--secondary);
}

.counter.degraded .counter-value {
    color: var(--warning);
}

.counter.total .counter-value {
    color: var(--primary);
}

/* VM Cards */
.vm-card {
    background-color: var(--bg-light);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow);
    overflow: hidden;
    border-top: 4px solid var(--secondary);
    margin-bottom: 20px;
}

.vm-card.running {
    border-top-color: var(--success);
}

.vm-card.stopped {
    border-top-color: var(--secondary);
}

.vm-card.error {
    border-top-color: var(--danger);
}

.vm-header {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border);
}

.vm-name {
    font-weight: 500;
    font-size: 18px;
}

.vm-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
}

.vm-body {
    padding: 15px;
}

.vm-specs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.spec-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.spec-item i {
    color: var(--text-secondary);
}

.vm-actions {
    display: flex;
    justify-content: space-around;
    padding: 10px 15px;
    background-color: var(--light);
    border-top: 1px solid var(--border);
}

.vm-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 16px;
    padding: 5px;
    border-radius: 4px;
}

.vm-action-btn:hover {
    color: var(--primary);
}

/* VM list grid */
.vm-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

/* Tabs */
.tabs-container {
    background-color: var(--bg-light);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow);
    overflow: hidden;
    margin-bottom: 20px;
}

.tabs {
    display: flex;
    border-bottom: 1px solid var(--border);
    background-color: var(--bg-header);
}

.tab-btn {
    padding: 15px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-secondary);
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    color: var(--primary);
}

.tab-btn.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
}

.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.btn i {
    font-size: 14px;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--light);
    color: var(--text-primary);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background-color: var(--border);
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-icon {
    padding: 8px;
    border-radius: 50%;
}

/* Refresh Buttons */
.refresh-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
}

.refresh-btn:hover {
    color: var(--primary);
}

/* Animation for refresh button */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* FontAwesome built-in spin animation */
.fa-spin {
    animation: spin 1s linear infinite;
}

/* Backup animation for any refresh button */
.refresh-btn.spinning i,
.btn-secondary.spinning i,
.btn-primary.spinning i,
button[id*="refresh"].spinning i,
#check-updates-btn.spinning i {
    animation: spin 1s linear infinite;
}

/* Consistent styling for all refresh buttons */
.refresh-btn,
button[id*="refresh"],
#check-updates-btn {
    cursor: pointer;
    transition: color 0.2s;
}

.refresh-btn:hover,
button[id*="refresh"]:hover,
#check-updates-btn:hover {
    color: var(--primary);
}

/* Ensure all refresh icons have consistent styling */
.fa-sync, .fa-redo {
    transition: transform 0.2s;
}

/* Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border);
}

.data-table th {
    background-color: var(--light);
    font-weight: 500;
}

.data-table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.running {
    background-color: rgba(62, 134, 53, 0.1);
    color: var(--success);
}

.status-badge.stopped {
    background-color: rgba(106, 110, 115, 0.1);
    color: var(--secondary);
}

.status-badge.error {
    background-color: rgba(201, 25, 11, 0.1);
    color: var(--danger);
}

/* Service styles */
.service-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: var(--bg-light);
    border-radius: 8px;
    border: 1px solid var(--border);
}

.service-name {
    font-weight: 500;
    font-size: 16px;
}

.service-status {
    display: flex;
    align-items: center;
    gap: 5px;
}

.service-actions {
    display: flex;
    gap: 10px;
}

/* User styles */
.user-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.user-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: var(--bg-light);
    border-radius: 8px;
    border: 1px solid var(--border);
    position: relative;
    margin-bottom: 5px;
}

.user-name {
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.user-role {
    font-size: 14px;
    color: var(--text-secondary);
    font-style: italic;
}

.user-actions {
    display: flex;
    gap: 10px;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
}

/* Terminal Styles */
.terminal-container {
    background-color: #1e1e1e;
    color: #f0f0f0;
    font-family: 'Courier New', monospace;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 400px;
}

.terminal-output {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.5;
}

.terminal-input-container {
    display: flex;
    padding: 10px;
    border-top: 1px solid #333;
    background-color: #252525;
}

.terminal-prompt {
    color: #4CAF50;
    margin-right: 8px;
    user-select: none;
}

.terminal-input {
    flex: 1;
    background: transparent;
    border: none;
    color: #f0f0f0;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    outline: none;
}

.terminal-input::placeholder {
    color: #888;
}

.terminal-message {
    margin-bottom: 8px;
}

.terminal-command {
    color: #4CAF50;
}

.terminal-response {
    color: #f0f0f0;
}

.terminal-error {
    color: #FF5252;
}

.terminal-controls {
    display: flex;
    gap: 10px;
    margin-left: auto;
}

/* Update Status */
.update-status {
    padding: 10px 0;
}

.update-status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#update-status-text {
    font-weight: 500;
    margin-right: 15px;
}

/* Login Page */
.login-page {
    background: linear-gradient(135deg, var(--bg-dark) 0%, #1e3c72 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.login-container {
    width: 100%;
    max-width: 450px;
    padding: 20px;
}

.login-card {
    background-color: var(--bg-light);
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.login-header {
    background-color: var(--primary);
    color: white;
    padding: 25px 20px;
    text-align: center;
}

.login-header .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 15px;
}

.login-header h1 {
    font-size: 24px;
    margin-bottom: 5px;
}

.login-header .version {
    font-size: 14px;
    opacity: 0.8;
}

.login-form {
    padding: 30px 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="password"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 16px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input[type="checkbox"] {
    margin-right: 8px;
}

.error-message {
    color: var(--danger);
    font-size: 14px;
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(201, 25, 11, 0.1);
    border-radius: 4px;
    display: none;
}

.error-message.visible {
    display: block;
}

.login-footer {
    text-align: center;
    border-top: 1px solid var(--border);
    font-size: 14px;
    color: var(--text-secondary);
    padding: 20px;
}

.login-footer p {
    margin-bottom: 10px;
}

.login-footer .copyright {
    margin-top: 15px;
    font-size: 12px;
}
