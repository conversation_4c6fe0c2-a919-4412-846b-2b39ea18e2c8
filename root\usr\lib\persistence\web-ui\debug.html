<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #0066cc;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0055aa;
        }
    </style>
</head>
<body>
    <h1>PersistenceOS Debug Page</h1>
    
    <div class="card">
        <h2>API Configuration</h2>
        <button onclick="loadApiConfig()">Load API Config</button>
        <pre id="api-config">Click button to load...</pre>
    </div>
    
    <div class="card">
        <h2>System Health</h2>
        <button onclick="checkHealth()">Check Health</button>
        <pre id="health-status">Click button to check...</pre>
    </div>
    
    <div class="card">
        <h2>File System Check</h2>
        <button onclick="checkFiles()">Check Files</button>
        <pre id="file-check">Click button to check...</pre>
    </div>
    
    <script>
        // Load API configuration
        async function loadApiConfig() {
            const configElement = document.getElementById('api-config');
            configElement.textContent = 'Loading...';
            
            try {
                // Try to load from api-config.json first
                let response = await fetch('/api-config.json');
                if (!response.ok) {
                    // If that fails, try the API endpoint
                    response = await fetch('/api/config');
                }
                
                if (response.ok) {
                    const data = await response.json();
                    configElement.textContent = JSON.stringify(data, null, 2);
                } else {
                    configElement.textContent = `Error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                configElement.textContent = `Error: ${error.message}`;
            }
        }
        
        // Check system health
        async function checkHealth() {
            const healthElement = document.getElementById('health-status');
            healthElement.textContent = 'Checking...';
            
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    const data = await response.json();
                    healthElement.textContent = JSON.stringify(data, null, 2);
                } else {
                    healthElement.textContent = `Error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                healthElement.textContent = `Error: ${error.message}`;
            }
        }
        
        // Check files
        async function checkFiles() {
            const filesElement = document.getElementById('file-check');
            filesElement.textContent = 'Checking...';
            
            try {
                const response = await fetch('/api/debug/files');
                if (response.ok) {
                    const data = await response.json();
                    filesElement.textContent = JSON.stringify(data, null, 2);
                } else {
                    filesElement.textContent = `Error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                filesElement.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html> 