#!/bin/bash
set -e
set -o pipefail

# =============================================================================
# KIWI EXECUTION VERIFICATION - CRITICAL FOR DEBUGGING
# =============================================================================
echo "========================================" | tee -a /var/log/kiwi-config-execution.log
echo "🚀 KIWI config.sh EXECUTION STARTED" | tee -a /var/log/kiwi-config-execution.log
echo "📅 Timestamp: $(date)" | tee -a /var/log/kiwi-config-execution.log
echo "📁 Working Directory: $(pwd)" | tee -a /var/log/kiwi-config-execution.log
echo "👤 User: $(whoami)" | tee -a /var/log/kiwi-config-execution.log
echo "🔧 Environment: ${KIWI_BUILD:-runtime}" | tee -a /var/log/kiwi-config-execution.log
echo "📋 Script: $0" | tee -a /var/log/kiwi-config-execution.log
echo "🎯 PID: $$" | tee -a /var/log/kiwi-config-execution.log
echo "========================================" | tee -a /var/log/kiwi-config-execution.log

# Also output to stdout for build log visibility
echo "🚀 PersistenceOS config.sh STARTED - KIWI EXECUTION CONFIRMED"
echo "📅 $(date) - config.sh executing in KIWI preparation stage"

# --- 0. Error Handling and Logging ---
log_info() {
    echo "[INFO] $1" | tee -a /var/log/kiwi-config-execution.log
}

log_error() {
    echo "[ERROR] $1" | tee -a /var/log/kiwi-config-execution.log >&2
    echo "❌ FATAL ERROR in config.sh: $1" | tee -a /var/log/kiwi-config-execution.log
    exit 1
}

# --- 1. Define Paths ---
PERSISTENCE_ROOT="/usr/lib/persistence"
BIN="${PERSISTENCE_ROOT}/bin"
SERVICES="${PERSISTENCE_ROOT}/services"
WEBUI="${PERSISTENCE_ROOT}/web-ui"
LOGDIR="/var/log/persistence"

# --- FUNCTION DEFINITIONS (must be defined before use) ---

# --- Self-contained login.js deployment function ---
deploy_self_contained_login() {
    log_info "Deploying self-contained login.js (no HTML dependencies)..."

    # The self-contained login.js will be handled by setup_static_directory()
    # This function is now a placeholder for any login-specific setup

    # Create minimal login endpoint file for web server compatibility
    mkdir -p "/var/lib/persistence/web-ui" "/usr/lib/persistence/web-ui"

    # Create a minimal login.html that just loads the self-contained login.js
    cat > "/var/lib/persistence/web-ui/login.html" <<'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login</title>
    <link rel="icon" href="img/favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- Self-contained login.js will create the entire page -->
    <script src="js/login.js"></script>
</body>
</html>
EOF

    # Copy to usr location as well
    cp "/var/lib/persistence/web-ui/login.html" "/usr/lib/persistence/web-ui/login.html"

    # Set permissions
    chmod 644 "/var/lib/persistence/web-ui/login.html" "/usr/lib/persistence/web-ui/login.html"

    log_info "✅ Self-contained login deployment complete"
}

# --- Function to create minimal fallback app.js for debugging ---
create_minimal_fallback_app_js() {
    log_info "Creating minimal fallback app.js for debugging file detection issues..."

    cat > "$WEBUI/js/app.js" <<'EOF'
/**
 * FALLBACK app.js - File Detection Issue
 * This is a minimal fallback created because the original app.js was not found.
 * This indicates a build process issue that needs to be resolved.
 */

console.error('🚨 FALLBACK app.js loaded - Original app.js file not found during build');
console.log('📁 Expected app.js locations that were checked:');
console.log('  - app.js (OBS root)');
console.log('  - /usr/src/packages/SOURCES/app.js');
console.log('  - /usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/app.js');
console.log('  - usr/lib/persistence/web-ui/js/app.js');

// Create a simple diagnostic interface
document.addEventListener('DOMContentLoaded', function() {
    const appElement = document.getElementById('app');
    if (appElement) {
        appElement.innerHTML = `
            <div style="padding: 20px; font-family: Arial, sans-serif;">
                <h1 style="color: #d32f2f;">⚠️ PersistenceOS - File Detection Issue</h1>
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3>🔧 Build Process Issue Detected</h3>
                    <p>The original <code>app.js</code> file was not found during the build process.</p>
                    <p>This fallback interface was created to prevent build failure.</p>
                </div>

                <div style="background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>📋 Troubleshooting Steps:</h4>
                    <ol>
                        <li>Verify <code>app.js</code> is uploaded to OBS source files</li>
                        <li>Check OBS build logs for file placement details</li>
                        <li>Ensure file permissions allow reading during build</li>
                        <li>Verify KIWI working directory during build process</li>
                        <li>Check if files are in expected locations:
                            <ul>
                                <li><code>app.js</code> (OBS root)</li>
                                <li><code>usr/lib/persistence/web-ui/js/app.js</code> (nested)</li>
                            </ul>
                        </li>
                    </ol>
                </div>

                <div style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>🎯 Next Steps:</h4>
                    <p>1. <strong>Fix the file detection issue</strong> rather than using embedded code</p>
                    <p>2. <strong>Rebuild the image</strong> after ensuring proper file placement</p>
                    <p>3. <strong>Verify the full dashboard loads</strong> with proper Vue.js components</p>
                </div>

                <button onclick="window.location.reload()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                    🔄 Reload Page
                </button>

                <button onclick="window.location.href='/login.html'" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                    🔙 Back to Login
                </button>
            </div>
        `;
    }
});
EOF

    log_info "✅ Created minimal fallback app.js for debugging file detection issues"

    # Verify the file was created successfully
    if [ -f "$WEBUI/js/app.js" ]; then
        local file_size=$(stat -c%s "$WEBUI/js/app.js" 2>/dev/null || echo "unknown")
        log_info "✅ Fallback app.js created successfully: $WEBUI/js/app.js ($file_size bytes)"
    else
        log_info "❌ Failed to create fallback app.js file"
    fi
}

# --- Function to setup static directory structure properly ---
setup_static_directory() {
    log_info "Setting up static directory structure for web UI with Vue.js support"

    # Create static directory if it doesn't exist
    mkdir -p "$WEBUI/static" || log_info "Warning: Failed to create static directory"
    mkdir -p "$WEBUI/static/css" "$WEBUI/static/js" "$WEBUI/static/img" || log_info "Warning: Failed to create static subdirectories"

    # Create components directory for Vue.js
    mkdir -p "$WEBUI/js/components" || log_info "Warning: Failed to create components directory"

    # Note: Vue.js installation is now handled by the comprehensive search logic below
    # This section has been consolidated with the enhanced JavaScript file detection

    # Install app.js from source
    log_info "Installing app.js Vue application..."

    # Enhanced debugging for OBS file placement with additional search locations
    log_info "=== ENHANCED DEBUGGING FOR OBS FILE PLACEMENT ==="
    log_info "Current working directory: $(pwd)"
    log_info "Current directory contents:"
    ls -la | head -20 | while read line; do log_info "  $line"; done

    # Check for app.js specifically in current directory
    if [ -f "app.js" ]; then
        log_info "✅ app.js found in current directory: $(pwd)/app.js"
        log_info "   File size: $(stat -c%s "app.js" 2>/dev/null || echo "unknown") bytes"
        log_info "   File permissions: $(stat -c%A "app.js" 2>/dev/null || echo "unknown")"
    else
        log_info "❌ app.js NOT found in current directory: $(pwd)/app.js"
    fi

    # Check SOURCES directory
    if [ -d "/usr/src/packages/SOURCES" ]; then
        log_info "SOURCES directory exists: /usr/src/packages/SOURCES"
        log_info "SOURCES directory contents:"
        ls -la /usr/src/packages/SOURCES | head -20 | while read line; do log_info "  $line"; done

        # Check for specific JavaScript files in SOURCES
        for js_file in "app.js" "auth.js" "vue.js" "login.js"; do
            if [ -f "/usr/src/packages/SOURCES/$js_file" ]; then
                file_size=$(stat -c%s "/usr/src/packages/SOURCES/$js_file" 2>/dev/null || echo "unknown")
                file_perms=$(stat -c%A "/usr/src/packages/SOURCES/$js_file" 2>/dev/null || echo "unknown")
                log_info "✅ Found $js_file in SOURCES: $file_size bytes, permissions: $file_perms"
            else
                log_info "❌ Missing $js_file in SOURCES"
            fi
        done

        # Check for @ prefix files (OBS notation)
        for js_file in "@app.js" "@auth.js" "@vue.js" "@login.js"; do
            if [ -f "/usr/src/packages/SOURCES/$js_file" ]; then
                file_size=$(stat -c%s "/usr/src/packages/SOURCES/$js_file" 2>/dev/null || echo "unknown")
                log_info "✅ Found OBS @ prefix file: $js_file ($file_size bytes)"
            fi
        done

        # Check for nested structure in SOURCES
        if [ -d "/usr/src/packages/SOURCES/usr" ]; then
            log_info "Found nested usr directory in SOURCES"
            find /usr/src/packages/SOURCES/usr -name "*.js" -type f 2>/dev/null | while read jsfile; do
                log_info "  Found JS file: $jsfile ($(stat -c%s "$jsfile" 2>/dev/null || echo "unknown") bytes)"
            done
        fi
    else
        log_info "SOURCES directory does not exist: /usr/src/packages/SOURCES"
    fi

    # Check additional OBS-specific locations where files might be placed
    log_info "Checking additional OBS-specific locations:"
    for obs_dir in "/usr/src/packages/BUILD" "/usr/src/packages/BUILDROOT" "/usr/src/packages/RPMS" "/usr/src/packages/SRPMS" "/home/<USER>/rpmbuild/SOURCES"; do
        if [ -d "$obs_dir" ]; then
            log_info "Checking OBS directory: $obs_dir"
            find "$obs_dir" -name "*.js" -type f 2>/dev/null | head -5 | while read jsfile; do
                log_info "  Found JS file: $jsfile ($(stat -c%s "$jsfile" 2>/dev/null || echo "unknown") bytes)"
            done
        else
            log_info "OBS directory does not exist: $obs_dir"
        fi
    done

    # Check for any JavaScript files in the build environment
    log_info "Searching for all .js files in common build locations:"
    for search_dir in "$(pwd)" "/usr/src/packages" "/image" "/tmp" "/var/tmp" "/home/<USER>"; do
        if [ -d "$search_dir" ]; then
            log_info "Searching in: $search_dir"
            find "$search_dir" -name "*.js" -type f 2>/dev/null | head -10 | while read jsfile; do
                log_info "  Found: $jsfile ($(stat -c%s "$jsfile" 2>/dev/null || echo "unknown") bytes)"
            done
        fi
    done
    log_info "=== END ENHANCED DEBUGGING ==="

    # Debug: Check for app.js specifically in various locations including OBS source paths
    log_info "Checking for app.js specifically:"
    for location in "$(pwd)/app.js" "/usr/src/packages/SOURCES/app.js" "/image/app.js" "app.js" "/usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/app.js" "usr/lib/persistence/web-ui/js/app.js"; do
        if [ -f "$location" ]; then
            log_info "  ✅ FOUND: $location (size: $(stat -c%s "$location" 2>/dev/null || echo "unknown") bytes)"
        else
            log_info "  ❌ NOT FOUND: $location"
        fi
    done

    # Ensure target directory exists
    log_info "Ensuring target directory exists: $WEBUI/js/"
    mkdir -p "$WEBUI/js" || log_info "Warning: Failed to create target directory: $WEBUI/js"

    if [ -d "$WEBUI/js" ]; then
        log_info "✅ Target directory confirmed: $WEBUI/js"
        log_info "   Directory permissions: $(ls -ld "$WEBUI/js" 2>/dev/null || echo "unknown")"
    else
        log_info "❌ Target directory does not exist: $WEBUI/js"
    fi

    # Install JavaScript files - Check if already installed by KIWI overlay first
    log_info "Checking for JavaScript files..."

    for js_name in "app" "auth" "vue" "login"; do
        log_info "Processing ${js_name}.js..."

        # Check if file already exists from KIWI overlay (Option B)
        if [ -f "$WEBUI/js/${js_name}.js" ]; then
            file_size=$(stat -c%s "$WEBUI/js/${js_name}.js" 2>/dev/null || echo "unknown")
            log_info "✅ ${js_name}.js already installed by KIWI overlay ($file_size bytes)"
            # Ensure backup copy exists
            mkdir -p "/var/lib/persistence/web-ui/js"
            cp "$WEBUI/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || true
            chmod 644 "$WEBUI/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || true
            continue
        fi

        # Check if file exists in the build root (KIWI overlay files)
        if [ -f "/usr/lib/persistence/web-ui/js/${js_name}.js" ]; then
            file_size=$(stat -c%s "/usr/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || echo "unknown")
            log_info "✅ ${js_name}.js found in KIWI overlay ($file_size bytes)"
            # Files are already in the correct location via KIWI overlay
            # Just ensure backup copy exists
            mkdir -p "/var/lib/persistence/web-ui/js"
            cp "/usr/lib/persistence/web-ui/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || true
            chmod 644 "/usr/lib/persistence/web-ui/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || true
            continue
        fi

        # If not found, try to locate and install from various sources
        JS_LOCATIONS=(
            "/image/${js_name}.js"
            "/usr/src/packages/SOURCES/${js_name}.js"
            "${js_name}.js"
            "$(pwd)/${js_name}.js"
            "/usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/${js_name}.js"
            "usr/lib/persistence/web-ui/js/${js_name}.js"
            "/usr/src/packages/BUILD/${js_name}.js"
            "/usr/src/packages/BUILDROOT/${js_name}.js"
            "/home/<USER>/rpmbuild/SOURCES/${js_name}.js"
            "/home/<USER>/${js_name}.js"
            "./usr/lib/persistence/web-ui/js/${js_name}.js"
            "usr/src/packages/SOURCES/${js_name}.js"
            "/var/tmp/build-root/usr/src/packages/SOURCES/${js_name}.js"
            "/usr/src/packages/SOURCES/@${js_name}.js"
            "/usr/src/packages/SOURCES/@usr/lib/persistence/web-ui/js/${js_name}.js"
        )

        JS_COPIED="false"
        for js_location in "${JS_LOCATIONS[@]}"; do
            if [ -f "$js_location" ]; then
                file_size=$(stat -c%s "$js_location" 2>/dev/null || echo "unknown")
                log_info "✅ ${js_name}.js found in SOURCES: $js_location ($file_size bytes)"
                # Copy to primary location (usr)
                cp "$js_location" "$WEBUI/js/${js_name}.js"
                # Copy to backup location (var)
                mkdir -p "/var/lib/persistence/web-ui/js"
                cp "$js_location" "/var/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || true
                chmod 644 "$WEBUI/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || true
                JS_COPIED="true"
                break
            fi
        done

        if [ "$JS_COPIED" = "false" ]; then
            log_info "⚠️  ${js_name}.js not found in any location"

            # Create fallback for login.js only
            if [ "$js_name" = "login" ]; then
                log_info "Creating self-contained fallback login.js..."
                cat << 'EOF' > "$WEBUI/js/login.js"
/**
 * PersistenceOS Self-Contained Login Script - FALLBACK VERSION
 */

// Configuration
const LOGIN_CONFIG = {
    API_BASE_URL: '/api',
    REDIRECT_URL: '/app.html',
    DEFAULT_USERNAME: 'root',
    DEFAULT_PASSWORD: 'linux',
    DEBUG: true,
    STANDALONE_MODE: true
};

// HTML Layout Generator
const LoginHTML = {
    createFullPage() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #0a4b78 0%, #003366 100%); height: 100vh; display: flex; justify-content: center; align-items: center; color: #333; }
        .login-container { width: 400px; max-width: 95%; }
        .login-card { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.2); }
        .login-header { background: #0066cc; color: white; padding: 30px 20px; text-align: center; }
        .login-header h1 { margin: 0 0 5px 0; font-size: 28px; }
        .login-form { padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; font-size: 14px; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .btn { padding: 12px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; transition: background 0.3s; }
        .btn-primary { background: #0066cc; color: white; }
        .error-message { color: #e74c3c; margin-top: 10px; text-align: center; padding: 8px; border-radius: 4px; background-color: rgba(231, 76, 60, 0.1); }
        .hidden { display: none !important; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>PersistenceOS</h1>
                <p>Version: 6.1.0</p>
            </div>
            <div class="login-form">
                <form id="login-form">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" value="root" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" placeholder="Enter password" required>
                    </div>
                    <div id="login-error" class="error-message hidden">Invalid username or password</div>
                    <div class="form-group">
                        <button type="button" id="login-button" class="btn btn-primary" style="width: 100%;">Log In</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        document.getElementById('login-button').addEventListener('click', async () => {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            if (username === 'root' && password === 'linux') {
                localStorage.setItem('authenticated', 'true');
                window.location.href = '/app.html?from_login=true';
            } else {
                document.getElementById('login-error').classList.remove('hidden');
            }
        });
    </script>
</body>
</html>`;
    }
};

// Initialize
if (LOGIN_CONFIG.STANDALONE_MODE) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            document.open(); document.write(LoginHTML.createFullPage()); document.close();
        });
    } else {
        document.open(); document.write(LoginHTML.createFullPage()); document.close();
    }
}
EOF
                # Copy to backup location (var) - primary is already in usr
                mkdir -p "/var/lib/persistence/web-ui/js"
                cp "$WEBUI/js/login.js" "/var/lib/persistence/web-ui/js/login.js" 2>/dev/null || true
                chmod 644 "$WEBUI/js/login.js" "/var/lib/persistence/web-ui/js/login.js" 2>/dev/null || true
                log_info "✅ Created self-contained fallback login.js"
            fi
        fi
    done

    log_info "✅ Static directory setup complete"
}

# Setup API directory and files
setup_api_directory() {
    log_info "Setting up API directory..."

    # Create API directory
    mkdir -p "$PERSISTENCE_ROOT/api"

    # API files will be created later in the script
    log_info "✅ API directory setup complete"
}

# Deploy self-contained login
deploy_self_contained_login() {
    log_info "Deploying self-contained login..."

    # Login HTML will be created later in the script
    log_info "✅ Self-contained login deployment complete"
}

# Verify dependencies
verify_dependencies() {
    log_info "Verifying PersistenceOS dependencies..."

    # Check if directories exist
    for dir in "$PERSISTENCE_ROOT" "$BIN" "$SERVICES" "$WEBUI"; do
        if [ -d "$dir" ]; then
            log_info "✅ Directory exists: $dir"
        else
            log_info "⚠️  Directory missing: $dir"
        fi
    done

    # Check if JavaScript files exist
    for js_file in "app.js" "auth.js" "vue.js" "login.js"; do
        if [ -f "$WEBUI/js/$js_file" ]; then
            file_size=$(stat -c%s "$WEBUI/js/$js_file" 2>/dev/null || echo "unknown")
            log_info "✅ JavaScript file exists: $js_file ($file_size bytes)"
        else
            log_info "⚠️  JavaScript file missing: $js_file"
        fi
    done

    # Check if API files exist
    for api_file in "run_api.sh" "main.py"; do
        if [ -f "$PERSISTENCE_ROOT/api/$api_file" ]; then
            file_size=$(stat -c%s "$PERSISTENCE_ROOT/api/$api_file" 2>/dev/null || echo "unknown")
            log_info "✅ API file exists: $api_file ($file_size bytes)"
        else
            log_info "⚠️  API file missing: $api_file"
        fi
    done

    # Check if systemd services are installed
    for service_file in "persistenceos-api.service" "persistenceos-core-services.service"; do
        if [ -f "/etc/systemd/system/$service_file" ]; then
            log_info "✅ Systemd service installed: $service_file"
        else
            log_info "⚠️  Systemd service missing: $service_file"
        fi
    done

    # Check if services are enabled
    for service_name in "persistenceos-api" "persistenceos-core-services"; do
        if systemctl is-enabled "$service_name" >/dev/null 2>&1; then
            log_info "✅ Service enabled: $service_name"
        else
            log_info "⚠️  Service not enabled: $service_name"
        fi
    done

    log_info "✅ Dependency verification complete"
}

# --- MAIN EXECUTION ---

# Create directory structure
log_info "Creating PersistenceOS directory structure..."
mkdir -p "$PERSISTENCE_ROOT" "$BIN" "$SERVICES" "$WEBUI" "$LOGDIR"
mkdir -p "$WEBUI/js" "$WEBUI/css" "$WEBUI/img"
mkdir -p "/var/lib/persistence/web-ui/js" "/var/lib/persistence/web-ui/css" "/var/lib/persistence/web-ui/img"

# Get Primary IP for Configuration
PRIMARY_IP=$(hostname -I | awk '{print $1}')

# Deploy Web UI Components
log_info "Deploying web UI components..."

# Setup API directory and copy API files
setup_api_directory

# Deploy self-contained login (minimal HTML wrapper)
deploy_self_contained_login

# Setup static directory structure and copy JavaScript files
setup_static_directory

# Create API config file
cat > "$WEBUI/api-config.json" <<EOF
{
  "host": "${PRIMARY_IP:-localhost}",
  "http_port": 8080,
  "https_port": 8443,
  "api_base_url": "/api",
  "secure_api_base_url": "/api",
  "available_ips": ["${PRIMARY_IP:-127.0.0.1}", "127.0.0.1"]
}
EOF

# --- 2. Setup Repositories (DISABLED - packages should be pre-installed) ---
setup_repositories() {
    log_info "Skipping repository setup - using pre-installed packages from build environment"
    # Note: All required packages should be specified in the KIWI configuration
    # and installed during the package installation phase, not during config.sh
    return 0
}

# --- 3. Install Python Dependencies (DISABLED - packages should be pre-installed) ---
install_python_dependencies() {
    log_info "Skipping Python package installation - using pre-installed packages from build environment"
    # Note: All Python packages should be specified in the KIWI configuration
    # and installed during the package installation phase, not during config.sh
    return 0
}

# --- 4. Verify Dependencies ---
verify_dependencies() {
    log_info "Verifying dependencies..."

    # Check Python (warn but don't fail)
    if command -v python3.11 &>/dev/null; then
        log_info "Python 3.11 is available"
    elif command -v python3 &>/dev/null; then
        log_info "Python 3 is available (fallback)"
    else
        log_info "Python not found - API may not work properly"
    fi

    # Check NetworkManager (warn but don't fail)
    if command -v NetworkManager &>/dev/null; then
        log_info "NetworkManager is available"
    else
        log_info "NetworkManager not found - network management may be limited"
    fi

    # Check for core Python packages via RPM (warn but don't fail)
    for pkg in python311-fastapi python311-uvicorn; do
        if rpm -q "$pkg" &>/dev/null; then
            log_info "Core RPM package $pkg is installed"
        else
            log_info "Core RPM package $pkg is not installed - API may not work properly"
        fi
    done

    # Check for optional Python packages via RPM (warn but don't fail)
    for pkg in python311-psutil python311-netifaces python311-pydantic; do
        if rpm -q "$pkg" &>/dev/null; then
            log_info "Optional RPM package $pkg is installed"
        else
            log_info "Optional RPM package $pkg is not installed (will use fallback if needed)"
        fi
    done

    log_info "Dependency verification completed"
}

# --- MAIN EXECUTION STARTS HERE ---

# --- 3. Ensure Directories ---
mkdir -p "$BIN" "$SERVICES" "$WEBUI" "$LOGDIR" "$PERSISTENCE_ROOT/api" || log_info "Warning: Failed to create some directories"
mkdir -p "$WEBUI/js" "$WEBUI/css" "$WEBUI/img" "$WEBUI/config" || log_info "Warning: Failed to create some web-ui subdirectories"
mkdir -p "/var/lib/persistence/web-ui/js" "/var/lib/persistence/web-ui/css" "/var/lib/persistence/web-ui/img" || log_info "Warning: Failed to create backup web-ui directories"

# --- 4. Check for KIWI Overlay Files (Priority Method) ---
log_info "Checking for KIWI overlay files..."
if [ -d "/usr/lib/persistence/web-ui/js" ] && [ "$(ls -A /usr/lib/persistence/web-ui/js 2>/dev/null)" ]; then
    log_info "✅ KIWI overlay files detected in /usr/lib/persistence/web-ui/js"
    log_info "   Files found: $(ls -la /usr/lib/persistence/web-ui/js/ | grep -E '\.(js)$' | wc -l) JavaScript files"
    # Files are already in place via KIWI overlay, no need to copy
    OVERLAY_FILES_PRESENT=true
else
    log_info "⚠️  No KIWI overlay files found, proceeding with SOURCES detection"
    OVERLAY_FILES_PRESENT=false
fi

# --- 3.1 Diagnostic Information ---
log_info "WEBUI path: $WEBUI"
log_info "Current directory: $(pwd)"

# --- 4. Set Permissions ---
chmod 755 "$BIN" "$SERVICES" "$WEBUI" "$LOGDIR" || log_info "Warning: Failed to set some permissions"
# Ensure web-ui files are readable by everyone
find "$WEBUI" -type f -exec chmod 644 {} \; 2>/dev/null || log_info "No files to set permissions for yet"
find "$WEBUI" -type d -exec chmod 755 {} \; 2>/dev/null || log_info "No directories to set permissions for yet"
chown -R root:root "$PERSISTENCE_ROOT" || log_info "Warning: Failed to set ownership"

# --- 5. Install/Enable NetworkManager ---
if ! systemctl enable NetworkManager; then
    log_info "Warning: Failed to enable NetworkManager"
fi

if ! systemctl start NetworkManager; then
    log_info "Warning: Failed to start NetworkManager (this is normal during build)"
fi

# --- 6. Minimal Network Bring-up ---
if command -v ip &>/dev/null; then
    for iface in $(ip -br link show | awk '{print $1}' | grep -v lo); do
        ip link set dev "$iface" up || log_info "Warning: Failed to bring up interface $iface (this is normal during build)"
    done
fi

# --- 7. Get Primary IP for Configuration ---
PRIMARY_IP=$(hostname -I | awk '{print $1}')
NM_STATUS=$(systemctl is-active NetworkManager 2>/dev/null || echo "unknown")
# Note: Using dynamic welcome message only (see step 15) instead of static MOTD

# --- 8. FastAPI main.py (Python) ---
cat > "$PERSISTENCE_ROOT/api/main.py" <<'EOF'
#!/usr/bin/env python3
"""
PersistenceOS FastAPI Backend
Main entry point for the PersistenceOS web interface and API
"""

import uvicorn
import sys
import os
import logging
from pathlib import Path

# Add the API directory to Python path
api_dir = Path(__file__).parent
sys.path.insert(0, str(api_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("persistenceos")

def main():
    """Main entry point for PersistenceOS API server"""

    # Check if we're in debug mode
    debug_mode = "--debug" in sys.argv

    if debug_mode:
        logger.info("🔧 Starting PersistenceOS API in DEBUG mode")
        log_level = "debug"
        reload = True
    else:
        logger.info("🚀 Starting PersistenceOS API in PRODUCTION mode")
        log_level = "info"
        reload = False

    # Import the FastAPI app
    try:
        from app import app
        logger.info("✅ FastAPI app imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import FastAPI app: {e}")
        sys.exit(1)

    # Get host and port from environment or use defaults
    host = os.getenv("PERSISTENCE_HOST", "0.0.0.0")
    port = int(os.getenv("PERSISTENCE_PORT", "8080"))

    logger.info(f"🌐 Server will bind to {host}:{port}")
    logger.info(f"📁 API directory: {api_dir}")
    logger.info(f"🔄 Auto-reload: {reload}")

    try:
        # Start the Uvicorn server
        uvicorn.run(
            "app:app",
            host=host,
            port=port,
            log_level=log_level,
            reload=reload,
            access_log=True,
            server_header=False,
            date_header=False
        )
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

# --- 8.1. Create app.py (FastAPI application) ---
cat > "$PERSISTENCE_ROOT/api/app.py" <<'EOF'
from fastapi import FastAPI, HTTPException, Request, Depends, status
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse, HTMLResponse, FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
import socket
import os
import logging
from datetime import datetime, timedelta
import json
from typing import Optional, Dict, Any, List

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("persistenceos")

# Define the web UI path
WEBUI_PATH = "/usr/lib/persistence/web-ui"

# Check if web UI exists
if not os.path.exists(WEBUI_PATH):
    logger.error(f"Web UI path does not exist: {WEBUI_PATH}")
elif not os.path.exists(os.path.join(WEBUI_PATH, "login.html")):
    logger.error(f"login.html not found in {WEBUI_PATH}")
else:
    logger.info(f"Web UI path exists: {WEBUI_PATH}")
    # List files for verification
    for item in os.listdir(WEBUI_PATH):
        item_path = os.path.join(WEBUI_PATH, item)
        if os.path.isfile(item_path):
            logger.info(f"Found file: {item} ({os.path.getsize(item_path)} bytes)")
        else:
            logger.info(f"Found directory: {item}")

# Create FastAPI app - do not set root_path here as it's handled by Uvicorn
app = FastAPI(
    title="PersistenceOS API",
    description="PersistenceOS Web UI and API",
    version="6.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Log request middleware for debugging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info(f"Request path: {request.url.path}")
    try:
        response = await call_next(request)
        logger.info(f"Response status: {response.status_code}")
        return response
    except Exception as e:
        logger.error(f"Request error: {str(e)}")
        raise

# OAuth2 password bearer token for auth
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token", auto_error=False)

# Simple in-memory token storage for demo purposes
# In production, use proper token management
active_tokens = {}

# Validate token function
async def validate_token(token: str = Depends(oauth2_scheme)) -> Optional[Dict[str, Any]]:
    if not token:
        return None

    # Check if token exists and is not expired
    if token in active_tokens:
        token_data = active_tokens[token]
        if datetime.now().timestamp() * 1000 < token_data["expires_at"]:
            return token_data["user"]

    return None

# Optional token validation (doesn't raise exceptions)
def validate_token_optional(token: str) -> Optional[Dict[str, Any]]:
    """Validate token without raising exceptions."""
    try:
        if not token:
            return None

        # Check if token exists and is not expired
        if token in active_tokens:
            token_data = active_tokens[token]
            if datetime.now().timestamp() * 1000 < token_data["expires_at"]:
                return token_data["user"]

        return None
    except Exception as e:
        logger.error(f"Error validating token: {e}")
        return None

# Authentication endpoint
@app.post("/api/auth/token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    # Simple authentication for demo
    if form_data.username == "root" and form_data.password == "linux":
        # Create token with expiry time
        token = f"token_{form_data.username}_{int(datetime.now().timestamp())}"
        user_data = {
            "username": form_data.username,
            "display_name": "Administrator",
            "roles": ["admin"]
        }
        expiry = int((datetime.now() + timedelta(hours=1)).timestamp() * 1000)

        # Store token
        active_tokens[token] = {
            "user": user_data,
            "expires_at": expiry
        }

        return {
            "access_token": token,
            "token_type": "bearer",
            "user": user_data,
            "expires_at": expiry
        }

    # Authentication failed
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid username or password",
        headers={"WWW-Authenticate": "Bearer"},
    )

# Token refresh endpoint
@app.post("/api/auth/refresh")
async def refresh_token(current_user: Dict = Depends(validate_token)):
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create new token
    token = f"token_{current_user['username']}_{int(datetime.now().timestamp())}"
    expiry = int((datetime.now() + timedelta(hours=1)).timestamp() * 1000)

    # Store token
    active_tokens[token] = {
        "user": current_user,
        "expires_at": expiry
    }

    return {
        "access_token": token,
        "token_type": "bearer",
        "expires_at": expiry
    }

# System config endpoint
@app.get("/api/config")
async def get_server_config():
    host_ip = socket.gethostbyname(socket.gethostname())

    # Get local IP addresses
    available_ips = []
    try:
        # Primary IP
        if host_ip and host_ip != "127.0.0.1":
            available_ips.append(host_ip)

        # Explicitly add localhost
        if "127.0.0.1" not in available_ips:
            available_ips.append("127.0.0.1")

    except Exception as e:
        logger.error(f"Error getting IPs: {str(e)}")
        available_ips = ["127.0.0.1"]

    config = {
        "host": host_ip,
        "port": 8080,
        "securePort": 8443,
        "apiBaseUrl": f"http://{host_ip}:8080/api",
        "secureApiBaseUrl": f"https://{host_ip}:8443/api",
        "availableIPs": available_ips,
        "version": "6.1.0",
        "generated_at": datetime.now().isoformat()
    }

    # Also update the api-config.json file to match
    try:
        api_config_path = os.path.join(WEBUI_PATH, "api-config.json")
        with open(api_config_path, "w") as f:
            json.dump({
                "host": host_ip,
                "http_port": 8080,
                "https_port": 8443,
                "api_base_url": "/api",
                "secure_api_base_url": "/api",
                "available_ips": available_ips
            }, f, indent=2)
        logger.info(f"Updated api-config.json at {api_config_path}")
    except Exception as e:
        logger.error(f"Failed to update api-config.json: {str(e)}")

    return config

# Health check endpoint
@app.get("/api/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# Root path redirects to login.html
@app.get("/")
async def redirect_to_login():
    logger.info("Root path accessed, redirecting to login.html")
    return RedirectResponse(url="/login.html")

# Direct login.html handler with caching headers
@app.get("/login.html", response_class=HTMLResponse)
async def serve_login(request: Request):
    login_path = os.path.join(WEBUI_PATH, "login.html")
    if os.path.exists(login_path):
        logger.info(f"Serving login.html directly from {login_path}")
        return FileResponse(
            login_path,
            media_type="text/html",
            headers={
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0"
            }
        )
    else:
        logger.error(f"login.html not found at {login_path}")
        raise HTTPException(status_code=404, detail=f"login.html not found at {login_path}")

# App.html endpoint for Vue.js app
@app.get("/app.html", response_class=HTMLResponse)
async def serve_app_html(request: Request):
    """Serve Vue.js app HTML wrapper"""
    # Check authentication from cookies or localStorage (more flexible)
    authenticated = False
    current_user = None

    try:
        # Try to get token from cookie first
        access_token = request.cookies.get("access_token")
        if access_token and access_token.startswith("Bearer "):
            token = access_token.replace("Bearer ", "")
            current_user = validate_token_optional(token)
            if current_user:
                authenticated = True
                logger.info(f"User authenticated via cookie: {current_user['username']}")

        # If no cookie auth, try Authorization header
        if not authenticated:
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
                current_user = validate_token_optional(token)
                if current_user:
                    authenticated = True
                    logger.info(f"User authenticated via header: {current_user['username']}")

        # If still not authenticated, allow access but let frontend handle auth
        if not authenticated:
            logger.info("No authentication found for app.html, serving page for frontend auth check")
            current_user = {"username": "guest", "display_name": "Guest"}
    except Exception as e:
        logger.error(f"Error checking authentication for app.html: {e}")
        current_user = {"username": "guest", "display_name": "Guest"}

    logger.info(f"Serving app.html for user: {current_user['username']}")

    # This is a wrapper HTML that loads the Vue.js app
    app_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>PersistenceOS - Dashboard</title>
        <link rel="stylesheet" href="css/style.css">
        <link rel="icon" href="img/favicon.ico" type="image/x-icon">
        <!-- Font Awesome for icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <!-- Authentication library -->
        <script src="js/auth.js"></script>
        <!-- Vue.js (with CDN fallback) -->
        <script>
            // Try to load local Vue.js first, fallback to CDN
            const vueScript = document.createElement('script');
            vueScript.src = 'js/vue.js';
            vueScript.onerror = function() {
                console.log('Local Vue.js not found, loading from CDN...');
                const cdnScript = document.createElement('script');
                cdnScript.src = 'https://unpkg.com/vue@3/dist/vue.global.prod.js';
                cdnScript.onload = function() {
                    console.log('Vue.js loaded from CDN successfully');
                };
                cdnScript.onerror = function() {
                    console.error('Failed to load Vue.js from CDN');
                    document.getElementById('app').innerHTML = '<div style="text-align: center; padding: 50px; color: red;">Error: Could not load Vue.js framework</div>';
                };
                document.head.appendChild(cdnScript);
            };
            vueScript.onload = function() {
                console.log('Vue.js loaded locally successfully');
            };
            document.head.appendChild(vueScript);
        </script>
        <!-- Main app script -->
        <script src="js/app.js" defer></script>
    </head>
    <body>
        <div id="app">
            <!-- Vue.js app will be mounted here -->
            <div class="loading-indicator">Loading PersistenceOS Dashboard...</div>
        </div>
    </body>
    </html>
    """

    return HTMLResponse(content=app_html, headers={
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0"
    })

# Serve JavaScript and CSS files directly
@app.get("/js/app.js")
async def serve_app_js(request: Request):
    """Serve the Vue.js application script"""
    app_js_path = os.path.join(WEBUI_PATH, "js/app.js")
    if os.path.exists(app_js_path):
        logger.info(f"Serving app.js from {app_js_path}")
        return FileResponse(app_js_path, media_type="application/javascript")
    else:
        logger.error(f"app.js not found at {app_js_path}")
        raise HTTPException(status_code=404, detail="app.js not found")

# Mount static files for CSS, JS, and images
app.mount("/css", StaticFiles(directory=os.path.join(WEBUI_PATH, "css")), name="css")
app.mount("/js", StaticFiles(directory=os.path.join(WEBUI_PATH, "js")), name="js")
app.mount("/img", StaticFiles(directory=os.path.join(WEBUI_PATH, "img")), name="img")

# Mount static directory if it exists
static_dir = os.path.join(WEBUI_PATH, "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")
EOF

# --- 9. Create run_api.sh script ---
cat > "$BIN/run_api.sh" <<'EOF'
#!/bin/bash
# PersistenceOS API Runner Script

set -e

API_DIR="/usr/lib/persistence/api"
MAIN_PY="$API_DIR/main.py"
LOG_FILE="/var/log/persistence/api.log"

# Ensure log directory exists
mkdir -p "$(dirname "$LOG_FILE")"

# Check if main.py exists
if [ ! -f "$MAIN_PY" ]; then
    echo "ERROR: main.py not found at $MAIN_PY" >&2
    exit 1
fi

# Change to API directory
cd "$API_DIR"

# Check for debug flag
if [ "$1" = "--debug" ]; then
    echo "Starting PersistenceOS API in debug mode..."
    exec python3 main.py --debug 2>&1 | tee "$LOG_FILE"
else
    echo "Starting PersistenceOS API in production mode..."
    exec python3 main.py 2>&1 | tee "$LOG_FILE"
fi
EOF

chmod +x "$BIN/run_api.sh"

# --- 10. Create systemd service ---
cat > "$SERVICES/persistenceos-api.service" <<'EOF'
[Unit]
Description=PersistenceOS FastAPI Backend
After=network.target
Wants=network.target

[Service]
Type=exec
User=root
Group=root
WorkingDirectory=/usr/lib/persistence/api
ExecStart=/usr/lib/persistence/bin/run_api.sh
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
Environment=PYTHONPATH=/usr/lib/persistence/api
Environment=PERSISTENCE_HOST=0.0.0.0
Environment=PERSISTENCE_PORT=8080

[Install]
WantedBy=multi-user.target
EOF

# --- 11. Install and enable the service ---
cp "$SERVICES/persistenceos-api.service" /etc/systemd/system/
systemctl daemon-reload
systemctl enable persistenceos-api.service

# --- 12. Create welcome message script ---
cat > "$BIN/welcome.sh" <<'EOF'
#!/bin/bash
# PersistenceOS Welcome Message

PRIMARY_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "unknown")
NM_STATUS=$(systemctl is-active NetworkManager 2>/dev/null || echo "unknown")
API_STATUS=$(systemctl is-active persistenceos-api 2>/dev/null || echo "unknown")

echo "=============================================="
echo "       Welcome to PersistenceOS 6.1"
echo "=============================================="
echo ""
echo "System Status:"
echo "  Primary IP: $PRIMARY_IP"
echo "  NetworkManager: $NM_STATUS"
echo "  API Service: $API_STATUS"
echo ""
echo "Web Interface:"
if [ "$PRIMARY_IP" != "unknown" ] && [ "$PRIMARY_IP" != "" ]; then
    echo "  http://$PRIMARY_IP:8080"
    echo "  https://$PRIMARY_IP:8443 (if SSL configured)"
else
    echo "  http://localhost:8080"
    echo "  https://localhost:8443 (if SSL configured)"
fi
echo ""
echo "Default Login: root / linux"
echo "=============================================="
EOF

chmod +x "$BIN/welcome.sh"

# --- 13. Setup login message ---
cat > /etc/profile.d/persistenceos-welcome.sh <<'EOF'
#!/bin/bash
# Show PersistenceOS welcome message on login
if [ -f /usr/lib/persistence/bin/welcome.sh ]; then
    /usr/lib/persistence/bin/welcome.sh
fi
EOF

chmod +x /etc/profile.d/persistenceos-welcome.sh

# --- 14. Setup repositories and install dependencies (DISABLED) ---
# setup_repositories  # Disabled - packages should be pre-installed
# install_python_dependencies  # Disabled - packages should be pre-installed

# --- 15. Create API config file ---
cat > "$WEBUI/api-config.json" <<EOF
{
  "host": "${PRIMARY_IP:-localhost}",
  "http_port": 8080,
  "https_port": 8443,
  "api_base_url": "/api",
  "secure_api_base_url": "/api",
  "available_ips": ["${PRIMARY_IP:-127.0.0.1}", "127.0.0.1"]
}
EOF

# --- 16. Deploy Web UI Components ---
log_info "Deploying web UI components..."

# Deploy self-contained login (minimal HTML wrapper)
deploy_self_contained_login

# Setup static directory structure and copy JavaScript files (includes self-contained login.js)
setup_static_directory

# --- 17. Verify Setup ---
verify_dependencies

# =============================================================================
# KIWI EXECUTION COMPLETION VERIFICATION
# =============================================================================
echo "========================================" | tee -a /var/log/kiwi-config-execution.log
echo "✅ KIWI config.sh EXECUTION COMPLETED" | tee -a /var/log/kiwi-config-execution.log
echo "📅 Completion Timestamp: $(date)" | tee -a /var/log/kiwi-config-execution.log
echo "📊 Final Status Summary:" | tee -a /var/log/kiwi-config-execution.log
echo "   📁 PersistenceOS directories: $(ls -la /usr/lib/persistence/ 2>/dev/null | wc -l) items" | tee -a /var/log/kiwi-config-execution.log
echo "   🌐 Web UI files: $(ls -la /usr/lib/persistence/web-ui/js/ 2>/dev/null | wc -l) files" | tee -a /var/log/kiwi-config-execution.log
echo "   🔧 Services: $(ls -la /etc/systemd/system/persistenceos-*.service 2>/dev/null | wc -l) services" | tee -a /var/log/kiwi-config-execution.log
echo "   🌍 Primary IP: ${PRIMARY_IP:-127.0.0.1}" | tee -a /var/log/kiwi-config-execution.log
echo "========================================" | tee -a /var/log/kiwi-config-execution.log

# Also output to stdout for build log visibility
echo "✅ PersistenceOS config.sh COMPLETED - KIWI EXECUTION SUCCESSFUL"
echo "📅 $(date) - config.sh finished in KIWI preparation stage"
echo "🎯 PersistenceOS components installed and configured"

echo "[SUCCESS] Minimal PersistenceOS config complete."
