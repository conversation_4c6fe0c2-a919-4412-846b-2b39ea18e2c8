<?xml version="1.0" encoding="utf-8"?>
<image schemaversion="8.3" name="PersistenceOS" displayname="PersistenceOS">
  <description type="system">
    <author>PersistenceOS Team</author>
    <contact><EMAIL></contact>
    <specification>PersistenceOS Installation Image (Micro Leap 6.1 Base)</specification>
  </description>

  <preferences>
    <version>6.1.0</version>
    <packagemanager>zypper</packagemanager>
    <locale>en_US</locale>
    <keytable>us</keytable>
    <timezone>UTC</timezone>
    <rpm-check-signatures>false</rpm-check-signatures>
    <rpm-excludedocs>true</rpm-excludedocs>
    <arch>x86_64</arch>
  </preferences>

  <profiles>
    <profile name="PersistenceOS" description="PersistenceOS Image Profile" arch="x86_64"/>
  </profiles>

  <users>
    <user password="$1$wYJUgpM5$RXMMeASDc035eX.NbYWFl0" home="/root" name="root" groups="root"/>
  </users>

  <bootloader theme="openSUSE" timeout="8" console="serial" arch="x86_64">
    <terminal>console</terminal>
  </bootloader>

  <repository type="rpm-md">
    <source path="obsrepositories:/"/>
  </repository>

  <type image="oem" filesystem="btrfs" firmware="uefi" installiso="true" arch="x86_64">
    <bootloader name="grub2" timeout="10"/>
    <size unit="G">8</size>
    <oemconfig>
      <oem-swap>true</oem-swap>
      <oem-swapsize>1024</oem-swapsize>
    </oemconfig>
    <systemdisk>
      <volume name="root" freespace="2G"/>
      <volume name="home" freespace="1G"/>
      <volume name="var" freespace="1G" copy_on_write="false"/>
    </systemdisk>
  </type>

  <packages type="bootstrap">
    <package name="filesystem"/>
    <package name="glibc-locale-base"/>
  </packages>

  <packages type="image">
    <!-- Core MicroOS patterns -->
    <namedCollection name="base_transactional"/>
    <namedCollection name="micro_defaults"/>
    
    <!-- Virtualization -->
    <namedCollection name="kvm_host"/>
    <package name="libvirt-client"/>
    <package name="libvirt-daemon-qemu"/>
    
    <!-- Storage -->
    <package name="btrfsprogs"/>
    <package name="xfsprogs"/>
    <package name="lvm2"/>
    <package name="snapper"/>
    <package name="snapper-zypp-plugin"/>
    
    <!-- Networking -->
    <package name="NetworkManager"/>
    <package name="firewalld"/>
    
    <!-- Transactional Updates -->
    <package name="transactional-update"/>
    <package name="tukit"/>
    
    <!-- Required for MicroOS -->
    <package name="kiwi-systemdeps-core"/>
    <package name="kiwi-systemdeps-iso-media"/>
    <package name="systemd-default-settings-branding-SLE-Micro"/>
  </packages>

  <config>
    <!-- Fix permissions -->
    <file name="/usr/bin/newgidmap" mode="0755" user="root" group="root" capabilities="cap_setgid=ep"/>
    <file name="/usr/bin/newuidmap" mode="0755" user="root" group="root" capabilities="cap_setuid=ep"/>
    
    <!-- Create required directories -->
    <directory name="/etc/polkit-1/rules.d" mode="755" user="root" group="root"/>
  </config>
</image>
