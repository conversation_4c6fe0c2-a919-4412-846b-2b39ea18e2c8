#!/bin/bash
# Test script for IP detection logic

echo "Testing PersistenceOS IP Detection Logic"
echo "========================================"

# Enhanced IP detection function (copied from config.sh)
get_primary_ip() {
    local found_ips=()
    
    echo "Method 1: Checking common interfaces..."
    # Method 1: Check for specific interfaces commonly used in VMs and cloud environments
    local common_interfaces=("ens3" "eth0" "ens1" "ens5" "enp1s0" "enp0s3" "ens32" "ens33" "ens160")
    for iface in "${common_interfaces[@]}"; do
        if command -v ip &>/dev/null && ip -4 addr show dev "$iface" >/dev/null 2>&1; then
            local iface_ip=$(ip -4 addr show dev "$iface" 2>/dev/null | grep -oP 'inet \K[\d.]+' | head -1)
            
            if [ -n "$iface_ip" ] && [[ "$iface_ip" != "127."* ]] && [[ "$iface_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                found_ips+=("$iface_ip")
                echo "  ✅ Found $iface interface with IP: ${iface_ip}"
                break
            fi
        fi
    done
    
    echo "Method 2: Checking default route interface..."
    # Method 2: Check default route interface if no specific interface was found
    if [ ${#found_ips[@]} -eq 0 ] && command -v ip &>/dev/null; then
        local default_interface=$(ip route show default 2>/dev/null | grep -oP 'dev \K\S+' | head -1)
        if [ -n "$default_interface" ] && [ "$default_interface" != "lo" ]; then
            local ip=$(ip -4 addr show dev "$default_interface" 2>/dev/null | grep -oP 'inet \K[\d.]+' | head -1)
            
            if [ -n "$ip" ] && [[ "$ip" != "127."* ]] && [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                found_ips+=("$ip")
                echo "  ✅ Found IP on default interface $default_interface: $ip"
            fi
        fi
    fi
    
    echo "Method 3: Trying hostname command..."
    # Method 3: Try hostname command as fallback
    if [ ${#found_ips[@]} -eq 0 ] && command -v hostname &>/dev/null; then
        local all_ips=$(hostname -I 2>/dev/null)
        for ip in $all_ips; do
            if [[ "$ip" != "127."* ]] && [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                found_ips+=("$ip")
                echo "  ✅ Found IP via hostname: $ip"
                break
            fi
        done
    fi
    
    echo "Method 4: Using ip route get..."
    # Method 4: Use ip route get as fallback
    if [ ${#found_ips[@]} -eq 0 ] && command -v ip &>/dev/null; then
        local route_ip=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}')
        if [ -n "$route_ip" ] && [[ "$route_ip" != "127."* ]] && [[ "$route_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            found_ips+=("$route_ip")
            echo "  ✅ Found IP via route get: $route_ip"
        fi
    fi
    
    # Last resort - use loopback
    if [ ${#found_ips[@]} -eq 0 ]; then
        found_ips+=("127.0.0.1")
        echo "  ⚠️  No network interfaces found, using loopback"
    fi
    
    echo "${found_ips[0]}"
}

echo ""
echo "Running IP detection..."
PRIMARY_IP=$(get_primary_ip)

echo ""
echo "Results:"
echo "========"
echo "Primary IP: $PRIMARY_IP"

# Additional network information
echo ""
echo "Additional Network Information:"
echo "==============================="

if command -v ip &>/dev/null; then
    echo "Available interfaces:"
    ip -br addr show | while read line; do
        echo "  $line"
    done
    
    echo ""
    echo "Default route:"
    ip route show default 2>/dev/null || echo "  No default route found"
    
    echo ""
    echo "Interface with primary IP:"
    if [ "$PRIMARY_IP" != "127.0.0.1" ]; then
        primary_interface=$(ip -4 addr show 2>/dev/null | grep -B2 "$PRIMARY_IP" | grep -oP '^\d+: \K[^:]+' | head -1)
        if [ -n "$primary_interface" ]; then
            echo "  Interface: $primary_interface"
            link_state=$(ip link show "$primary_interface" 2>/dev/null | grep -oP 'state \K\w+')
            echo "  State: $link_state"
        else
            echo "  Could not determine interface for IP: $PRIMARY_IP"
        fi
    else
        echo "  Using loopback interface"
    fi
else
    echo "ip command not available"
fi

echo ""
echo "Test completed!"
