# PersistenceOS Software Package Reference

This document provides an explanation of all software packages used in PersistenceOS, their functions, and how they interact with our FastAPI-based web UI system.

## Table of Contents
- [Core System Components](#core-system-components)
- [Filesystem and Storage Management](#filesystem-and-storage-management)
- [Virtualization Stack](#virtualization-stack)
- [Web UI and API Stack](#web-ui-and-api-stack)
- [Networking Components](#networking-components)
- [Security Components](#security-components)
- [System Utilities](#system-utilities)
- [Package Interaction Flow](#package-interaction-flow)
- [Web UI Component Flow](#web-ui-component-flow)
- [Visual System Architecture](#visual-system-architecture)

## Core System Components

| Package | Function | Web UI Integration |
|---------|----------|-------------------|
| **patterns-microos-base** | Core MicroOS system patterns including transactional updates | Status displayed in Updates tab |
| **patterns-microos-defaults** | Default MicroOS configuration | Configuration shown in System tab |
| **systemd** | Init system and service manager | Services management through Services tab |
| **dracut** | Tool for generating initramfs images | Used in system updates |
| **grub2** | Bootloader | Boot configuration settings |
| **kernel-default** | Linux kernel | Kernel version displayed in System tab |
| **openssh** | SSH server/client for remote access | SSH service management |
| **health-checker** | Built-in MicroOS health checking system | Health status displayed in Dashboard |
| **health-checker-plugins-MicroOS** | MicroOS-specific health checks | Health alerts and notifications |

## Filesystem and Storage Management

| Package | Function | Web UI Integration |
|---------|----------|-------------------|
| **btrfsprogs** | Tools for btrfs filesystem | System snapshot management |
| **xfsprogs** | Tools for XFS filesystem | VM storage management |
| **e2fsprogs** | Tools for ext2/3/4 filesystems | Additional storage management |
| **lvm2** | Logical Volume Manager tools | LVM creation and management |
| **snapper** | Snapshot manager for btrfs | Snapshot creation/restoration UI |
| **smartmontools** | Disk health monitoring | Disk health status in Storage tab |
| **mdadm** | Software RAID management | RAID configuration and monitoring |
| **parted** | Disk partitioning tool | Storage pool creation and management |
| **cryptsetup** | Disk encryption tools | Encrypted storage management |

## Virtualization Stack

| Package | Function | Web UI Integration |
|---------|----------|-------------------|
| **libvirt-daemon** | Core virtualization service | VM operations backend |
| **libvirt-client** | Client tools for managing VMs | API communication with libvirt |
| **qemu-kvm** | Main hypervisor | VM execution engine |
| **qemu-tools** | Tools for managing VM images | VM image operations |
| **libvirt-daemon-qemu** | QEMU driver for libvirt | VM type support |
| **dnsmasq** | DNS/DHCP for VM networking | VM network configuration |

## Web UI and API Stack

| Package | Function | Web UI Integration |
|---------|----------|-------------------|
| **python311-base** | Core Python 3.11 runtime | Base for all Python components |
| **python311-fastapi** | Web API framework | Main API framework |
| **python311-uvicorn** | ASGI server for FastAPI | Web server |
| **python311-psutil** | System monitoring library | Real-time system metrics |
| **python311-pydantic** | Data validation | API data models |
| **python311-typing-extensions** | Type hints | API type safety |
| **python311-websockets** | WebSocket support | Real-time dashboard updates |
| **python311-aiofiles** | Async file operations | File uploads/downloads |
| **python311-python-multipart** | Form data parsing | API form handling |
| **python311-starlette** | Core FastAPI dependency | ASGI application building |
| **python311-httptools** | HTTP parsing performance | HTTP request optimization |
| **python311-ujson** | Fast JSON handling | API response optimization |
| **python311-sqlalchemy** | SQL database toolkit | User/VM/storage data persistence |
| **python311-jinja2** | Templating engine | Dynamic content generation |
| **python311-passlib** | Password hashing | User authentication security |

## Networking Components

| Package | Function | Web UI Integration |
|---------|----------|-------------------|
| **NetworkManager** | Network connection manager | Network interface management |
| **firewalld** | Dynamic firewall manager | Firewall configuration in Network tab |
| **iproute2** | Modern network tools (ip) | Network configuration backend |
| **iputils** | Network ping tools | Network diagnostics |
| **bind-utils** | DNS utilities (dig, nslookup) | DNS management and diagnostics |
| **netcat-openbsd** | Network testing tool | Advanced network diagnostics |

## Security Components

| Package | Function | Web UI Integration |
|---------|----------|-------------------|
| **policycoreutils** | SELinux policy tools | Security policy management |
| **selinux-policy** | SELinux policy definitions | Security configuration |
| **openssl** | SSL/TLS toolkit | HTTPS certificates for web UI |
| **ca-certificates** | Certificate authority bundle | HTTPS certificate validation |
| **polkit** | Authorization manager | User permissions in web UI |

## System Utilities

| Package | Function | Web UI Integration |
|---------|----------|-------------------|
| **util-linux** | System utilities (lscpu, uptime) | System information display |
| **procps** | Process utilities (free, ps) | Process management in Terminal tab |
| **jq** | JSON processor | API and configuration parsing |
| **figlet** | ASCII art for welcome screen | Welcome screen customization |
| **curl** | Command-line HTTP client | API and system communications |
| **rsync** | File synchronization tool | Backup operations |
| **sudo** | Privilege escalation | Admin operations |
| **timezone** | Time zone data | System time configuration |
| **zypper** | Package manager | Software updates |
| **transactional-update** | Atomic system updates | System update functionality |
| **supportutils** | Support information collection | System diagnostics |

## Package Interaction Flow

This flow diagram illustrates how the different software packages interact within PersistenceOS:

1. **User Interface Layer**
   - The user accesses the FastAPI-powered web UI via a browser
   - FastAPI (python311-fastapi) serves static HTML/CSS/JS files
   - Uvicorn (python311-uvicorn) provides the ASGI server
   - WebSockets (python311-websockets) enable real-time updates

2. **API Layer**
   - API endpoints process requests using FastAPI routes
   - Pydantic (python311-pydantic) validates incoming data
   - Data is processed and formatted using ujson (python311-ujson)
   - Authentication handled via passlib (python311-passlib)

3. **System Monitoring Layer**
   - psutil (python311-psutil) collects system metrics
   - health-checker provides system health status
   - smartmontools monitors disk health
   - procps provides process statistics

4. **Storage Management Layer**
   - btrfsprogs manages system snapshots
   - xfsprogs manages VM storage
   - lvm2 handles logical volume operations
   - snapper provides snapshot management
   - mdadm manages RAID arrays

5. **Virtualization Layer**
   - libvirt-daemon provides the virtualization service
   - qemu-kvm runs the virtual machines
   - libvirt-client communicates with the libvirt API
   - dnsmasq handles VM networking

6. **Network Layer**
   - NetworkManager configures network interfaces
   - firewalld manages firewall rules
   - iproute2 provides advanced networking
   - bind-utils offers DNS management

7. **Security Layer**
   - selinux-policy enforces security policies
   - openssl handles SSL/TLS for HTTPS
   - polkit manages authorization

8. **System Update Layer**
   - transactional-update performs atomic system updates
   - zypper manages package operations
   - dracut generates initramfs images
   - grub2 manages boot configuration

## Web UI Component Flow

This section details how packages connect to specific web UI components, particularly focusing on the `login.html` and `index.html` files.

### Core Web UI Files

| Component | Purpose | Key Packages | Data Flow |
|-----------|---------|--------------|-----------|
| **login.html** | User authentication entry point | python311-fastapi, python311-passlib | User credentials → API → Auth validation → JWT generation → Redirect to index.html |
| **index.html** | Main dashboard and application UI | python311-fastapi, python311-websockets | API data → UI components → Real-time updates via WebSockets |
| **js/auth.js** | Authentication & session management | python311-passlib, python311-pydantic | Handles login/logout flow and session persistence |
| **css/style.css** | UI styling | python311-fastapi (serves static files) | Styling applied to UI components |
| **js/server.js** | Server configuration interface | python311-fastapi, iproute2 (provides IP info) | Server configuration data exposed to frontend |

### Authentication Flow (login.html)

1. **Initial Request:**
   - Browser requests `login.html`
   - python311-uvicorn receives request
   - FastAPI StaticFiles middleware serves the login page

2. **Authentication Process:**
   - User enters credentials in login form
   - js/auth.js collects credentials and sends to `/api/auth/token` endpoint
   - python311-fastapi routes request to auth handler
   - python311-passlib verifies password hash
   - jwt token generated and returned
   - js/auth.js stores token in localStorage/sessionStorage
   - Browser redirects to index.html

3. **Security Layer:**
   - openssl provides SSL/TLS encryption for the connection
   - python311-passlib securely hashes and verifies passwords
   - Authentication process protected by SELinux policies

### Main UI Flow (index.html)

1. **Initial Dashboard Load:**
   - Browser requests `index.html` with auth token
   - FastAPI validates token via auth middleware
   - StaticFiles middleware serves the main UI page
   - js/auth.js verifies local token and establishes session

2. **System Data Collection:**
   - python311-psutil collects real-time system metrics
   - health-checker provides system health status
   - util-linux tools provide system information
   - Data formatted as JSON with python311-ujson

3. **Data Display Components:**
   - Dashboard cards populated with system metrics
   - WebSocket connection established for real-time updates
   - VM list populated from libvirt-client data
   - Storage information from btrfsprogs/xfsprogs/lvm2
   - Network data from NetworkManager/iproute2

4. **User Interactions:**
   - VM operations trigger libvirt-client API calls
   - Storage operations invoke btrfsprogs/xfsprogs commands
   - Network changes apply via NetworkManager
   - System settings modify SELinux/firewalld configurations
   - Terminal tab functionality uses procps data

## Visual System Architecture

```
┌───────────────────────────────────────────────────────────────────────────┐
│                            CLIENT BROWSER                                 │
└───────────────┬────────────────────────────────────────┬─────────────────┘
                │                                        │
                ▼                                        ▼
┌───────────────────────────┐               ┌───────────────────────────────┐
│       login.html          │               │         index.html            │
│  ┌───────────────────┐    │               │  ┌─────────────────────────┐  │
│  │  Authentication   │    │  Redirect     │  │  Dashboard Components    │  │
│  │    Form           │────┼───────────────┼─▶│  - System Overview       │  │
│  └───────────────────┘    │  after login  │  │  - VM Cards              │  │
│  ┌───────────────────┐    │               │  │  - Storage Cards         │  │
│  │  js/auth.js       │    │               │  │  - Network Cards         │  │
│  └───────────────────┘    │               │  └─────────────────────────┘  │
└───────────────┬───────────┘               └─────────────┬─────────────────┘
                │                                         │
                │ POST credentials                        │ GET data & WebSocket
                ▼                                         ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                          FASTAPI (python311-fastapi)                        │
│                                                                             │
│  ┌───────────────────┐    ┌────────────────────┐    ┌────────────────────┐  │
│  │ Authentication    │    │ Static File        │    │ WebSocket          │  │
│  │ Endpoints         │    │ Serving            │    │ Connections        │  │
│  └─────────┬─────────┘    └──────────┬─────────┘    └──────────┬─────────┘  │
│            │                         │                         │            │
│  ┌─────────▼─────────┐    ┌──────────▼─────────┐    ┌──────────▼─────────┐  │
│  │ python311-passlib │    │ python311-aiofiles │    │ python311-websocket│  │
│  └─────────┬─────────┘    └─────────────────────┘    └─────────────────────┘ │
│            │                                                                 │
│  ┌─────────▼─────────┐    ┌─────────────────────┐    ┌─────────────────────┐ │
│  │ python311-        │    │ python311-          │    │ python311-          │ │
│  │ sqlalchemy        │    │ pydantic           │    │ ujson               │ │
│  └─────────┬─────────┘    └─────────────────────┘    └─────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
                │
                ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                         SYSTEM INTEGRATION LAYER                            │
│                                                                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌────────────┐ │
│  │ Virtualization  │ │ Storage         │ │ Network         │ │ Security   │ │
│  │ libvirt-daemon  │ │ btrfsprogs      │ │ NetworkManager  │ │ SELinux    │ │
│  │ libvirt-client  │ │ xfsprogs        │ │ iproute2        │ │ openssl    │ │
│  │ qemu-kvm        │ │ lvm2            │ │ firewalld       │ │ polkit     │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ └────────────┘ │
│                                                                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌────────────┐ │
│  │ System Info     │ │ System Updates  │ │ Health Checks   │ │ Utilities  │ │
│  │ util-linux      │ │ transactional-  │ │ health-checker  │ │ procps     │ │
│  │ psutil          │ │ update          │ │ smartmontools   │ │ jq         │ │
│  │ figlet          │ │ zypper          │ │                 │ │ sudo       │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ └────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────┐
│                         SYSTEM CONSOLE & LOGIN                              │
│                                                                             │
│  ┌─────────────────────────────────────────────────────────────────────┐    │
│  │                         MOTD WELCOME SCREEN                         │    │
│  │                                                                     │    │
│  │  ┌─────────────────┐                                                │    │
│  │  │ ASCII Logo      │ PersistenceOS 6.1                             │    │
│  │  │ (figlet)        │ Advanced Storage Management System            │    │
│  │  └─────────────────┘                                                │    │
│  │                                                                     │    │
│  │  System Information:                                                │    │
│  │  - Hostname: persistence-server                                     │    │
│  │  - Kernel: 5.14.21-150400.24.76-default                            │    │
│  │  - Uptime: 10 days, 4 hours                                         │    │
│  │  - CPU: Intel Xeon ...                                              │    │
│  │                                                                     │    │
│  │  Network Interfaces:                                                │    │
│  │  - eth0: UP - *************                                         │    │
│  │  - virbr0: UP - *************                                       │    │
│  │                                                                     │    │
│  │  WEB INTERFACE ACCESS:                                              │    │
│  │  - HTTP: http://*************:8080                                  │    │
│  │  - HTTPS: https://*************:8443 (secure)                       │    │
│  │                                                                     │    │
│  └─────────────────────────────────────────────────────────────────────┘    │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Post-Installation Welcome Screen (MOTD)

The MOTD (Message of the Day) welcome screen is a critical component of PersistenceOS that appears after installation and on every console login. It provides essential system information and web UI access details.

### Components and Package Integration

| Component | Function | Key Packages | Integration Points |
|-----------|----------|--------------|-------------------|
| **ASCII Logo** | Branded visual identity | figlet | Generated during installation and login |
| **System Information** | Hardware/OS details | util-linux, procps | Real-time system metrics |
| **Network Information** | Connection details | iproute2, NetworkManager | Dynamic IP detection |
| **Web UI Access** | Access URLs | config.sh scripts | Links to FastAPI web interface |

### MOTD Flow and Generation

1. **Initial Setup:**
   - During installation, persistenceos-core-services.service configures the welcome screen
   - ASCII logo created using figlet package
   - /etc/motd file created with system information template

2. **Information Collection:**
   - System data collected using:
     - util-linux tools (lscpu, uptime) for hardware information
     - procps for memory statistics
     - iproute2 for network interface detection
   - Network IP addresses dynamically detected using iproute2

3. **MOTD Update Process:**
   - persistenceos-welcome.service updates the MOTD periodically
   - persistenceos-welcome.timer schedules updates every minute
   - Network status refreshed to show real-time information
   - WebSocket server status checked and displayed

4. **Integration Points:**
   - System boots → persistenceos-core-services.service runs
   - config.sh script generates welcome screen with figlet
   - /etc/issue and /etc/motd populated with system information
   - persistenceos-welcome.service updates data periodically
   - User logs in → Login shell shows welcome screen via /etc/profile hook

5. **User Experience:**
   - User sees ASCII PersistenceOS logo on login
   - Current system metrics displayed (hostname, kernel, uptime)
   - Network interfaces and IP addresses shown
   - Web UI access URLs prominently displayed
   - Health status indicators provided

### Technology Integration

The welcome screen integrates with multiple system components:

```
┌───────────────────────┐
│ MOTD Generation       │
│                       │     ┌────────────────────┐
│  ┌─────────────────┐  │     │  System Services   │
│  │ config.sh       │◄─┼─────┤                    │
│  │ scripts         │  │     │ - core-services    │
│  └────────┬────────┘  │     │ - welcome service  │
│           │           │     │ - welcome timer    │
│  ┌────────▼────────┐  │     └────────────────────┘
│  │ figlet          │  │
│  │ (ASCII art)     │  │     ┌────────────────────┐
│  └────────┬────────┘  │     │  Network Services  │
│           │           │     │                    │
│  ┌────────▼────────┐  │     │ - NetworkManager  │
│  │ System Data     │◄─┼─────┤ - FastAPI status  │
│  │ Collection      │  │     │ - IP detection    │
│  └────────┬────────┘  │     └────────────────────┘
│           │           │
│  ┌────────▼────────┐  │     ┌────────────────────┐
│  │ /etc/motd       │  │     │  Login Process     │
│  │ /etc/issue      │◄─┼─────┤                    │
│  └─────────────────┘  │     │ - /etc/profile     │
└───────────────────────┘     │ - getty service    │
                              └────────────────────┘
```

### Example MOTD Output

```
 ____               _     _                      ___  ____  
|  _ \ ___ _ __ ___(_)___| |_ ___ _ __   ___ ___|_ _|/ ___| 
| |_) / _ \ '__/ __| / __| __/ _ \ '_ \ / __/ _ \| | \___ \ 
|  __/  __/ |  \__ \ \__ \ ||  __/ | | | (_|  __/| |  ___) |
|_|   \___|_|  |___/_|___/\__\___|_| |_|\___\___|___||____/ 

PersistenceOS - Advanced Storage Management and Snapshot Functionality

System Information:
Hostname: persistence-server
Kernel: 5.14.21-150400.24.76-default
Uptime: 10 days, 4 hours
CPU: Intel Xeon E5-2680 v4 @ 2.40GHz
Memory: 4.0G/16.0G
Storage: 150G/500G

Network Interfaces:
Interface: eth0 - State: UP - IP: *************
Interface: virbr0 - State: UP - IP: *************

WEB INTERFACE ACCESS (Direct FastAPI):
HTTP:  http://*************:8080
HTTPS: https://*************:8443 (secure connection)

Note: The web interface uses a self-signed certificate.
Default login: root / linux
Have a lot of fun...
```

The welcome screen serves as both a functional system information display and a guide to accessing the web UI, creating a seamless bridge between the console login experience and the web-based management interface.

### Example User Journey Through the System

#### Login Process
1. User navigates to PersistenceOS web UI in browser
2. Browser loads `login.html` served by FastAPI
3. Login form appears with fields for username/password
4. User enters credentials and submits form
5. js/auth.js sends credentials to `/api/auth/token` endpoint
6. python311-fastapi receives request
7. python311-passlib validates credentials 
8. Upon success, a token is generated and returned
9. js/auth.js stores token in browser storage
10. User is redirected to `index.html`

#### Dashboard View (index.html)
1. Browser requests `index.html` with auth token
2. FastAPI validates token and serves the page
3. Dashboard initializes and establishes WebSocket connection
4. Initial data loaded:
   - System info (via python311-psutil + util-linux)
   - VM status (via libvirt-client)
   - Storage status (via btrfsprogs/xfsprogs) 
   - Network status (via NetworkManager/iproute2)
5. Dashboard components display real-time data
6. Continuous updates arrive via WebSockets

#### VM Management
1. User clicks "Create VM" in web UI
2. Modal form appears with VM configuration options
3. User submits VM creation request
4. Request validated by python311-pydantic
5. API endpoint processes request and calls libvirt-client
6. libvirt-daemon creates VM using qemu-kvm
7. Storage allocated using xfsprogs
8. Network configured using dnsmasq
9. Real-time status updates sent via WebSocket
10. VM appears in dashboard with "Creating" status
11. Status updates to "Running" when complete

This comprehensive flow illustrates how each software package contributes to the seamless operation of the PersistenceOS web UI, from initial login through complex system management tasks.

### Example Workflows

#### VM Creation Workflow
1. User requests VM creation via web UI
2. FastAPI endpoint receives request and validates with Pydantic
3. Python code uses libvirt-client to communicate with libvirt-daemon
4. qemu-kvm creates the VM with storage managed by xfsprogs
5. dnsmasq configures VM networking
6. Real-time updates sent to UI via WebSockets

#### Storage Snapshot Workflow
1. User initiates snapshot via web UI
2. FastAPI endpoint processes request
3. snapper or btrfsprogs creates the snapshot
4. UI is updated with new snapshot information
5. Snapshot data is persisted using SQLAlchemy

#### System Update Workflow
1. User initiates update via web UI
2. transactional-update is called to create update snapshot
3. zypper performs package operations
4. grub2 and dracut update boot configuration
5. User is prompted to reboot to apply changes

This interaction model demonstrates how PersistenceOS integrates multiple components into a cohesive system administration platform with a modern web UI frontend. 