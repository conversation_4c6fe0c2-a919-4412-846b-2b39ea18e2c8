/**
 * FILE          : error-handlers.js
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : Centralized error handling
 */

/**
 * Error handling module for PersistenceOS
 * Provides centralized error handling, logging, and user notifications
 */
const ErrorHandler = (function() {
    // Error severity levels
    const SEVERITY = {
        INFO: 'info',
        WARNING: 'warning',
        ERROR: 'error',
        CRITICAL: 'critical'
    };
    
    // Toast container element
    let toastContainer = null;
    
    /**
     * Initializes the error handler
     * Creates toast container if it doesn't exist
     */
    function init() {
        // Create toast container if it doesn't exist
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container top-right';
            document.body.appendChild(toastContainer);
        }
        
        // Set up global error handling
        window.addEventListener('error', handleGlobalError);
        window.addEventListener('unhandledrejection', handleUnhandledRejection);
    }
    
    /**
     * Handles global JavaScript errors
     * @param {ErrorEvent} event - The error event
     */
    function handleGlobalError(event) {
        const { message, filename, lineno, colno, error } = event;
        
        // Log error details
        console.error('Global error:', {
            message,
            filename,
            lineno,
            colno,
            stack: error ? error.stack : null
        });
        
        // Show error notification to user
        showErrorNotification('An unexpected error occurred', message);
        
        // Prevent default browser error handling
        event.preventDefault();
    }
    
    /**
     * Handles unhandled promise rejections
     * @param {PromiseRejectionEvent} event - The rejection event
     */
    function handleUnhandledRejection(event) {
        const reason = event.reason;
        
        // Log rejection details
        console.error('Unhandled promise rejection:', reason);
        
        // Show error notification to user
        showErrorNotification(
            'Operation failed',
            reason instanceof Error ? reason.message : String(reason)
        );
        
        // Prevent default browser error handling
        event.preventDefault();
    }
    
    /**
     * Handles API errors
     * @param {Error} error - The error object
     * @param {string} context - The context where the error occurred
     * @param {Object} options - Additional options
     * @returns {void}
     */
    function handleApiError(error, context = 'API request', options = {}) {
        // Default options
        const defaultOptions = {
            showNotification: true,
            severity: SEVERITY.ERROR,
            retry: null
        };
        
        const config = { ...defaultOptions, ...options };
        
        // Log error with context
        console.error(`${context} error:`, error);
        
        // Extract error message
        let errorMessage = error.message || 'Unknown error';
        
        // Handle specific error types
        if (errorMessage.includes('Authentication failed') || errorMessage.includes('Authentication required')) {
            // Authentication errors
            if (config.showNotification) {
                showErrorNotification('Authentication Error', 'Your session has expired. Please log in again.');
            }
            
            // Redirect to login page after a short delay
            setTimeout(() => {
                window.location.href = '/login.html?session_expired=true';
            }, 2000);
            
            return;
        } else if (errorMessage.includes('Network Error') || errorMessage.includes('Failed to fetch')) {
            // Network errors
            errorMessage = 'Network connection error. Please check your internet connection.';
            
            // Add retry button if retry function provided
            if (config.retry && typeof config.retry === 'function') {
                if (config.showNotification) {
                    showErrorNotification('Connection Error', errorMessage, {
                        actionText: 'Retry',
                        actionCallback: config.retry
                    });
                }
                return;
            }
        } else if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
            // Timeout errors
            errorMessage = 'The request timed out. Please try again.';
        }
        
        // Show notification if enabled
        if (config.showNotification) {
            showErrorNotification(context + ' Error', errorMessage);
        }
    }
    
    /**
     * Shows an error notification to the user
     * @param {string} title - The notification title
     * @param {string} message - The notification message
     * @param {Object} options - Additional options
     */
    function showErrorNotification(title, message, options = {}) {
        showNotification(title, message, 'error', options);
    }
    
    /**
     * Shows a success notification to the user
     * @param {string} title - The notification title
     * @param {string} message - The notification message
     * @param {Object} options - Additional options
     */
    function showSuccessNotification(title, message, options = {}) {
        showNotification(title, message, 'success', options);
    }
    
    /**
     * Shows a warning notification to the user
     * @param {string} title - The notification title
     * @param {string} message - The notification message
     * @param {Object} options - Additional options
     */
    function showWarningNotification(title, message, options = {}) {
        showNotification(title, message, 'warning', options);
    }
    
    /**
     * Shows an info notification to the user
     * @param {string} title - The notification title
     * @param {string} message - The notification message
     * @param {Object} options - Additional options
     */
    function showInfoNotification(title, message, options = {}) {
        showNotification(title, message, 'info', options);
    }
    
    /**
     * Shows a notification to the user
     * @param {string} title - The notification title
     * @param {string} message - The notification message
     * @param {string} type - The notification type (success, error, warning, info)
     * @param {Object} options - Additional options
     */
    function showNotification(title, message, type = 'info', options = {}) {
        // Default options
        const defaultOptions = {
            duration: 5000,
            actionText: null,
            actionCallback: null
        };
        
        const config = { ...defaultOptions, ...options };
        
        // Create toast container if it doesn't exist
        if (!toastContainer) {
            init();
        }
        
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        
        // Set toast content
        toast.innerHTML = `
            <div class="toast-header">
                <i class="toast-icon fas ${getIconForType(type)}"></i>
                <strong class="toast-title">${title}</strong>
                <button type="button" class="toast-close" aria-label="Close">&times;</button>
            </div>
            <div class="toast-body">
                ${message}
                ${config.actionText ? `<button class="btn btn-sm btn-${type} mt-2">${config.actionText}</button>` : ''}
            </div>
        `;
        
        // Add to container
        toastContainer.appendChild(toast);
        
        // Add event listeners
        const closeButton = toast.querySelector('.toast-close');
        closeButton.addEventListener('click', () => {
            removeToast(toast);
        });
        
        // Add action button event listener if provided
        if (config.actionText && config.actionCallback) {
            const actionButton = toast.querySelector('.btn');
            actionButton.addEventListener('click', () => {
                config.actionCallback();
                removeToast(toast);
            });
        }
        
        // Show toast with animation
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);
        
        // Auto-remove after duration
        if (config.duration > 0) {
            setTimeout(() => {
                removeToast(toast);
            }, config.duration);
        }
    }
    
    /**
     * Removes a toast notification with animation
     * @param {HTMLElement} toast - The toast element to remove
     */
    function removeToast(toast) {
        // Remove show class to trigger fade out animation
        toast.classList.remove('show');
        
        // Remove from DOM after animation completes
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
    
    /**
     * Gets the appropriate icon class for a notification type
     * @param {string} type - The notification type
     * @returns {string} - The icon class
     */
    function getIconForType(type) {
        switch (type) {
            case 'success':
                return 'fa-check-circle';
            case 'error':
                return 'fa-exclamation-circle';
            case 'warning':
                return 'fa-exclamation-triangle';
            case 'info':
            default:
                return 'fa-info-circle';
        }
    }
    
    /**
     * Handles form validation errors
     * @param {HTMLFormElement} form - The form element
     * @param {Object} errors - The validation errors
     */
    function handleFormValidationErrors(form, errors) {
        // Reset previous validation messages
        const validationMessages = form.querySelectorAll('.validation-message');
        validationMessages.forEach(message => {
            message.style.display = 'none';
        });
        
        // Reset invalid classes
        const formControls = form.querySelectorAll('.form-control');
        formControls.forEach(control => {
            control.classList.remove('is-invalid');
        });
        
        // Display new validation messages
        Object.keys(errors).forEach(field => {
            const control = form.querySelector(`[name="${field}"]`);
            if (control) {
                // Add invalid class to control
                control.classList.add('is-invalid');
                
                // Find or create validation message element
                let messageElement = form.querySelector(`#${field}-error`);
                if (!messageElement) {
                    messageElement = document.createElement('div');
                    messageElement.id = `${field}-error`;
                    messageElement.className = 'validation-message';
                    control.parentNode.appendChild(messageElement);
                }
                
                // Set message text and display
                messageElement.textContent = errors[field];
                messageElement.style.display = 'block';
            }
        });
        
        // Focus first invalid field
        const firstInvalid = form.querySelector('.is-invalid');
        if (firstInvalid) {
            firstInvalid.focus();
        }
    }
    
    // Public API
    return {
        init,
        handleApiError,
        handleFormValidationErrors,
        showErrorNotification,
        showSuccessNotification,
        showWarningNotification,
        showInfoNotification,
        SEVERITY
    };
})();

// Initialize error handler when DOM is loaded
document.addEventListener('DOMContentLoaded', ErrorHandler.init);

// Export for use in other modules
window.ErrorHandler = ErrorHandler;
