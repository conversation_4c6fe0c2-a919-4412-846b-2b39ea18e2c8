/**
 * PersistenceOS Web UI - Dashboard Widgets
 * 
 * This module handles dashboard widget rendering and data updates.
 * It creates charts, status displays, and other dashboard components.
 */

// Self-executing function to create a module scope
(function() {
  'use strict';

  // Widget types
  const WIDGET_TYPES = {
    CHART: 'chart',
    STATUS: 'status',
    METRIC: 'metric',
    LIST: 'list'
  };

  // Chart types
  const CHART_TYPES = {
    LINE: 'line',
    BAR: 'bar',
    PIE: 'pie',
    DOUGHNUT: 'doughnut',
    GAUGE: 'gauge'
  };

  // Widget registry
  const widgets = {};

  // Chart instances
  const charts = {};

  /**
   * Initialize dashboard widgets
   * Creates and renders all dashboard widgets
   */
  function initialize() {
    console.log('Initializing dashboard widgets');
    
    // Create system resource widgets
    createSystemResourceWidgets();
    
    // Create storage widgets
    createStorageWidgets();
    
    // Create VM widgets
    createVMWidgets();
    
    // Create network widgets
    createNetworkWidgets();
    
    // Create status widgets
    createStatusWidgets();
    
    // Set up refresh interval
    setupRefreshInterval();
    
    // Listen for theme changes
    listenForThemeChanges();
    
    console.log('Dashboard widgets initialized');
  }

  /**
   * Create system resource widgets
   * CPU, memory, and system load widgets
   */
  function createSystemResourceWidgets() {
    // CPU usage widget
    createWidget('cpu-usage', {
      type: WIDGET_TYPES.CHART,
      title: 'CPU Usage',
      chartType: CHART_TYPES.LINE,
      dataSource: '/api/system/metrics/cpu',
      refreshInterval: 5000,
      options: {
        yAxisMax: 100,
        yAxisUnit: '%',
        showLegend: true
      }
    });
    
    // Memory usage widget
    createWidget('memory-usage', {
      type: WIDGET_TYPES.CHART,
      title: 'Memory Usage',
      chartType: CHART_TYPES.PIE,
      dataSource: '/api/system/metrics/memory',
      refreshInterval: 10000,
      options: {
        showLegend: true,
        showTotal: true
      }
    });
    
    // System load widget
    createWidget('system-load', {
      type: WIDGET_TYPES.METRIC,
      title: 'System Load',
      dataSource: '/api/system/metrics/load',
      refreshInterval: 10000,
      options: {
        showAverage: true,
        showTrend: true
      }
    });
  }

  /**
   * Create storage widgets
   * Disk usage and I/O widgets
   */
  function createStorageWidgets() {
    // Disk usage widget
    createWidget('disk-usage', {
      type: WIDGET_TYPES.CHART,
      title: 'Disk Usage',
      chartType: CHART_TYPES.DOUGHNUT,
      dataSource: '/api/storage/metrics/usage',
      refreshInterval: 30000,
      options: {
        showLegend: true,
        showTotal: true
      }
    });
    
    // Disk I/O widget
    createWidget('disk-io', {
      type: WIDGET_TYPES.CHART,
      title: 'Disk I/O',
      chartType: CHART_TYPES.BAR,
      dataSource: '/api/storage/metrics/io',
      refreshInterval: 5000,
      options: {
        showLegend: true,
        stacked: true
      }
    });
  }

  /**
   * Create VM widgets
   * VM status and resource usage widgets
   */
  function createVMWidgets() {
    // VM status widget
    createWidget('vm-status', {
      type: WIDGET_TYPES.STATUS,
      title: 'Virtual Machines',
      dataSource: '/api/vms/status',
      refreshInterval: 10000,
      options: {
        showCount: true,
        groupByStatus: true
      }
    });
    
    // VM resource usage widget
    createWidget('vm-resources', {
      type: WIDGET_TYPES.CHART,
      title: 'VM Resource Usage',
      chartType: CHART_TYPES.BAR,
      dataSource: '/api/vms/resources',
      refreshInterval: 10000,
      options: {
        showLegend: true,
        stacked: false
      }
    });
  }

  /**
   * Create network widgets
   * Network traffic and connection widgets
   */
  function createNetworkWidgets() {
    // Network traffic widget
    createWidget('network-traffic', {
      type: WIDGET_TYPES.CHART,
      title: 'Network Traffic',
      chartType: CHART_TYPES.LINE,
      dataSource: '/api/network/metrics/traffic',
      refreshInterval: 5000,
      options: {
        showLegend: true,
        fillArea: true
      }
    });
    
    // Network connections widget
    createWidget('network-connections', {
      type: WIDGET_TYPES.METRIC,
      title: 'Network Connections',
      dataSource: '/api/network/metrics/connections',
      refreshInterval: 10000,
      options: {
        showCount: true,
        groupByType: true
      }
    });
  }

  /**
   * Create status widgets
   * System health and service status widgets
   */
  function createStatusWidgets() {
    // System health widget
    createWidget('system-health', {
      type: WIDGET_TYPES.STATUS,
      title: 'System Health',
      dataSource: '/api/system/health',
      refreshInterval: 30000,
      options: {
        showIndicators: true
      }
    });
    
    // Service status widget
    createWidget('service-status', {
      type: WIDGET_TYPES.LIST,
      title: 'Service Status',
      dataSource: '/api/system/services',
      refreshInterval: 30000,
      options: {
        showStatus: true,
        showUptime: true
      }
    });
  }

  /**
   * Create a dashboard widget
   * @param {string} id - The widget ID
   * @param {Object} config - The widget configuration
   */
  function createWidget(id, config) {
    // Store widget configuration
    widgets[id] = config;
    
    // Get widget element
    const widgetElement = document.getElementById(id);
    if (!widgetElement) {
      console.warn(`Widget element not found: ${id}`);
      return;
    }
    
    // Create widget based on type
    switch (config.type) {
      case WIDGET_TYPES.CHART:
        createChartWidget(id, widgetElement, config);
        break;
      case WIDGET_TYPES.STATUS:
        createStatusWidget(id, widgetElement, config);
        break;
      case WIDGET_TYPES.METRIC:
        createMetricWidget(id, widgetElement, config);
        break;
      case WIDGET_TYPES.LIST:
        createListWidget(id, widgetElement, config);
        break;
      default:
        console.warn(`Unknown widget type: ${config.type}`);
    }
    
    // Load initial data
    refreshWidget(id);
  }

  /**
   * Create a chart widget
   * @param {string} id - The widget ID
   * @param {HTMLElement} element - The widget element
   * @param {Object} config - The widget configuration
   */
  function createChartWidget(id, element, config) {
    // Create canvas element
    const canvas = document.createElement('canvas');
    canvas.id = `${id}-chart`;
    
    // Create chart container
    const chartContainer = document.createElement('div');
    chartContainer.className = 'chart-container';
    chartContainer.appendChild(canvas);
    
    // Create widget header
    const header = document.createElement('h3');
    header.className = 'widget-title';
    header.textContent = config.title;
    
    // Add elements to widget
    element.appendChild(header);
    element.appendChild(chartContainer);
    
    // Create chart (placeholder until data is loaded)
    charts[id] = {
      type: config.chartType,
      canvas: canvas,
      instance: null
    };
  }

  /**
   * Create a status widget
   * @param {string} id - The widget ID
   * @param {HTMLElement} element - The widget element
   * @param {Object} config - The widget configuration
   */
  function createStatusWidget(id, element, config) {
    // Create widget header
    const header = document.createElement('h3');
    header.className = 'widget-title';
    header.textContent = config.title;
    
    // Create status container
    const statusContainer = document.createElement('div');
    statusContainer.className = 'status-container';
    statusContainer.id = `${id}-status`;
    
    // Add elements to widget
    element.appendChild(header);
    element.appendChild(statusContainer);
  }

  /**
   * Create a metric widget
   * @param {string} id - The widget ID
   * @param {HTMLElement} element - The widget element
   * @param {Object} config - The widget configuration
   */
  function createMetricWidget(id, element, config) {
    // Create widget header
    const header = document.createElement('h3');
    header.className = 'widget-title';
    header.textContent = config.title;
    
    // Create metric container
    const metricContainer = document.createElement('div');
    metricContainer.className = 'metric-container';
    metricContainer.id = `${id}-metric`;
    
    // Add elements to widget
    element.appendChild(header);
    element.appendChild(metricContainer);
  }

  /**
   * Create a list widget
   * @param {string} id - The widget ID
   * @param {HTMLElement} element - The widget element
   * @param {Object} config - The widget configuration
   */
  function createListWidget(id, element, config) {
    // Create widget header
    const header = document.createElement('h3');
    header.className = 'widget-title';
    header.textContent = config.title;
    
    // Create list container
    const listContainer = document.createElement('div');
    listContainer.className = 'list-container';
    
    // Create list element
    const list = document.createElement('ul');
    list.className = 'widget-list';
    list.id = `${id}-list`;
    
    // Add elements to widget
    listContainer.appendChild(list);
    element.appendChild(header);
    element.appendChild(listContainer);
  }

  /**
   * Refresh a widget with new data
   * @param {string} id - The widget ID
   */
  function refreshWidget(id) {
    const config = widgets[id];
    if (!config) {
      console.warn(`Widget not found: ${id}`);
      return;
    }
    
    // Show loading indicator
    LoadingIndicators.showLoading(`widget-${id}`);
    
    // Fetch data from API
    API.get(config.dataSource)
      .then(data => {
        updateWidget(id, data);
      })
      .catch(error => {
        console.error(`Error refreshing widget ${id}:`, error);
        ErrorHandlers.handleApiError(error, `refresh ${config.title} widget`);
      })
      .finally(() => {
        LoadingIndicators.hideLoading(`widget-${id}`);
      });
  }

  /**
   * Update a widget with new data
   * @param {string} id - The widget ID
   * @param {Object} data - The widget data
   */
  function updateWidget(id, data) {
    const config = widgets[id];
    if (!config) {
      return;
    }
    
    switch (config.type) {
      case WIDGET_TYPES.CHART:
        updateChartWidget(id, data);
        break;
      case WIDGET_TYPES.STATUS:
        updateStatusWidget(id, data);
        break;
      case WIDGET_TYPES.METRIC:
        updateMetricWidget(id, data);
        break;
      case WIDGET_TYPES.LIST:
        updateListWidget(id, data);
        break;
    }
  }

  /**
   * Set up refresh interval for widgets
   */
  function setupRefreshInterval() {
    // Set up refresh intervals for each widget
    Object.keys(widgets).forEach(id => {
      const config = widgets[id];
      if (config.refreshInterval) {
        setInterval(() => {
          refreshWidget(id);
        }, config.refreshInterval);
      }
    });
  }

  /**
   * Listen for theme changes and update charts
   */
  function listenForThemeChanges() {
    document.addEventListener('themechange', () => {
      // Update all charts with new theme colors
      Object.keys(charts).forEach(id => {
        if (charts[id].instance) {
          updateChartTheme(id);
        }
      });
    });
  }

  // Public API
  window.DashboardWidgets = {
    initialize: initialize,
    refreshWidget: refreshWidget,
    WIDGET_TYPES: WIDGET_TYPES,
    CHART_TYPES: CHART_TYPES
  };

  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', initialize);
})();
