/**
 * FILE          : api.js
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : API communication module
 */

/**
 * API module for PersistenceOS
 * Handles all communication with the backend API
 */
const API = (function() {
    // Base API URL
    const API_BASE_URL = '/api';
    
    // Default request timeout in milliseconds
    const DEFAULT_TIMEOUT = 30000;
    
    // Request queue for handling concurrent requests
    const requestQueue = [];
    let isProcessingQueue = false;
    
    /**
     * Creates a fetch request with authentication and timeout
     * @param {string} url - The API endpoint URL
     * @param {Object} options - Fetch options
     * @param {number} timeout - Request timeout in milliseconds
     * @returns {Promise<Response>} - The fetch response
     */
    async function fetchWithAuth(url, options = {}, timeout = DEFAULT_TIMEOUT) {
        // Get authentication token
        const token = Auth.getToken();
        
        // Set default headers
        const headers = {
            'Content-Type': 'application/json',
            ...options.headers
        };
        
        // Add authentication header if token exists
        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }
        
        // Create abort controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            // Make the fetch request
            const response = await fetch(url, {
                ...options,
                headers,
                signal: controller.signal
            });
            
            // Handle authentication errors
            if (response.status === 401) {
                // Try to refresh token if authenticated
                if (Auth.isAuthenticated()) {
                    try {
                        await Auth.refreshToken();
                        // Retry the request with new token
                        return fetchWithAuth(url, options, timeout);
                    } catch (refreshError) {
                        // If refresh fails, log out
                        Auth.logout();
                        throw new Error('Authentication failed. Please log in again.');
                    }
                } else {
                    // Not authenticated, redirect to login
                    window.location.href = '/login.html';
                    throw new Error('Authentication required');
                }
            }
            
            return response;
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error(`Request timeout after ${timeout}ms`);
            }
            throw error;
        } finally {
            clearTimeout(timeoutId);
        }
    }
    
    /**
     * Adds a request to the queue and processes it
     * @param {Function} requestFn - The request function to execute
     * @returns {Promise<any>} - The request result
     */
    async function queueRequest(requestFn) {
        return new Promise((resolve, reject) => {
            // Add request to queue
            requestQueue.push({ requestFn, resolve, reject });
            
            // Process queue if not already processing
            if (!isProcessingQueue) {
                processQueue();
            }
        });
    }
    
    /**
     * Processes the request queue sequentially
     */
    async function processQueue() {
        if (requestQueue.length === 0) {
            isProcessingQueue = false;
            return;
        }
        
        isProcessingQueue = true;
        
        // Get next request from queue
        const { requestFn, resolve, reject } = requestQueue.shift();
        
        try {
            // Execute the request
            const result = await requestFn();
            resolve(result);
        } catch (error) {
            reject(error);
        } finally {
            // Process next request in queue
            processQueue();
        }
    }
    
    /**
     * Makes a GET request to the API
     * @param {string} endpoint - The API endpoint
     * @param {Object} params - Query parameters
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async function get(endpoint, params = {}, options = {}) {
        return queueRequest(async () => {
            // Build URL with query parameters
            const url = new URL(`${API_BASE_URL}${endpoint}`, window.location.origin);
            
            // Add query parameters
            Object.keys(params).forEach(key => {
                if (params[key] !== undefined && params[key] !== null) {
                    url.searchParams.append(key, params[key]);
                }
            });
            
            // Make the request
            const response = await fetchWithAuth(url.toString(), {
                method: 'GET',
                ...options
            });
            
            // Handle non-OK responses
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `API error: ${response.status} ${response.statusText}`);
            }
            
            // Parse and return response data
            return response.json();
        });
    }
    
    /**
     * Makes a POST request to the API
     * @param {string} endpoint - The API endpoint
     * @param {Object} data - The request payload
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async function post(endpoint, data = {}, options = {}) {
        return queueRequest(async () => {
            // Make the request
            const response = await fetchWithAuth(`${API_BASE_URL}${endpoint}`, {
                method: 'POST',
                body: JSON.stringify(data),
                ...options
            });
            
            // Handle non-OK responses
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `API error: ${response.status} ${response.statusText}`);
            }
            
            // Parse and return response data
            return response.json();
        });
    }
    
    /**
     * Makes a PUT request to the API
     * @param {string} endpoint - The API endpoint
     * @param {Object} data - The request payload
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async function put(endpoint, data = {}, options = {}) {
        return queueRequest(async () => {
            // Make the request
            const response = await fetchWithAuth(`${API_BASE_URL}${endpoint}`, {
                method: 'PUT',
                body: JSON.stringify(data),
                ...options
            });
            
            // Handle non-OK responses
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `API error: ${response.status} ${response.statusText}`);
            }
            
            // Parse and return response data
            return response.json();
        });
    }
    
    /**
     * Makes a DELETE request to the API
     * @param {string} endpoint - The API endpoint
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async function del(endpoint, options = {}) {
        return queueRequest(async () => {
            // Make the request
            const response = await fetchWithAuth(`${API_BASE_URL}${endpoint}`, {
                method: 'DELETE',
                ...options
            });
            
            // Handle non-OK responses
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `API error: ${response.status} ${response.statusText}`);
            }
            
            // Parse and return response data if available
            if (response.headers.get('Content-Length') !== '0') {
                return response.json();
            }
            
            return { success: true };
        });
    }
    
    /**
     * Makes a PATCH request to the API
     * @param {string} endpoint - The API endpoint
     * @param {Object} data - The request payload
     * @param {Object} options - Additional fetch options
     * @returns {Promise<any>} - The response data
     */
    async function patch(endpoint, data = {}, options = {}) {
        return queueRequest(async () => {
            // Make the request
            const response = await fetchWithAuth(`${API_BASE_URL}${endpoint}`, {
                method: 'PATCH',
                body: JSON.stringify(data),
                ...options
            });
            
            // Handle non-OK responses
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `API error: ${response.status} ${response.statusText}`);
            }
            
            // Parse and return response data
            return response.json();
        });
    }
    
    /**
     * Uploads a file to the API
     * @param {string} endpoint - The API endpoint
     * @param {File|Blob} file - The file to upload
     * @param {Object} additionalData - Additional form data
     * @param {Function} progressCallback - Callback for upload progress
     * @returns {Promise<any>} - The response data
     */
    async function uploadFile(endpoint, file, additionalData = {}, progressCallback = null) {
        return queueRequest(async () => {
            // Create form data
            const formData = new FormData();
            formData.append('file', file);
            
            // Add additional data
            Object.keys(additionalData).forEach(key => {
                formData.append(key, additionalData[key]);
            });
            
            // Create XMLHttpRequest for progress tracking
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                
                // Set up progress tracking
                if (progressCallback && typeof progressCallback === 'function') {
                    xhr.upload.addEventListener('progress', (event) => {
                        if (event.lengthComputable) {
                            const percentComplete = Math.round((event.loaded / event.total) * 100);
                            progressCallback(percentComplete);
                        }
                    });
                }
                
                // Handle response
                xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            resolve({ success: true });
                        }
                    } else {
                        try {
                            const errorData = JSON.parse(xhr.responseText);
                            reject(new Error(errorData.detail || `Upload failed: ${xhr.status} ${xhr.statusText}`));
                        } catch (e) {
                            reject(new Error(`Upload failed: ${xhr.status} ${xhr.statusText}`));
                        }
                    }
                };
                
                // Handle errors
                xhr.onerror = function() {
                    reject(new Error('Network error during upload'));
                };
                
                // Handle timeouts
                xhr.ontimeout = function() {
                    reject(new Error(`Upload timeout after ${DEFAULT_TIMEOUT}ms`));
                };
                
                // Open and send request
                xhr.open('POST', `${API_BASE_URL}${endpoint}`);
                xhr.timeout = DEFAULT_TIMEOUT;
                
                // Add authentication header
                const token = Auth.getToken();
                if (token) {
                    xhr.setRequestHeader('Authorization', `Bearer ${token}`);
                }
                
                xhr.send(formData);
            });
        });
    }
    
    // Public API
    return {
        get,
        post,
        put,
        delete: del,
        patch,
        uploadFile
    };
})();

// Export for use in other modules
window.API = API;
