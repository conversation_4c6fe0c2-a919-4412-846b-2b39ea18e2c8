#!/bin/bash
#================
# FILE          : health-check-system.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : System resource health check script
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Source common functions
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
if [ -f "${SCRIPT_DIR}/../scripts/util-common.sh" ]; then
    source "${SCRIPT_DIR}/../scripts/util-common.sh"
else
    echo "Error: util-common.sh not found"
    exit 1
fi

# Define log file
LOG_FILE="${PERSISTENCE_LOG}/health-check-system.log"
export COMMON_LOG_FILE="${LOG_FILE}"

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help, -h            Show this help message"
    echo "  --version, -v         Show version information"
    echo "  --verbose             Show detailed output"
    echo "  --output FORMAT       Output format (json, text)"
    echo ""
    echo "Examples:"
    echo "  $0                    Run system health check"
    echo "  $0 --output json      Run system health check and output in JSON format"
}

# Check CPU health
check_cpu_health() {
    log_info "Checking CPU health"
    
    # Get CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4}')
    
    # Get system load
    local load_1min=$(cat /proc/loadavg | awk '{print $1}')
    local load_5min=$(cat /proc/loadavg | awk '{print $2}')
    local load_15min=$(cat /proc/loadavg | awk '{print $3}')
    
    # Get CPU count
    local cpu_count=$(nproc)
    
    # Determine status
    local status="healthy"
    
    if (( $(echo "${load_5min} > ${cpu_count} * 1.5" | bc -l) )); then
        status="critical"
    elif (( $(echo "${load_5min} > ${cpu_count} * 0.8" | bc -l) )); then
        status="warning"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${status}",
    "usage": ${cpu_usage},
    "load": [${load_1min}, ${load_5min}, ${load_15min}],
    "cores": ${cpu_count}
}
EOF
)
    
    log_success "CPU health check completed with status: ${status}"
    echo "${result}"
    return 0
}

# Check memory health
check_memory_health() {
    log_info "Checking memory health"
    
    # Get memory information
    local memory_total=$(free -m | awk '/Mem:/ {print $2}')
    local memory_used=$(free -m | awk '/Mem:/ {print $3}')
    local memory_percent=$((memory_used * 100 / memory_total))
    
    # Determine status
    local status="healthy"
    
    if [ ${memory_percent} -gt 90 ]; then
        status="critical"
    elif [ ${memory_percent} -gt 80 ]; then
        status="warning"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${status}",
    "total": ${memory_total},
    "used": ${memory_used},
    "percent": ${memory_percent}
}
EOF
)
    
    log_success "Memory health check completed with status: ${status}"
    echo "${result}"
    return 0
}

# Check disk health
check_disk_health() {
    log_info "Checking disk health"
    
    # Get disk information
    local disk_total=$(df -m / | awk 'NR==2 {print $2}')
    local disk_used=$(df -m / | awk 'NR==2 {print $3}')
    local disk_percent=$(df -h / | awk 'NR==2 {print $5}' | tr -d '%')
    
    # Determine status
    local status="healthy"
    
    if [ ${disk_percent} -gt 90 ]; then
        status="critical"
    elif [ ${disk_percent} -gt 80 ]; then
        status="warning"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${status}",
    "total": ${disk_total},
    "used": ${disk_used},
    "percent": ${disk_percent}
}
EOF
)
    
    log_success "Disk health check completed with status: ${status}"
    echo "${result}"
    return 0
}

# Check service health
check_service_health() {
    log_info "Checking service health"
    
    # Define critical services
    local critical_services=(
        "persistenceos-api"
        "nginx"
        "libvirtd"
    )
    
    # Define important services
    local important_services=(
        "persistenceos-health-daemon"
        "persistenceos-backup"
        "NetworkManager"
    )
    
    # Initialize arrays for failed services
    local critical_failed=()
    local warning_failed=()
    
    # Check critical services
    for service in "${critical_services[@]}"; do
        if ! systemctl is-active --quiet "${service}"; then
            critical_failed+=("${service}")
        fi
    done
    
    # Check important services
    for service in "${important_services[@]}"; do
        if ! systemctl is-active --quiet "${service}"; then
            warning_failed+=("${service}")
        fi
    done
    
    # Determine status
    local status="healthy"
    
    if [ ${#critical_failed[@]} -gt 0 ]; then
        status="critical"
    elif [ ${#warning_failed[@]} -gt 0 ]; then
        status="warning"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${status}",
    "critical": [$(printf '"%s",' "${critical_failed[@]}" | sed 's/,$//')],
    "warning": [$(printf '"%s",' "${warning_failed[@]}" | sed 's/,$//')]
}
EOF
)
    
    log_success "Service health check completed with status: ${status}"
    echo "${result}"
    return 0
}

# Check system health
check_system_health() {
    log_info "Checking system health"
    
    # Check components
    local cpu_health=$(check_cpu_health)
    local memory_health=$(check_memory_health)
    local disk_health=$(check_disk_health)
    local service_health=$(check_service_health)
    
    # Determine overall status
    local cpu_status=$(echo "${cpu_health}" | grep -o '"status": *"[^"]*"' | cut -d'"' -f4)
    local memory_status=$(echo "${memory_health}" | grep -o '"status": *"[^"]*"' | cut -d'"' -f4)
    local disk_status=$(echo "${disk_health}" | grep -o '"status": *"[^"]*"' | cut -d'"' -f4)
    local service_status=$(echo "${service_health}" | grep -o '"status": *"[^"]*"' | cut -d'"' -f4)
    
    local overall_status="healthy"
    
    if [ "${cpu_status}" = "critical" ] || [ "${memory_status}" = "critical" ] || [ "${disk_status}" = "critical" ] || [ "${service_status}" = "critical" ]; then
        overall_status="critical"
    elif [ "${cpu_status}" = "warning" ] || [ "${memory_status}" = "warning" ] || [ "${disk_status}" = "warning" ] || [ "${service_status}" = "warning" ]; then
        overall_status="warning"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${overall_status}",
    "details": {
        "cpu": ${cpu_health},
        "memory": ${memory_health},
        "disk": ${disk_health},
        "services": ${service_health}
    }
}
EOF
)
    
    log_success "System health check completed with status: ${overall_status}"
    
    # Output health status
    if [ "${OUTPUT_FORMAT}" = "json" ]; then
        echo "${result}"
    else
        # Default to text format
        echo "System Health Status: ${overall_status}"
        echo ""
        echo "CPU: ${cpu_status}"
        echo "Memory: ${memory_status}"
        echo "Disk: ${disk_status}"
        echo "Services: ${service_status}"
        echo ""
        echo "Details:"
        echo "- CPU Usage: $(echo "${cpu_health}" | grep -o '"usage": *[0-9.]*' | cut -d':' -f2 | tr -d ' ')%"
        echo "- Memory Usage: $(echo "${memory_health}" | grep -o '"percent": *[0-9]*' | cut -d':' -f2 | tr -d ' ')%"
        echo "- Disk Usage: $(echo "${disk_health}" | grep -o '"percent": *[0-9]*' | cut -d':' -f2 | tr -d ' ')%"
        
        # Show failed services
        local critical_services=$(echo "${service_health}" | grep -o '"critical": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        local warning_services=$(echo "${service_health}" | grep -o '"warning": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        
        if [ -n "${critical_services}" ]; then
            echo "- Critical Services: ${critical_services}"
        fi
        
        if [ -n "${warning_services}" ]; then
            echo "- Warning Services: ${warning_services}"
        fi
    fi
    
    return 0
}

# Main function
main() {
    # Default values
    VERBOSE="false"
    OUTPUT_FORMAT="text"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --help|-h)
                show_usage
                exit 0
                ;;
            --version|-v)
                echo "health-check-system.sh version ${VERSION}"
                echo "Part of PersistenceOS ${PERSISTENCE_VERSION}"
                exit 0
                ;;
            --verbose)
                VERBOSE="true"
                shift
                ;;
            --output)
                OUTPUT_FORMAT="$2"
                shift 2
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log_info "Starting system health check (version ${VERSION})"
    
    # Run system health check
    check_system_health
    
    log_success "System health check completed successfully"
    return 0
}

# Run main function
main "$@"
exit $?
