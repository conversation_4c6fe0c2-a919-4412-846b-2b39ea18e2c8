# PersistenceOS

PersistenceOS is a specialized hypervisor and NAS operating system based on SUSE Micro Leap 6.1, designed to provide robust virtualization capabilities with advanced storage management and snapshot functionality.

## Core Features

- **Dual Filesystem Strategy**: Leverages both btrfs (for system and snapshots) and XFS (for VM storage) to provide the optimal balance of snapshot capabilities and performance.
- **Modern Web UI**: A hybrid TrueNAS Scale/Cockpit-like web UI serves as the primary management interface, providing a modern, responsive interface for managing virtualization and NAS functionality.
- **Robust Virtualization**: Built on KVM/QEMU with libvirt for comprehensive VM management.
- **Advanced Storage Management**: Integrated snapshot and backup capabilities across filesystems.
- **Immutable Base OS**: Built on the transactional, immutable SUSE Micro Leap 6.1 foundation.

## Project Structure

The project follows a well-defined structure:

```
/usr/lib/persistence/
├── bin/                      # Executable scripts
├── scripts/                  # Setup and utility scripts
├── services/                 # Service definitions
├── tools/                    # System tools
├── web-ui/                   # Web UI files
└── api/                      # API backend
```

## Core Components

### System Scripts

- **util-common.sh**: Common utility functions used across all scripts
- **system-manager.sh**: Consolidated system management script
- **storage-manager.sh**: Storage management operations
- **network-manager.sh**: Network configuration and management
- **ui-manager.sh**: UI management and configuration
- **service-generator.sh**: Generate standardized service files
- **install-manager.sh**: Consolidated installation script with command-based interface
- **config-firewall.sh**: Firewall configuration
- **health-manager.sh**: System health monitoring
- **backup-manager.sh**: Backup and snapshot management
- **upgrade-manager.sh**: System upgrade management
- **image.sh**: Welcome screen and system information display

### Services

- **persistenceos-api.service**: FastAPI backend service
- **persistenceos-health-daemon.service**: Health monitoring daemon
- **persistenceos-backup.service**: Backup service
- **persistenceos-backup.timer**: Timer for scheduled backups
- **persistenceos-core-services.service**: Core services enablement including welcome screen

### Tools

- **health-checker**: Tool for checking system health
- **system-diagnostics.sh**: Comprehensive system diagnostics tool

### Python API

- **main.py**: Main FastAPI application
- **persistenceos/**: Python package for PersistenceOS
  - **system.py**: System management module
  - **utils/**: Utility modules
    - **shell_integration.py**: Shell script integration utilities

## Web UI Architecture

The web UI follows a modern architecture:

1. **Frontend**: Pure HTML, CSS, and JavaScript without frameworks
2. **Web Server**: FastAPI with Uvicorn serves both static files and API endpoints
3. **Backend**: Python FastAPI for API endpoints and direct file serving
4. **Integration**: Shell scripts interface with MicroOS packages

## Web UI Information

### Authentication and Login Page

The PersistenceOS web interface provides a secure login system with the following features:

- **Authentication System**: Uses FastAPI's OAuth2PasswordBearer for secure token-based authentication
- **Default Credentials**: Username: `root`, Password: `linux` (same as system login)
- **Token Management**: Automatically refreshes tokens to maintain session
- **Fallback Support**: Works even if the API server is temporarily unavailable

### API Endpoints

The web UI communicates with the following key API endpoints:

- `/api/auth/token` - For authentication
- `/api/config` - For server configuration
- `/api/health` - For health checks
- `/api/ip` - For IP information
- `/api/debug/files` - For debugging file existence

### Troubleshooting

If you encounter issues with the web UI, check the following:

1. **API Server Status**: `systemctl status persistenceos-api`
2. **API Server Logs**: `journalctl -u persistenceos-api`
3. **Detailed API Logs**: `/var/log/persistence/api.log`
4. **Network Configuration**: `nmcli device show`
5. **File Permissions**: Ensure web UI files are readable: `find /usr/lib/persistence/web-ui -type f -not -perm -644`

### Directory Structure

The web UI files are organized as follows:

```
/usr/lib/persistence/web-ui/
├── login.html        # Login page
├── index.html        # Main dashboard
├── css/              # Stylesheets
├── js/               # JavaScript modules
│   └── auth.js       # Authentication module
├── img/              # Images and icons
└── config/           # Configuration files
    └── server.js     # Server configuration
```

## Web UI Troubleshooting

If you encounter issues with the web UI not loading correctly, you can try the following solutions:

### Login Page Not Found

If you see "login.html not found at /usr/lib/persistence/web-ui/login.html" when accessing the web UI:

1. **Use the built-in fallback login page**:
   The system will automatically generate an embedded login page if the original is missing. You don't need to do anything special, just access:
   ```
   http://YOUR_SERVER_IP:8080/
   ```
   The system will redirect to the embedded login page if the static file isn't found.

2. **Check file existence and permissions**:
   ```
   ls -la /usr/lib/persistence/web-ui/login.html
   ```
   Ensure the file exists and has proper permissions (should be 644).

3. **Check API service logs**:
   ```
   systemctl status persistenceos-api
   journalctl -u persistenceos-api
   ```

4. **Verify debug information**:
   Access the debug endpoint to see available files:
   ```
   http://YOUR_SERVER_IP:8080/api/debug/files
   ```

### Duplicate Welcome Screens

If you see multiple welcome screens or duplicate login prompts in the terminal:

1. **The MOTD has been fixed** to use a specialized bash script that only shows once per login session. The script uses the `PERSISTENCEOS_WELCOME_SHOWN` environment variable to track whether the welcome message has already been displayed during the current session.

2. **How the fix works**:
   - Creates a profile.d script that runs on each login
   - Uses an environment variable to prevent duplicate displays
   - Clears the static MOTD file to remove any static content
   - Shows dynamic content with the current IP address
   - Includes system information from a configuration file

3. **Manually check the MOTD setup**:
   ```
   cat /etc/profile.d/persistenceos-welcome.sh
   ```
   Confirm it contains the variable `PERSISTENCEOS_WELCOME_SHOWN` to prevent duplication.

4. **Clear the static MOTD** if it exists:
   ```
   echo "" > /etc/motd
   ```

## Development Guidelines

### Package Management

- Include only necessary packages to maintain a minimal footprint
- Prioritize packages from the standard SUSE Micro Leap 6.1 repositories
- Use Package Hub only when necessary

### Code Quality

- Use shellscript for system tasks and integration with MicroOS native tools
- Use Python 3.11 with FastAPI for backend API endpoints
- Use pure HTML, CSS, and JavaScript for frontend UI components
- Implement proper error handling and logging
- Include comprehensive comments

## Installation

PersistenceOS is designed to be installed on bare metal or as a virtual machine. The installation process is handled by the KIWI build system, which creates a bootable ISO or disk image.

## Usage

After installation, PersistenceOS can be managed through the web UI, which is accessible at http://server-ip:8080. The default login credentials are:

- Username: admin
- Password: admin

It is recommended to change the default password after the first login.

## Contributing

Contributions to PersistenceOS are welcome! Please follow these guidelines:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

PersistenceOS is licensed under the MIT License. See the LICENSE file for details.

### Welcome Screen

The welcome screen displays important system information including IP addresses and web interface URLs. It is implemented through:

- **persistenceos-core-services.service**: Creates and manages the welcome screen services
- **image.sh**: Gathers system information and updates the display
- **Multiple display methods**: Pre-login via `/etc/issue` and post-login via `/etc/motd`

For more details, see [Welcome Screen Documentation](docs/welcome-screen.md).
