# PersistenceOS Authentication-to-Dashboard Workflow

## Overview

This document outlines the complete user authentication and dashboard flow for PersistenceOS, from initial access to full dashboard functionality.

## Workflow Steps

### 1. Initial Access
**User Action**: Navigate to PersistenceOS web interface
- **URL**: `http://*************:8080` (or system IP)
- **Backend**: FastAPI serves minimal `login.html`
- **Result**: <PERSON><PERSON><PERSON> loads login page wrapper

### 2. Login Page Loading
**Process**: Self-contained login interface creation
- **File**: `login.html` loads `login.js`
- **Mode**: `STANDALONE_MODE: true` in login.js
- **Action**: login.js creates complete login interface
- **Design**: Blue PersistenceOS theme with:
  - PersistenceOS logo and branding
  - Username field (pre-filled: "root")
  - Password field (placeholder: "Enter password")
  - "Remember me" checkbox
  - Blue "Log In" button
  - Footer with system information

### 3. Authentication Process
**User Action**: Enter credentials and click "Log In"
- **Default Credentials**: `root` / `linux`
- **Primary Method**: FastAPI backend (`/api/auth/token`)
- **Fallback Method**: Client-side validation for offline mode
- **Token Storage**: localStorage/sessionStorage
- **Success Message**: "Login successful! Redirecting..."

### 4. Dashboard Redirect
**Process**: Automatic redirection after authentication
- **Target**: `/app.html?from_login=true`
- **Delay**: 1 second for user feedback
- **Auth Check**: auth.js validates authentication state
- **Fallback**: Redirect to login if authentication fails

### 5. Dashboard Loading
**Process**: Vue.js dashboard initialization
- **File**: `app.html` endpoint serves Vue.js wrapper
- **Scripts Loaded**:
  - `auth.js` (authentication management)
  - `vue.js` (Vue.js framework with CDN fallback)
  - `app.js` (main dashboard application)
- **Authentication**: auth.js verifies token validity
- **Vue Mounting**: app.js mounts Vue application to `#app`

### 6. Dashboard Display
**Result**: Full PersistenceOS dashboard interface
- **Layout**: TrueNAS Scale/Cockpit-like design
- **Components**:
  - Left sidebar navigation
  - Main dashboard area with system overview cards
  - Real-time system metrics and status
  - Responsive design for all devices

## Technical Implementation

### File Structure
```
/usr/lib/persistence/web-ui/
├── login.html          # Minimal wrapper (loads login.js)
├── js/
│   ├── login.js        # Self-contained login (HTML + JS)
│   ├── auth.js         # Authentication management
│   ├── app.js          # Vue.js dashboard application
│   └── vue.js          # Vue.js framework
└── css/
    └── style.css       # Consolidated styles
```

### Authentication Flow
1. **login.js** → FastAPI `/api/auth/token` → JWT token
2. **auth.js** → Token validation and refresh management
3. **app.js** → Dashboard initialization with authenticated state

### Dashboard Components
- **SidebarComponent**: Navigation with sections (Overview, VMs, Storage, etc.)
- **DashboardComponent**: System overview with real-time metrics
- **VirtualMachineComponent**: VM management interface
- **StorageComponent**: Storage pool management
- **NetworkComponent**: Network interface management
- **SnapshotComponent**: Snapshot management
- **SettingsComponent**: System settings

## Key Features

### Self-Contained Login
- **No HTML/JS coordination issues**
- **Robust fallback creation**
- **Complete login interface in single file**
- **Simplified OBS build process**

### Authentication Management
- **JWT token with automatic refresh**
- **Fallback authentication for offline mode**
- **Session persistence (localStorage/sessionStorage)**
- **Automatic logout on token expiry**

### Dashboard Features
- **Real-time system monitoring**
- **Responsive card-based layout**
- **Hash-based navigation**
- **Component-based architecture**
- **Progressive loading with fallbacks**

## Error Handling

### Login Failures
- **API Unavailable**: Fallback to client-side validation
- **Invalid Credentials**: Clear error messages
- **Network Issues**: Offline mode with limited functionality

### Dashboard Failures
- **Vue.js Load Failure**: CDN fallback mechanism
- **Authentication Expiry**: Automatic redirect to login
- **Component Errors**: Graceful degradation with error messages

## Testing

### Manual Testing
1. Navigate to system IP address
2. Verify login form displays correctly
3. Test authentication with root/linux
4. Confirm redirect to dashboard
5. Verify all dashboard components load
6. Test navigation between sections

### Automated Testing
- **Flow Test Script**: `flow-test.js`
- **Browser Console**: Run `FlowTest.runAllTests()`
- **Comprehensive Coverage**: All workflow steps
- **Detailed Reporting**: Success/failure analysis

## Browser Compatibility
- **Chrome**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **Mobile Browsers**: Responsive design support

## Performance Optimization
- **Minimal Initial Load**: login.html is lightweight
- **Progressive Enhancement**: Features load incrementally
- **CDN Fallbacks**: Ensures availability
- **Efficient Caching**: Static assets cached appropriately

## Security Features
- **JWT Token Authentication**: Secure token-based auth
- **Automatic Token Refresh**: Prevents session expiry
- **HTTPS Support**: Secure communication
- **CORS Configuration**: Proper cross-origin handling
- **Input Validation**: Client and server-side validation

## Troubleshooting

### Common Issues
1. **Login Form Not Displaying**: Check login.js loading
2. **Authentication Failures**: Verify FastAPI backend
3. **Dashboard Not Loading**: Check Vue.js and app.js
4. **Navigation Issues**: Verify hash routing

### Debug Tools
- **Browser Console**: Detailed logging available
- **Flow Test**: Automated workflow verification
- **Network Tab**: Monitor API requests
- **Vue DevTools**: Component inspection

## Future Enhancements
- **Multi-factor Authentication**: Enhanced security
- **Role-based Access Control**: Granular permissions
- **Real-time Notifications**: System alerts
- **Advanced Monitoring**: Extended metrics
- **Mobile App**: Native mobile interface
