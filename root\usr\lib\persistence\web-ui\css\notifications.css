/*
 * FILE          : notifications.css
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : Styles for error and success notifications
 */

/* Toast Container
   ========================================================================== */

.toast-container {
    position: fixed;
    z-index: var(--z-index-tooltip);
    padding: 1rem;
    pointer-events: none;
}

.toast-container.top-right {
    top: 0;
    right: 0;
}

.toast-container.top-left {
    top: 0;
    left: 0;
}

.toast-container.bottom-right {
    bottom: 0;
    right: 0;
}

.toast-container.bottom-left {
    bottom: 0;
    left: 0;
}

.toast-container.top-center {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.toast-container.bottom-center {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

/* Toast Notification
   ========================================================================== */

.toast {
    width: 350px;
    max-width: 100%;
    font-size: var(--font-size-base);
    color: var(--foreground);
    background-color: var(--card-bg);
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
    pointer-events: auto;
    overflow: hidden;
}

.toast.show {
    opacity: 1;
    transform: translateY(0);
}

.toast-header {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    color: var(--secondary);
    background-color: rgba(0, 0, 0, 0.03);
    background-clip: padding-box;
    border-bottom: 1px solid var(--border-color);
}

.toast-body {
    padding: 0.75rem;
}

.toast-icon {
    margin-right: 0.5rem;
    font-size: 1.25rem;
}

.toast-title {
    margin-right: auto;
    font-weight: var(--font-weight-bold);
}

.toast-close {
    font-size: 1.25rem;
    font-weight: var(--font-weight-bold);
    line-height: 1;
    color: var(--secondary);
    text-shadow: 0 1px 0 #fff;
    opacity: 0.5;
    background: transparent;
    border: 0;
    padding: 0;
    cursor: pointer;
}

.toast-close:hover {
    color: var(--foreground);
    opacity: 0.75;
}

/* Toast Variants
   ========================================================================== */

.toast-success {
    border-left: 4px solid var(--success);
}

.toast-success .toast-icon {
    color: var(--success);
}

.toast-info {
    border-left: 4px solid var(--info);
}

.toast-info .toast-icon {
    color: var(--info);
}

.toast-warning {
    border-left: 4px solid var(--warning);
}

.toast-warning .toast-icon {
    color: var(--warning);
}

.toast-error {
    border-left: 4px solid var(--danger);
}

.toast-error .toast-icon {
    color: var(--danger);
}

/* Inline Notifications
   ========================================================================== */

.notification {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.notification-success {
    color: var(--success-dark);
    background-color: rgba(40, 167, 69, 0.1);
    border-color: rgba(40, 167, 69, 0.2);
}

.notification-info {
    color: var(--info-dark);
    background-color: rgba(23, 162, 184, 0.1);
    border-color: rgba(23, 162, 184, 0.2);
}

.notification-warning {
    color: var(--warning-dark);
    background-color: rgba(255, 193, 7, 0.1);
    border-color: rgba(255, 193, 7, 0.2);
}

.notification-error {
    color: var(--danger-dark);
    background-color: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.2);
}

.notification-icon {
    margin-right: 0.5rem;
}

.notification-title {
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.25rem;
}

.notification-message {
    margin-bottom: 0;
}

.notification-close {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0.75rem 1.25rem;
    color: inherit;
    background: transparent;
    border: 0;
    cursor: pointer;
    opacity: 0.5;
}

.notification-close:hover {
    opacity: 1;
}

/* Form Validation Messages
   ========================================================================== */

.form-group {
    position: relative;
}

.validation-message {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: var(--danger);
}

.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: var(--danger);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:invalid:focus,
.form-control.is-invalid:focus {
    border-color: var(--danger);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.was-validated .form-control:invalid ~ .validation-message,
.form-control.is-invalid ~ .validation-message {
    display: block;
}

.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: var(--success);
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:valid:focus,
.form-control.is-valid:focus {
    border-color: var(--success);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* System Notification Banner
   ========================================================================== */

.system-notification-banner {
    position: relative;
    padding: 1rem;
    background-color: var(--info);
    color: white;
    text-align: center;
    font-weight: var(--font-weight-bold);
    z-index: var(--z-index-fixed);
}

.system-notification-banner.warning {
    background-color: var(--warning);
    color: var(--foreground);
}

.system-notification-banner.error {
    background-color: var(--danger);
}

.system-notification-banner.success {
    background-color: var(--success);
}

.system-notification-banner .close {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: inherit;
    background: transparent;
    border: 0;
    font-size: 1.5rem;
    line-height: 1;
    opacity: 0.7;
    cursor: pointer;
}

.system-notification-banner .close:hover {
    opacity: 1;
}

/* Dark Theme Adjustments
   ========================================================================== */

.dark-theme .toast {
    background-color: #2c2c2c;
    border-color: #444;
}

.dark-theme .toast-header {
    background-color: #333;
    border-color: #444;
    color: #ddd;
}

.dark-theme .toast-close {
    color: #ddd;
    text-shadow: none;
}

.dark-theme .notification-success {
    background-color: rgba(40, 167, 69, 0.2);
    border-color: rgba(40, 167, 69, 0.3);
}

.dark-theme .notification-info {
    background-color: rgba(23, 162, 184, 0.2);
    border-color: rgba(23, 162, 184, 0.3);
}

.dark-theme .notification-warning {
    background-color: rgba(255, 193, 7, 0.2);
    border-color: rgba(255, 193, 7, 0.3);
}

.dark-theme .notification-error {
    background-color: rgba(220, 53, 69, 0.2);
    border-color: rgba(220, 53, 69, 0.3);
}
