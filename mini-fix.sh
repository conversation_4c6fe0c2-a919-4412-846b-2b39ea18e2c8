#!/bin/bash
# Minimal fix for nginx redirect issue

# Fix nginx configuration
cat > /etc/nginx/conf.d/persistenceos.conf << 'EOF'
server {
    listen 8080 default_server;
    listen [::]:8080 default_server;
    root /usr/lib/persistence/web-ui;
    index login.html index.html;
    
    # Key fix: explicit redirect instead of try_files
    location = / {
        return 302 /login.html;
    }
    
    location / {
        try_files $uri $uri.html $uri/ =404;
    }
    
    location = /login.html {
    }
    
    location = /index.html {
    }
}
EOF

# Create server.js if missing
mkdir -p /usr/lib/persistence/web-ui/config
IP=$(ip -4 addr show | grep -v "127.0.0.1" | grep -oP 'inet \K[\d.]+' | head -1)
[ -z "$IP" ] && IP="127.0.0.1"

echo "// Server Config
const serverConfig = {
    host: '$IP',
    port: 8080,
    apiBaseUrl: 'http://$IP:8080/api'
};
window.serverConfig = serverConfig;" > /usr/lib/persistence/web-ui/config/server.js

# Fix permissions
chmod -R 755 /usr/lib/persistence/web-ui
find /usr/lib/persistence/web-ui -type f -exec chmod 644 {} \;

# Restart nginx
systemctl restart nginx

echo "Fix applied, test with: curl -I http://localhost:8080/" 