// Network Component
app.component('Network', {
    props: {
        interfaces: Array
    },
    emits: ['refresh-data'],
    data() {
        return {
            isLoading: false
        };
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');
            
            // Reset loading state after animation
            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        
        configureInterface(name) {
            console.log(`Configuring interface: ${name}`);
            // In a real implementation, this would open a modal
        }
    },
    template: `
        <div id="network-section" class="content-section">
            <div class="card">
                <div class="card-header">
                    <h3>Network Interfaces</h3>
                    <div class="card-actions">
                        <button class="refresh-btn" @click="refreshData"
                            :class="{'spinning': isLoading}">
                            <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>IP Address</th>
                                <th>MAC Address</th>
                                <th>Traffic</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="iface in interfaces" :key="iface.name">
                                <td>{{ iface.name }}</td>
                                <td>
                                    <span class="status-badge" :class="{ 'running': iface.status === 'up' }">
                                        {{ iface.status.toUpperCase() }}
                                    </span>
                                </td>
                                <td>{{ iface.ipAddress }}</td>
                                <td>{{ iface.macAddress }}</td>
                                <td>
                                    <div class="traffic-stats">
                                        <span><i class="fas fa-arrow-down"></i> {{ iface.download }}</span>
                                        <span><i class="fas fa-arrow-up"></i> {{ iface.upload }}</span>
                                    </div>
                                </td>
                                <td>
                                    <button class="btn btn-primary" @click="configureInterface(iface.name)">Configure</button>
                                </td>
                            </tr>
                            
                            <!-- Show message if no interfaces exist -->
                            <tr v-if="interfaces.length === 0">
                                <td colspan="6" class="no-data">
                                    No network interfaces detected.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `
}); 