# PersistenceOS KIWI Setup Guide

## Table of Contents
1. [Critical Requirements](#critical-requirements)
2. [Overview](#overview)
3. [KIWI Schema Structure](#kiwi-schema-structure)
4. [Detailed Section Breakdown](#detailed-section-breakdown)
5. [Package Selection](#package-selection)
6. [Repository Configuration](#repository-configuration)
7. [Installation Process Flow](#installation-process-flow)
8. [Deployment & Testing](#deployment-testing)
9. [Troubleshooting](#troubleshooting)

## Critical Requirements
> ### ⚠️ ESSENTIAL KIWI ELEMENT ORDER
> For KIWI schema version 8.3 (used by PersistenceOS), elements **MUST** appear in this exact order:
> ```xml
> <description>
> <preferences>
> <users> (optional)
> <bootloader>
> <type>          <!-- MUST BE SEPARATE from <preferences> in schema 8.3 -->
> <repository>
> <packages>
> ```
> 
> **Element Order Rules:**
> - Sequence must be: `description → preferences → bootloader → type → repository → packages`
> - All elements are required EXCEPT `bootloader` (though recommended for ISO images)
> - The `<preferences>` section contains system-wide settings (locale, keytable, etc.)
> - The `<type>` element defines the output format and filesystem
>
> ### ⚠️ REQUIRED OBS REPOSITORY CONFIGURATION
> ```xml
> <repository type="rpm-md">
>   <source path="obsrepositories:/"/>
> </repository>
> ```
> The special `obsrepositories:/` path is **mandatory** for successful OBS builds
>
> ### ⚠️ ESSENTIAL INSTALLER PACKAGES
> These specific packages **MUST** be included for the installer to function:
> ```xml
> <package name="dracut-kiwi-oem-repart"/>
> <package name="dracut-kiwi-oem-dump"/>
> ```
> Without these packages, the installation process will fail
>
> ### ⚠️ CONSISTENT VERSION NUMBERING
> The **exact same version number** must be used in all project files:
> ```
> PersistenceOS.kiwi:   <version>6.1.0</version>
> persistence-os.spec:  Version:        6.1.0
> _service:             <param name="version">6.1.0</param>
> scripts/image.sh:     PERSISTENCE_VERSION="6.1.0"
> scripts/config.sh:    PERSISTENCE_VERSION="6.1.0"
> ```
> Inconsistent version numbers between files will cause subtle bugs and may break the build process

## Overview

PersistenceOS is a specialized hypervisor and NAS operating system based on SUSE Micro Leap 6.1, designed to provide robust virtualization capabilities with advanced storage management and snapshot functionality. This guide documents the structure and configuration of the PersistenceOS KIWI file, which serves as the blueprint for building the operating system image.

## KIWI Schema Structure

### Schema Version and Element Order

For KIWI schema version 8.3 (the version used by PersistenceOS), the proper element order is **critical** for successful builds:

```
<image schemaversion="8.3">
  <description>
  <preferences>
  <users> (optional)
  <profiles> (optional)
  <bootloader> (may be inside or outside <type> depending on requirements)
  <type> (separate from <preferences> in schema 8.3)
  <repository>
  <packages>
</image>
```

### Key Schema Rules
- **Element order matters**: The elements must appear in the exact sequence shown above
- **Required elements**: `<description>`, `<preferences>`, `<type>`, `<repository>`, and `<packages>` are mandatory
- **Optional elements**: `<users>`, `<profiles>`, and `<bootloader>` are optional but recommended
- **Separation of concerns**: In schema 8.3, the `<type>` element must be separate from `<preferences>`
- **Multiple instances**: You can have multiple `<repository>` and `<packages>` sections

## Detailed Section Breakdown

### Image Declaration
```xml
<?xml version="1.0" encoding="utf-8"?>
<image schemaversion="8.3" name="PersistenceOS" displayname="PersistenceOS">
```
- `schemaversion="8.3"`: Specifies the KIWI schema version
- `name="PersistenceOS"`: The internal name used for image files
- `displayname="PersistenceOS"`: The user-friendly name shown in the bootloader

### Description
```xml
<description type="system">
  <author>PersistenceOS Team</author>
  <contact><EMAIL></contact>
  <specification>PersistenceOS Installation Image (MicroOS 6.1 Base)</specification>
</description>
```
- `type="system"`: Indicates a system image (alternative: `type="boot"` for boot images)
- Contains metadata about the image creator, contact information, and purpose

### Preferences
```xml
<preferences>
  <version>6.1.0</version>
  <packagemanager>zypper</packagemanager>
  <locale>en_US</locale>
  <keytable>us</keytable>
  <timezone>UTC</timezone>
  <rpm-check-signatures>false</rpm-check-signatures>
  <rpm-excludedocs>true</rpm-excludedocs>
  <type image="oem" filesystem="btrfs" firmware="uefi" installiso="true" bootpartition="false" btrfs_root_is_snapper_snapshot="false">
    <bootloader name="grub2" console="serial" timeout="10"/>
    <size unit="G">8</size>
  </type>
</preferences>
```
- `<version>`: The version number of your image
- `<packagemanager>`: The package manager to use (zypper for SUSE-based systems)
- `<locale>`, `<keytable>`, `<timezone>`: System localization settings
- `<rpm-check-signatures>`: Whether to verify package signatures
- `<rpm-excludedocs>`: Whether to exclude documentation files
- `<type>`: Defines the output format and filesystem

#### The `type` Element
The `type` element is particularly important as it defines the image type and various attributes:
- `image="oem"`: Creates an OEM installable image
- `filesystem="btrfs"`: Uses btrfs as the root filesystem
- `firmware="uefi"`: Targets UEFI systems
- `installiso="true"`: Creates an ISO that can be used for installation
- `bootpartition="false"`: Does not create a separate boot partition
- `btrfs_root_is_snapper_snapshot="false"`: Disables snapper snapshot as root

The nested `<bootloader>` element configures:
- `name="grub2"`: Uses GRUB2 as the bootloader
- `console="serial"`: Enables serial console for VM environments
- `timeout="10"`: Sets a 10-second timeout for bootloader menu

### Users
```xml
<users>
  <user password="$1$wYJUgpM5$RXMMeASDc035eX.NbYWFl0" home="/root" name="root" groups="root"/>
</users>
```
- Defines user accounts that will be created in the image
- The password is specified as a pre-hashed string
- The hash shown above corresponds to the password "linux"

### Profiles
```xml
<profiles>
  <profile name="PersistenceOS" description="PersistenceOS Image Profile" arch="x86_64"/>
</profiles>
```
- Defines profiles for specific architectures or use cases
- Used for conditional inclusion of packages or configurations
- In this case, specifies that this profile is for x86_64 architecture

### Repository
```xml
<repository type="rpm-md">
  <source path="obsrepositories:/"/>
</repository>
```
- `type="rpm-md"`: Specifies a standard RPM repository format
- `<source path="obsrepositories:/"/>`: **Critical setting** that uses the local OBS repository
- This configuration is essential for the installer to work properly

### Packages
PersistenceOS uses multiple package sections for different stages of the installation:

```xml
<packages type="image">
  <!-- Base system packages -->
  <package name="patterns-microos-base"/>
  <package name="patterns-microos-defaults"/>
  <!-- ... more packages ... -->
</packages>

<packages type="bootstrap">
  <package name="filesystem"/>
  <package name="glibc-locale"/>
  <package name="ca-certificates"/>
</packages>
```

- `type="image"`: Packages installed in the final image
- `type="bootstrap"`: Packages needed during the bootstrap phase

## Package Selection

### Critical Installation Packages
These packages are **required** for the PersistenceOS installer to function properly:

```xml
<package name="dracut-kiwi-oem-repart"/>
<package name="dracut-kiwi-oem-dump"/>
```

These packages provide essential functionality:
- `dracut-kiwi-oem-repart`: Handles partition resizing during installation
- `dracut-kiwi-oem-dump`: Dumps the installation image onto the target system

Without these packages, the OEM installation process will fail.

### Core System Packages
```xml
<package name="patterns-microos-base"/>
<package name="patterns-microos-defaults"/>
<package name="systemd"/>
<package name="dracut"/>
<package name="grub2"/>
<package name="kernel-default"/>
```
These packages provide the foundation of the operating system.

### Virtualization Packages
```xml
<package name="libvirt-daemon"/>
<package name="libvirt-client"/>
<package name="qemu-kvm"/>
<package name="qemu-tools"/>
<package name="libvirt-daemon-qemu"/>
<package name="dnsmasq"/>
```
These packages enable the hypervisor functionality of PersistenceOS.

### Storage Packages
```xml
<package name="btrfsprogs"/>
<package name="e2fsprogs"/>
<package name="xfsprogs"/>
<package name="lvm2"/>
<package name="snapper"/>
<package name="smartmontools"/>
<package name="mdadm"/>
```
These packages provide filesystem management and storage capabilities.

### Web UI & API Packages
```xml
<package name="nginx"/>
<package name="python311-base"/>
<package name="python311-fastapi"/>
<package name="python311-uvicorn"/>
<package name="python311-psutil"/>
<package name="python311-pydantic"/>
<package name="python311-typing-extensions"/>
```
These packages enable the web management interface.

### Networking Packages
```xml
<package name="NetworkManager"/>
<package name="iproute2"/>
```
These packages handle network configuration and management.

### Security Packages
```xml
<package name="firewalld"/>
<package name="policycoreutils"/>
<package name="selinux-policy"/>
```
These packages provide security features.

### Recommended Additional Packages
For a complete NAS/Virtualization solution, consider adding:
```xml
<package name="swtpm"/>             <!-- TPM emulation for secure VMs -->
<package name="libguestfs"/>        <!-- VM image manipulation tools -->
<package name="libvirt-nss"/>       <!-- DNS resolution for VMs -->
<package name="samba"/>             <!-- SMB/CIFS sharing support -->
<package name="nfs-utils"/>         <!-- NFS server functionality -->
<package name="mergerfs"/>          <!-- Flexible storage pooling -->
<package name="numad"/>             <!-- NUMA optimization for multi-socket hosts -->
```

## Repository Configuration

### OBS Repository Setup
The most crucial repository configuration for building with OBS is:

```xml
<repository type="rpm-md">
  <source path="obsrepositories:/"/>
</repository>
```

This special path tells KIWI to use the repositories configured in the OBS build environment. This is essential for successful builds in OBS.

### Adding Additional Repositories
If you need to add specific repositories (e.g., PackageHub for additional packages), use:

```xml
<repository type="rpm-md" alias="PackageHub" priority="100">
  <source path="https://download.opensuse.org/repositories/openSUSE:/Backports:/SLE-15-SP4/standard/"/>
</repository>
```

- `alias`: A unique name for the repository
- `priority`: Lower numbers indicate higher priority (1-99: higher than default, 100: default, >100: lower than default)

## Installation Process Flow

The PersistenceOS installation follows this sequence:

1. **Boot Phase**: System boots from installation media
   - GRUB bootloader loads the kernel and initrd
   - The kernel starts the boot process

2. **Installation Preparation**:
   - The dracut-kiwi-oem modules detect the installation environment
   - User selects installation target

3. **Image Deployment**:
   - `dracut-kiwi-oem-dump` extracts the system image to the target disk
   - `dracut-kiwi-oem-repart` handles partition resizing

4. **First Boot Configuration**:
   - System reboots into the installed system
   - Image.sh script runs to set up environment
   - SystemD services are enabled

5. **Final Setup**:
   - The Web UI is configured
   - Network is set up
   - Storage pools are initialized
   - The system is ready for use

## Deployment & Testing

### Building with OBS
1. Upload all files to your OBS project
2. Ensure the following files are present:
   - `PersistenceOS.kiwi`
   - `config.sh`
   - `image.sh`
   - `persistence-os.spec`
   - Supporting script files

3. Trigger a build in OBS
4. Wait for the build to complete
5. Download the resulting ISO image

### Testing the Image
1. Create a new virtual machine with:
   - At least 2 CPU cores
   - 4GB RAM minimum (8GB recommended)
   - 20GB disk minimum

2. Boot from the ISO image
3. Select "Installation" from the GRUB menu
4. Follow the installation prompts
5. After installation, the system will reboot
6. Log in with the root user and password "linux"
7. Verify that the welcome message and network information appear
8. Access the web UI using the provided URL

## Troubleshooting

### Common Build Issues

#### XML Syntax Errors
If you see errors like:
```
broken: element 'script' closes, but I expected '![CDATA['
```
Check:
- CDATA sections are properly closed with `]]>`
- XML tags are properly nested
- Element order follows the schema requirements

#### Missing Packages
If builds fail due to missing packages:
- Check that all required packages exist in the specified repositories
- Verify package names match exactly what's available in the repositories
- Consider using fallback `<namedCollection>` patterns for groups of packages

#### OBS Repository Issues
If you see:
```
The sources either contain no build description (e.g. specfile)
```
Ensure:
- The spec file is properly formatted
- The repository configuration uses the correct `obsrepositories:/` path
- All source files referenced in the spec file exist

### Installation Troubleshooting

#### Boot Issues
- **Black screen**: Check the console parameters in the bootloader configuration
- **Kernel panic**: Verify that all necessary drivers are included

#### Partition Issues
- **Failed to resize**: Check if `dracut-kiwi-oem-repart` is included
- **Image extraction failure**: Verify `dracut-kiwi-oem-dump` is included

#### Post-installation Issues
- **No welcome message**: Check if `image.sh` is being executed properly
- **Network configuration fails**: Verify NetworkManager is enabled
- **Web UI inaccessible**: Check nginx configuration and service status

For advanced troubleshooting, use:
```bash
journalctl -xeu dracut-kiwi-oem-*
journalctl -xeu persistenceos-*
```

---

This guide serves as the definitive reference for building and deploying PersistenceOS using the KIWI system. Follow these guidelines to ensure successful builds and deployments.