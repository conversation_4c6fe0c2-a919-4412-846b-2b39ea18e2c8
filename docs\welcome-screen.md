# PersistenceOS Welcome Screen

## Overview

The PersistenceOS welcome screen displays important system information including IP addresses and web interface URLs. This document explains how the welcome screen is implemented and managed in PersistenceOS.

## Architecture

### Components

1. **Core Service**: `persistenceos-core-services.service`
   - Creates and manages the welcome screen services
   - Ensures proper startup order and dependencies

2. **Welcome Services**:
   - `persistenceos-welcome.service`: One-time setup of welcome screen
   - `persistenceos-welcome.timer`: Periodically updates welcome screen (every 5 minutes)

3. **Script**: `/usr/lib/persistence/bin/image`
   - Implements the actual welcome screen functionality
   - Gathers system information (IP addresses, hardware details, etc.)
   - Updates `/etc/motd` and `/etc/issue`
   - Configures agetty to display the welcome message

### Display Methods

The welcome screen is displayed through multiple mechanisms to ensure reliability:

1. **Pre-login Display**: 
   - Uses `/etc/issue` and agetty configuration
   - Shows welcome message before login prompt

2. **Post-login Display**:
   - Uses `/etc/motd` 
   - Uses profile.d script to display on login
   - Uses bashrc integration for interactive shells

## Implementation Details

### Service Creation

The welcome screen services are created by `persistenceos-core-services.service` during system startup:

```bash
# From persistenceos-core-services.service
ExecStart=/bin/bash -c 'cat > /etc/systemd/system/persistenceos-welcome.service << EOF
[Unit]
Description=PersistenceOS Welcome Message Setup
After=network.target
ConditionPathExists=/usr/lib/persistence/bin/image

[Service]
Type=oneshot
ExecStart=/usr/lib/persistence/bin/image --install
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF'
```

### Script Functionality

The `image` script performs the following functions:

1. Gathers system information with fallbacks
2. Creates welcome message with IP addresses and web interface URLs
3. Updates `/etc/motd` and `/etc/issue`
4. Configures agetty to display the welcome message
5. Sets up profile.d and bashrc integration

## Troubleshooting

If the welcome screen is not displaying:

1. Check if the core services are running:
   ```
   systemctl status persistenceos-core-services
   ```

2. Check if the welcome services are running:
   ```
   systemctl status persistenceos-welcome.service
   systemctl status persistenceos-welcome.timer
   ```

3. Manually update the welcome screen:
   ```
   /usr/lib/persistence/bin/image --force
   ```

4. Check if the script exists and is executable:
   ```
   ls -la /usr/lib/persistence/bin/image
   ```

5. Verify that agetty is configured correctly:
   ```
   cat /etc/systemd/system/getty@.service.d/persistenceos.conf
   ``` 