/*
 * FILE          : loading-indicators.css
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : Styles for loading spinners and overlays
 */

/* Loading Spinner
   ========================================================================== */

.spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    border: 0.25rem solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

.spinner-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.2rem;
}

.spinner-lg {
    width: 3rem;
    height: 3rem;
    border-width: 0.3rem;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

/* Growing Spinner */
.spinner-grow {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    background-color: currentColor;
    border-radius: 50%;
    opacity: 0;
    animation: spinner-grow 0.75s linear infinite;
}

.spinner-grow-sm {
    width: 1rem;
    height: 1rem;
}

.spinner-grow-lg {
    width: 3rem;
    height: 3rem;
}

@keyframes spinner-grow {
    0% {
        transform: scale(0);
    }
    50% {
        opacity: 1;
        transform: none;
    }
}

/* Spinner Colors */
.spinner-primary,
.spinner-grow-primary {
    color: var(--primary);
}

.spinner-secondary,
.spinner-grow-secondary {
    color: var(--secondary);
}

.spinner-success,
.spinner-grow-success {
    color: var(--success);
}

.spinner-danger,
.spinner-grow-danger {
    color: var(--danger);
}

.spinner-warning,
.spinner-grow-warning {
    color: var(--warning);
}

.spinner-info,
.spinner-grow-info {
    color: var(--info);
}

/* Loading Overlay
   ========================================================================== */

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: var(--z-index-modal);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-content {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
    max-width: 90%;
}

.loading-spinner {
    margin-bottom: 1rem;
}

.loading-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.5rem;
}

.loading-subtext {
    color: var(--secondary);
    margin-bottom: 0;
}

/* Element Loading State
   ========================================================================== */

.element-loading {
    position: relative;
    min-height: 100px;
}

.element-loading::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 1;
}

.element-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin-top: -1rem;
    margin-left: -1rem;
    border: 0.25rem solid var(--primary);
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
    z-index: 2;
}

.dark-theme .element-loading::before {
    background-color: rgba(0, 0, 0, 0.7);
}

/* Button Loading State
   ========================================================================== */

.btn.loading {
    position: relative;
    pointer-events: none;
    color: transparent !important;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 1rem;
    height: 1rem;
    top: calc(50% - 0.5rem);
    left: calc(50% - 0.5rem);
    border: 0.15rem solid rgba(255, 255, 255, 0.5);
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

.btn-secondary.loading::after,
.btn-light.loading::after {
    border-color: rgba(0, 0, 0, 0.5);
    border-right-color: transparent;
}

/* Progress Bar
   ========================================================================== */

.progress {
    display: flex;
    height: 1rem;
    overflow: hidden;
    font-size: 0.75rem;
    background-color: var(--border-color);
    border-radius: var(--border-radius);
}

.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    background-color: var(--primary);
    transition: width 0.6s ease;
}

.progress-bar-striped {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 1rem 1rem;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    from { background-position: 1rem 0; }
    to { background-position: 0 0; }
}

/* Progress Bar Colors */
.progress-bar-success {
    background-color: var(--success);
}

.progress-bar-info {
    background-color: var(--info);
}

.progress-bar-warning {
    background-color: var(--warning);
}

.progress-bar-danger {
    background-color: var(--danger);
}

/* Skeleton Loading
   ========================================================================== */

.skeleton {
    display: inline-block;
    position: relative;
    overflow: hidden;
    background-color: var(--border-color);
    border-radius: var(--border-radius-sm);
}

.skeleton::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background-image: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0,
        rgba(255, 255, 255, 0.2) 20%,
        rgba(255, 255, 255, 0.5) 60%,
        rgba(255, 255, 255, 0)
    );
    animation: shimmer 2s infinite;
    content: '';
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
    width: 100%;
}

.skeleton-text:last-child {
    width: 80%;
}

.skeleton-circle {
    border-radius: 50%;
}

.skeleton-avatar {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
}

.skeleton-button {
    height: 2.5rem;
    width: 6rem;
    border-radius: var(--border-radius);
}

.skeleton-card {
    height: 200px;
    border-radius: var(--border-radius);
}

.dark-theme .skeleton {
    background-color: #333;
}

.dark-theme .skeleton::after {
    background-image: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0,
        rgba(255, 255, 255, 0.05) 20%,
        rgba(255, 255, 255, 0.1) 60%,
        rgba(255, 255, 255, 0)
    );
}
