# PersistenceOS Web UI Fix

This document provides solutions for fixing common issues with the PersistenceOS web interface.

## Common Issues

1. **403 Forbidden Error**: This typically occurs when Nginx doesn't have proper permissions to access files or directories.
2. **API Configuration Issues**: The system may be using localhost (127.0.0.1) instead of the actual server IP.
3. **Missing Configuration Files**: Files like `api-config.json` may be missing.

## Solution

We've updated several scripts to fix these issues:

1. `scripts/config.sh` has been enhanced to:
   - Create and configure the `api-config.json` file with the correct IP address
   - Set proper permissions for Nginx directories and web UI files
   - Restart Nginx with the correct configuration

2. Created a standalone fix script: `scripts/fix-web-ui.sh` that can be run any time to fix these issues.

## Applying the Fix

To apply these fixes, do the following on your PersistenceOS system:

1. Log in to your PersistenceOS system.
2. Run the existing configuration script:
   ```
   sudo /usr/lib/persistence/scripts/config.sh
   ```
3. If issues persist, run the dedicated fix script:
   ```
   sudo /usr/lib/persistence/scripts/fix-web-ui.sh
   ```
4. <PERSON>art Nginx to apply the changes:
   ```
   sudo systemctl restart nginx
   ```

## Verification

After applying the fixes, you should be able to access the web interface at:
- http://your-server-ip:8080
- https://your-server-ip:8443 (secure)

## Troubleshooting

If you still encounter issues:

1. Check Nginx logs:
   ```
   sudo tail -n 50 /var/log/nginx/error.log
   ```
2. Verify Nginx configuration:
   ```
   sudo nginx -t
   ```
3. Make sure the web UI files exist and have proper permissions:
   ```
   ls -la /usr/lib/persistence/web-ui/
   ```
4. Verify the API configuration:
   ```
   cat /usr/lib/persistence/web-ui/config/api-config.json
   ```

## Additional Resources

For more information, refer to the PersistenceOS documentation. 