/**
 * PersistenceOS Authentication-to-Dashboard Flow Test
 * This script verifies the complete user workflow from login to dashboard
 */

const FlowTest = {
    // Test configuration
    config: {
        testCredentials: { username: 'root', password: 'linux' },
        timeouts: { login: 5000, redirect: 3000, dashboard: 10000 },
        debug: true
    },

    // Test state
    state: {
        currentStep: 0,
        startTime: null,
        results: [],
        errors: []
    },

    // Test steps
    steps: [
        { name: 'Initial Page Load', test: 'testInitialPageLoad' },
        { name: 'Login Form Display', test: 'testLoginFormDisplay' },
        { name: 'Authentication Process', test: 'testAuthentication' },
        { name: 'Redirect to Dashboard', test: 'testRedirectToDashboard' },
        { name: 'Vue.js Dashboard Load', test: 'testVueDashboardLoad' },
        { name: 'System Data Display', test: 'testSystemDataDisplay' },
        { name: 'Navigation Functionality', test: 'testNavigationFunctionality' }
    ],

    // Logging utilities
    log: (message, data = null) => {
        if (FlowTest.config.debug) {
            console.log(`🧪 [FlowTest] ${message}`, data || '');
        }
    },

    error: (message, error = null) => {
        console.error(`❌ [FlowTest] ${message}`, error || '');
        FlowTest.state.errors.push({ message, error, step: FlowTest.state.currentStep });
    },

    success: (message, data = null) => {
        console.log(`✅ [FlowTest] ${message}`, data || '');
        FlowTest.state.results.push({ message, data, step: FlowTest.state.currentStep });
    },

    // Test execution
    async runAllTests() {
        this.log('Starting PersistenceOS authentication-to-dashboard flow test');
        this.state.startTime = Date.now();
        this.state.currentStep = 0;
        this.state.results = [];
        this.state.errors = [];

        for (const step of this.steps) {
            this.state.currentStep++;
            this.log(`Step ${this.state.currentStep}: ${step.name}`);
            
            try {
                const result = await this[step.test]();
                if (result) {
                    this.success(`${step.name} - PASSED`, result);
                } else {
                    this.error(`${step.name} - FAILED`);
                }
            } catch (error) {
                this.error(`${step.name} - ERROR`, error);
            }
        }

        this.generateReport();
    },

    // Individual test methods
    async testInitialPageLoad() {
        this.log('Testing initial page load...');
        
        // Check if we're on the correct page
        const isLoginPage = window.location.pathname.includes('login.html') || 
                           document.getElementById('login-form') !== null;
        
        if (!isLoginPage) {
            throw new Error('Not on login page');
        }

        // Check if login.js is loaded
        if (typeof LoginManager === 'undefined') {
            throw new Error('LoginManager not loaded');
        }

        return { page: 'login', loginManagerLoaded: true };
    },

    async testLoginFormDisplay() {
        this.log('Testing login form display...');
        
        const form = document.getElementById('login-form');
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        const loginButton = document.getElementById('login-button');

        if (!form || !usernameField || !passwordField || !loginButton) {
            throw new Error('Login form elements missing');
        }

        // Check if username is pre-filled
        const usernamePrefilled = usernameField.value === 'root';
        
        return { 
            formPresent: true, 
            usernamePrefilled,
            elementsFound: { form: !!form, username: !!usernameField, password: !!passwordField, button: !!loginButton }
        };
    },

    async testAuthentication() {
        this.log('Testing authentication process...');
        
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Authentication timeout'));
            }, this.config.timeouts.login);

            // Fill in credentials
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            
            if (usernameField) usernameField.value = this.config.testCredentials.username;
            if (passwordField) passwordField.value = this.config.testCredentials.password;

            // Monitor for authentication success
            const originalStoreAuth = Auth?.storeAuth;
            if (originalStoreAuth) {
                Auth.storeAuth = function(...args) {
                    clearTimeout(timeout);
                    Auth.storeAuth = originalStoreAuth; // Restore original
                    resolve({ authenticated: true, method: 'API or fallback' });
                    return originalStoreAuth.apply(this, args);
                };
            }

            // Trigger login
            const loginButton = document.getElementById('login-button');
            if (loginButton) {
                loginButton.click();
            } else {
                clearTimeout(timeout);
                reject(new Error('Login button not found'));
            }
        });
    },

    async testRedirectToDashboard() {
        this.log('Testing redirect to dashboard...');
        
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Redirect timeout'));
            }, this.config.timeouts.redirect);

            // Monitor for page change
            const checkRedirect = () => {
                if (window.location.pathname.includes('app.html')) {
                    clearTimeout(timeout);
                    resolve({ redirected: true, url: window.location.href });
                } else {
                    setTimeout(checkRedirect, 100);
                }
            };

            checkRedirect();
        });
    },

    async testVueDashboardLoad() {
        this.log('Testing Vue.js dashboard load...');
        
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Vue.js dashboard load timeout'));
            }, this.config.timeouts.dashboard);

            // Check if Vue is available
            if (typeof Vue === 'undefined') {
                clearTimeout(timeout);
                reject(new Error('Vue.js not loaded'));
                return;
            }

            // Monitor for Vue app initialization
            const checkVueApp = () => {
                const appElement = document.getElementById('app');
                if (appElement && appElement.innerHTML.includes('PersistenceOS')) {
                    clearTimeout(timeout);
                    resolve({ vueLoaded: true, appMounted: true });
                } else {
                    setTimeout(checkVueApp, 200);
                }
            };

            checkVueApp();
        });
    },

    async testSystemDataDisplay() {
        this.log('Testing system data display...');
        
        // Wait a moment for data to load
        await new Promise(resolve => setTimeout(resolve, 2000));

        const systemStats = document.querySelector('.system-stats');
        const dashboardGrid = document.querySelector('.dashboard-grid');
        const sidebar = document.querySelector('.sidebar');

        if (!systemStats || !dashboardGrid || !sidebar) {
            throw new Error('Dashboard components missing');
        }

        return { 
            systemStatsPresent: !!systemStats,
            dashboardGridPresent: !!dashboardGrid,
            sidebarPresent: !!sidebar
        };
    },

    async testNavigationFunctionality() {
        this.log('Testing navigation functionality...');
        
        const navItems = document.querySelectorAll('.nav-item');
        if (navItems.length === 0) {
            throw new Error('Navigation items not found');
        }

        // Test clicking on different sections
        const testSections = ['vms', 'storage', 'network'];
        const results = {};

        for (const section of testSections) {
            const navItem = document.querySelector(`[href="#${section}"]`);
            if (navItem) {
                navItem.click();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const sectionElement = document.getElementById(`${section}-section`);
                results[section] = !!sectionElement;
            }
        }

        return { navigationTested: testSections, results };
    },

    // Report generation
    generateReport() {
        const duration = Date.now() - this.state.startTime;
        const totalTests = this.steps.length;
        const passedTests = this.state.results.length;
        const failedTests = this.state.errors.length;

        console.log('\n' + '='.repeat(60));
        console.log('🧪 PERSISTENCEOS FLOW TEST REPORT');
        console.log('='.repeat(60));
        console.log(`⏱️  Duration: ${duration}ms`);
        console.log(`📊 Total Tests: ${totalTests}`);
        console.log(`✅ Passed: ${passedTests}`);
        console.log(`❌ Failed: ${failedTests}`);
        console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
        console.log('='.repeat(60));

        if (this.state.results.length > 0) {
            console.log('\n✅ PASSED TESTS:');
            this.state.results.forEach((result, index) => {
                console.log(`  ${index + 1}. ${result.message}`);
            });
        }

        if (this.state.errors.length > 0) {
            console.log('\n❌ FAILED TESTS:');
            this.state.errors.forEach((error, index) => {
                console.log(`  ${index + 1}. ${error.message}`);
                if (error.error) {
                    console.log(`     Error: ${error.error.message}`);
                }
            });
        }

        console.log('\n' + '='.repeat(60));
        
        // Return summary for programmatic use
        return {
            duration,
            totalTests,
            passedTests,
            failedTests,
            successRate: Math.round((passedTests / totalTests) * 100),
            results: this.state.results,
            errors: this.state.errors
        };
    }
};

// Auto-run tests if on login page
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => FlowTest.runAllTests(), 1000);
    });
} else {
    setTimeout(() => FlowTest.runAllTests(), 1000);
}

// Export for manual testing
window.FlowTest = FlowTest;
