:root {
    /* Color Palette */
    --color-primary: #0078d4;
    --color-primary-rgb: 0, 120, 212;
    --color-primary-dark: #005a9e;
    --color-primary-light: #2b88d8;
    --color-secondary: #6c757d;
    --color-secondary-rgb: 108, 117, 125;
    --color-success: #28a745;
    --color-success-rgb: 40, 167, 69;
    --color-info: #17a2b8;
    --color-info-rgb: 23, 162, 184;
    --color-warning: #ffc107;
    --color-warning-rgb: 255, 193, 7;
    --color-danger: #dc3545;
    --color-danger-rgb: 220, 53, 69;
    --color-error: #dc3545;
    --color-error-rgb: 220, 53, 69;
    --color-error-background: rgba(220, 53, 69, 0.1);
    
    /* Light Theme Colors */
    --color-background: #f8f9fa;
    --color-card-background: #ffffff;
    --color-input-background: #ffffff;
    --color-text: #212529;
    --color-text-secondary: #6c757d;
    --color-border: #dee2e6;
    --color-disabled: #adb5bd;
    --color-hover: rgba(0, 0, 0, 0.05);
    --color-active: rgba(0, 0, 0, 0.1);
    --color-sidebar-background: #2c3e50;
    --color-sidebar-text: #ffffff;
    --color-sidebar-active: #1a252f;
    --color-sidebar-hover: #34495e;
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-size-base: 16px;
    --font-size-sm: 0.875rem;
    --font-size-lg: 1.25rem;
    --font-weight-normal: 400;
    --font-weight-bold: 700;
    --line-height-base: 1.5;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 1rem;
    --spacing-4: 1.5rem;
    --spacing-5: 3rem;
    
    /* Borders */
    --border-width: 1px;
    --border-radius: 0.375rem;
    --border-radius-sm: 0.25rem;
    --border-radius-lg: 0.5rem;
    --border-radius-pill: 50rem;
    
    /* Shadows */
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Transitions */
    --transition-base: all 0.2s ease-in-out;
    --transition-fade: opacity 0.15s linear;
    
    /* Z-index */
    --z-index-dropdown: 1000;
    --z-index-sticky: 1020;
    --z-index-fixed: 1030;
    --z-index-modal-backdrop: 1040;
    --z-index-modal: 1050;
    --z-index-popover: 1060;
    --z-index-tooltip: 1070;
    
    /* Layout */
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 70px;
    --header-height: 60px;
    --footer-height: 40px;
    --content-max-width: 1200px;
    
    /* Charts */
    --chart-height-sm: 200px;
    --chart-height-md: 300px;
    --chart-height-lg: 400px;
    
    /* Cards */
    --card-padding: 1.25rem;
    --card-margin-bottom: 1.5rem;
    
    /* Tables */
    --table-cell-padding: 0.75rem;
    --table-border-color: var(--color-border);
    
    /* Forms */
    --input-padding-y: 0.375rem;
    --input-padding-x: 0.75rem;
    --input-height: calc(1.5em + 0.75rem + 2px);
    
    /* Buttons */
    --btn-padding-y: 0.375rem;
    --btn-padding-x: 0.75rem;
    --btn-font-size: 1rem;
    --btn-border-radius: var(--border-radius);
    
    /* Alerts */
    --alert-padding-y: 0.75rem;
    --alert-padding-x: 1.25rem;
    --alert-margin-bottom: 1rem;
    --alert-border-radius: var(--border-radius);
    
    /* Badges */
    --badge-padding-y: 0.25em;
    --badge-padding-x: 0.4em;
    --badge-border-radius: var(--border-radius-pill);
    
    /* Progress */
    --progress-height: 1rem;
    --progress-border-radius: var(--border-radius);
    
    /* Tooltips */
    --tooltip-padding-y: 0.25rem;
    --tooltip-padding-x: 0.5rem;
    --tooltip-border-radius: var(--border-radius);
    
    /* Popovers */
    --popover-padding-y: 0.5rem;
    --popover-padding-x: 0.75rem;
    --popover-border-radius: var(--border-radius);
    
    /* Modals */
    --modal-padding: 1rem;
    --modal-border-radius: var(--border-radius);
    
    /* Animations */
    --animation-duration-fast: 0.15s;
    --animation-duration-normal: 0.3s;
    --animation-duration-slow: 0.5s;
    
    /* Custom Components */
    --terminal-background: #1e1e1e;
    --terminal-text: #f8f8f8;
    --terminal-prompt: #4cd964;
    --terminal-font-family: "Courier New", monospace;
}

/* Dark Theme Variables - Will be applied when .dark-theme class is added to body */
.dark-theme {
    --color-background: #121212;
    --color-card-background: #1e1e1e;
    --color-input-background: #2c2c2c;
    --color-text: #e0e0e0;
    --color-text-secondary: #a0a0a0;
    --color-border: #333333;
    --color-disabled: #666666;
    --color-hover: rgba(255, 255, 255, 0.05);
    --color-active: rgba(255, 255, 255, 0.1);
    --color-sidebar-background: #1a1a1a;
    --color-sidebar-text: #e0e0e0;
    --color-sidebar-active: #0078d4;
    --color-sidebar-hover: #333333;
    
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.5);
    --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.6);
    
    --table-border-color: #333333;
}
