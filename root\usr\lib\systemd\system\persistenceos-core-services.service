[Unit]
Description=PersistenceOS Core Services Enablement
DefaultDependencies=no
Before=basic.target network.target
After=local-fs.target
Wants=local-fs.target
Conflicts=shutdown.target
ConditionPathExists=/usr/lib/persistence/scripts/config.sh

[Service]
Type=oneshot
RemainAfterExit=yes
# Set working directory to ensure proper file detection during OBS builds
WorkingDirectory=/usr/lib/persistence/scripts
# This service handles core functionality:
# 1. Run config.sh to install missing packages and configure system
# 2. NetworkManager (previously in network-manager.sh)
# 3. FastAPI web server (replacing Nginx)
# 4. Welcome screen with IP address display (previously in network-manager.sh)
# 5. Health checker service integration

# First enable this service for future boots
ExecStart=/bin/bash -c 'if [ ! -f /etc/systemd/system/basic.target.wants/persistenceos-core-services.service ]; then \
    mkdir -p /etc/systemd/system/basic.target.wants; \
    ln -sf /usr/lib/systemd/system/persistenceos-core-services.service /etc/systemd/system/basic.target.wants/persistenceos-core-services.service; \
    echo "Enabled persistenceos-core-services.service for future boots" >> /var/log/persistenceos-core-services.log; \
fi'

# Then run config.sh to install missing packages and configure system
# Use absolute path and ensure proper environment for file detection
# Redirect output to log file for debugging file detection issues
ExecStart=/bin/bash -c '/usr/lib/persistence/scripts/config.sh 2>&1 | tee -a /var/log/persistenceos-file-detection.log'

# Then ensure services are enabled
ExecStart=/bin/systemctl enable NetworkManager
ExecStart=/bin/systemctl enable persistenceos-api.service

# Setup welcome screen service and timer
ExecStart=/bin/mkdir -p /etc/systemd/system/
ExecStart=/bin/bash -c 'cat > /etc/systemd/system/persistenceos-welcome.service << EOF
[Unit]
Description=PersistenceOS Welcome Message Setup
After=network-online.target NetworkManager.service persistenceos-api.service
Wants=NetworkManager.service persistenceos-api.service network-online.target
Requires=network-online.target
ConditionPathExists=/usr/lib/persistence/scripts/config.sh

[Service]
Type=oneshot
# Add a small delay to ensure network interfaces are fully configured
ExecStartPre=/bin/sleep 3
ExecStart=/usr/lib/persistence/scripts/config.sh --update
StandardOutput=journal+console
StandardError=journal+console
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF'

ExecStart=/bin/bash -c 'cat > /etc/systemd/system/persistenceos-welcome.timer << EOF
[Unit]
Description=Periodically update PersistenceOS welcome message
After=network-online.target

[Timer]
OnBootSec=30
OnUnitActiveSec=1min

[Install]
WantedBy=timers.target
EOF'

# Set up image/config symlinks in bin directory
ExecStart=/bin/bash -c 'mkdir -p /usr/lib/persistence/bin'
ExecStart=/bin/bash -c 'if [ -f "/usr/lib/persistence/scripts/config.sh" ]; then \
    cp "/usr/lib/persistence/scripts/config.sh" "/usr/lib/persistence/bin/config"; \
    chmod +x "/usr/lib/persistence/bin/config"; \
    ln -sf "/usr/lib/persistence/scripts/config.sh" "/usr/lib/persistence/bin/image"; \
    chmod +x "/usr/lib/persistence/bin/image"; \
    mkdir -p "/usr/lib/persistence/script"; \
    ln -sf "/usr/lib/persistence/scripts/config.sh" "/usr/lib/persistence/script/config.sh"; \
fi'

# Create PersistenceOS health-checker integration
ExecStart=/bin/bash -c 'mkdir -p /usr/libexec/health-checker'
ExecStart=/bin/bash -c 'cat > /etc/systemd/system/persistenceos-health-checker.service << EOF
[Unit]
Description=PersistenceOS Health Checker Integration
After=health-checker.service
Wants=health-checker.service
ConditionPathExists=/usr/lib/persistence/scripts/config.sh

[Service]
Type=oneshot
ExecStart=/usr/lib/persistence/scripts/config.sh --health-check
StandardOutput=journal+console
StandardError=journal+console
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF'

# Create health-checker hooks
ExecStart=/bin/bash -c 'mkdir -p /usr/libexec/health-checker'
ExecStart=/bin/bash -c 'if [ ! -f "/usr/libexec/health-checker/etc-overlayfs.sh" ]; then \
    cat > "/usr/libexec/health-checker/etc-overlayfs.sh" << "EOFH" \
#!/bin/bash
check() {
    # Simple check that always succeeds for now
    # Real implementation will depend on specific requirements
    return 0
}
if [ "$1" = "check" ]; then
    check
fi
exit 0
EOFH
    chmod +x "/usr/libexec/health-checker/etc-overlayfs.sh"; \
fi'

# Create health checker configuration file if missing
ExecStart=/bin/bash -c 'mkdir -p /etc/health-checker.d/'
ExecStart=/bin/bash -c 'if [ ! -f "/etc/health-checker.d/persistenceos.conf" ]; then \
    cat > "/etc/health-checker.d/persistenceos.conf" << "EOFH" \
# PersistenceOS Health Checker configuration
CHECK_INTERVAL=300
SNAPSHOT_CHECK=yes
OVERLAY_CHECK=yes
UPDATE_CHECK=yes
SERVICE_CHECK=yes
BTRFS_CHECK=yes
EOFH
    chmod 644 "/etc/health-checker.d/persistenceos.conf"; \
fi'

# Create PersistenceOS specific health checker hook
ExecStart=/bin/bash -c 'if [ ! -f "/usr/libexec/health-checker/persistenceos.sh" ]; then \
    cat > "/usr/libexec/health-checker/persistenceos.sh" << "EOFH" \
#!/bin/bash
check() {
    # Check if core PersistenceOS directories exist
    [ -d "/usr/lib/persistence" ] || return 1
    [ -d "/var/lib/persistence" ] || return 1

    # Check if web server is accessible
    if command -v curl >/dev/null 2>&1; then
        curl -f -s -m 2 http://localhost:8080/api/health >/dev/null || return 1
    fi

    # All checks passed
    return 0
}

if [ "$1" = "check" ]; then
    check
fi
exit 0
EOFH
    chmod +x "/usr/libexec/health-checker/persistenceos.sh"; \
fi'

# Enable health-checker and our integration
ExecStart=/bin/systemctl enable health-checker.service
ExecStart=/bin/systemctl enable persistenceos-health-checker.service

# Enable welcome screen service and timer
ExecStart=/bin/systemctl enable persistenceos-welcome.service
ExecStart=/bin/systemctl enable persistenceos-welcome.timer

# Then start all services in correct order
ExecStart=/bin/systemctl start NetworkManager

# Generate self-signed SSL certificate if it doesn't exist
ExecStart=/bin/bash -c 'if [ ! -f "/etc/ssl/persistenceos/persistenceos.crt" ]; then \
    mkdir -p "/etc/ssl/persistenceos"; \
    openssl req -x509 -nodes -days 3650 -newkey rsa:2048 \
        -keyout "/etc/ssl/persistenceos/persistenceos.key" \
        -out "/etc/ssl/persistenceos/persistenceos.crt" \
        -subj "/C=US/ST=State/L=City/O=PersistenceOS/CN=persistenceos.local" 2>/dev/null || echo "Warning: Failed to generate SSL certificate"; \
    chmod 600 "/etc/ssl/persistenceos/persistenceos.key"; \
    chmod 644 "/etc/ssl/persistenceos/persistenceos.crt"; \
fi'

# Start FastAPI service
ExecStart=/bin/systemctl start persistenceos-api.service

# Ensure web-ui directory exists and has at least a basic index.html
ExecStart=/bin/bash -c 'mkdir -p /usr/lib/persistence/web-ui; chmod 755 /usr/lib/persistence/web-ui'
ExecStart=/bin/bash -c 'if [ ! -f "/usr/lib/persistence/web-ui/index.html" ]; then \
    echo "<!DOCTYPE html><html><head><title>PersistenceOS</title></head><body><h1>PersistenceOS</h1><p>Web interface initializing...</p></body></html>" > /usr/lib/persistence/web-ui/index.html; \
    chmod 644 /usr/lib/persistence/web-ui/index.html; \
fi'

# Add a delay to ensure NetworkManager has time to establish connections
ExecStart=/bin/sleep 5
ExecStart=/bin/systemctl start persistenceos-welcome.service
ExecStart=/bin/systemctl start persistenceos-welcome.timer
ExecStart=/bin/systemctl start health-checker.service
ExecStart=/bin/systemctl start persistenceos-health-checker.service

# Create direct symlinks in case systemctl enable fails
ExecStart=/bin/mkdir -p /etc/systemd/system/multi-user.target.wants/
ExecStart=/bin/ln -sf /usr/lib/systemd/system/NetworkManager.service /etc/systemd/system/multi-user.target.wants/NetworkManager.service
ExecStart=/bin/ln -sf /etc/systemd/system/persistenceos-api.service /etc/systemd/system/multi-user.target.wants/persistenceos-api.service
ExecStart=/bin/ln -sf /etc/systemd/system/persistenceos-welcome.service /etc/systemd/system/multi-user.target.wants/persistenceos-welcome.service
ExecStart=/bin/ln -sf /etc/systemd/system/persistenceos-welcome.timer /etc/systemd/system/timers.target.wants/persistenceos-welcome.timer
ExecStart=/bin/ln -sf /usr/lib/systemd/system/health-checker.service /etc/systemd/system/multi-user.target.wants/health-checker.service
ExecStart=/bin/ln -sf /etc/systemd/system/persistenceos-health-checker.service /etc/systemd/system/multi-user.target.wants/persistenceos-health-checker.service

# Ensure MOTD is displayed properly
ExecStart=/bin/bash -c 'mkdir -p /etc/profile.d/'
ExecStart=/bin/bash -c 'if [ -f "/usr/lib/persistence/scripts/config.sh" ]; then \
    echo "# Adding PersistenceOS welcome message hook to /etc/profile" >> /var/log/persistenceos-core-services.log; \
    if ! grep -q "PersistenceOS welcome message" "/etc/profile" 2>/dev/null; then \
        cat >> "/etc/profile" << "EOF" \
\n# Display PersistenceOS welcome message on login for interactive shells \
\nif [[ \$- == *i* ]]; then \
\n    if [[ -f /usr/lib/persistence/bin/image ]]; then \
\n        /usr/lib/persistence/bin/image --login \
\n    elif [[ -f /usr/lib/persistence/scripts/config.sh ]]; then \
\n        /usr/lib/persistence/scripts/config.sh --login \
\n    fi \
\nfi \
EOF
    fi \
fi'

# Ensure /etc/motd.d exists and is properly linked
ExecStart=/bin/bash -c 'mkdir -p /etc/motd.d/'
ExecStart=/bin/bash -c 'if [ -f "/etc/motd" ]; then ln -sf /etc/motd /etc/motd.d/persistenceos.motd; fi'

# Create a dynamic motd copy
ExecStart=/bin/bash -c 'if [ -f "/etc/motd" ]; then mkdir -p /run && cp /etc/motd /run/motd.dynamic; fi'

# Make sure getty displays issue
ExecStart=/bin/bash -c 'mkdir -p /etc/systemd/system/getty@.service.d/'
ExecStart=/bin/bash -c 'echo "[Service]" > /etc/systemd/system/getty@.service.d/persistenceos.conf'
ExecStart=/bin/bash -c 'echo "# Override the ExecStart directive to prevent clearing the screen" >> /etc/systemd/system/getty@.service.d/persistenceos.conf'
ExecStart=/bin/bash -c 'echo "ExecStart=" >> /etc/systemd/system/getty@.service.d/persistenceos.conf'
ExecStart=/bin/bash -c 'echo "ExecStart=-/sbin/agetty --noclear --keep-baud %I 115200,38400,9600 \\$TERM" >> /etc/systemd/system/getty@.service.d/persistenceos.conf'
ExecStart=/bin/systemctl daemon-reload

# Verify welcome screen files are created
ExecStart=/bin/bash -c 'echo "Verifying welcome screen files..." > /var/log/persistenceos-core-services.log'
ExecStart=/bin/bash -c 'if [ -f /etc/motd ] && [ -s /etc/motd ]; then echo "MOTD file exists and has content" >> /var/log/persistenceos-core-services.log; else echo "ERROR: MOTD file missing or empty!" >> /var/log/persistenceos-core-services.log; fi'
ExecStart=/bin/bash -c 'if [ -f /etc/issue ] && [ -s /etc/issue ]; then echo "Issue file exists and has content" >> /var/log/persistenceos-core-services.log; else echo "ERROR: Issue file missing or empty!" >> /var/log/persistenceos-core-services.log; fi'

# Force update the welcome message
ExecStart=/bin/bash -c 'if [ -f "/usr/lib/persistence/bin/image" ]; then /usr/lib/persistence/bin/image --force; elif [ -f "/usr/lib/persistence/scripts/config.sh" ]; then /usr/lib/persistence/scripts/config.sh --force; fi'

# Verify JavaScript files are available (either from RPM or config.sh deployment)
ExecStart=/bin/bash -c 'js_count=0; for js_file in app.js auth.js vue.js login.js; do if [ -f "/usr/lib/persistence/web-ui/js/$js_file" ]; then js_count=$((js_count + 1)); echo "✅ $js_file available: $(stat -c%s /usr/lib/persistence/web-ui/js/$js_file) bytes" >> /var/log/persistenceos-core-services.log; fi; done; echo "JavaScript files available: $js_count/4" >> /var/log/persistenceos-core-services.log'

# Verify web server is running
ExecStart=/bin/bash -c 'if systemctl is-active --quiet persistenceos-api; then echo "Web server is running" >> /var/log/persistenceos-core-services.log; else echo "ERROR: Web server is not running!" >> /var/log/persistenceos-core-services.log; systemctl start persistenceos-api; fi'

# Verify health-checker is running
ExecStart=/bin/bash -c 'if systemctl is-active --quiet health-checker; then echo "Health checker is running" >> /var/log/persistenceos-core-services.log; else echo "ERROR: Health checker is not running!" >> /var/log/persistenceos-core-services.log; systemctl start health-checker; fi'

# Log success
ExecStart=/bin/bash -c 'echo "Core services enabled and started successfully" >> /var/log/persistenceos-core-services.log'

[Install]
WantedBy=basic.target