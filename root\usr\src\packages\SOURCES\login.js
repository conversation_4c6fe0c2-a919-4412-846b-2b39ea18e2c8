/**
 * PersistenceOS Self-Contained Login Script
 * This file creates its own HTML layout and handles all login functionality
 * Perfect for OBS builds where file detection is challenging
 */

// Configuration
const LOGIN_CONFIG = {
    API_BASE_URL: '/api',
    REDIRECT_URL: '/app.html',
    DEFAULT_USERNAME: 'root',
    DEFAULT_PASSWORD: 'linux',
    DEBUG: true,
    STANDALONE_MODE: false  // ✅ FIXED: Don't overwrite existing HTML
};

// HTML Layout Generator
const LoginHTML = {
    createFullPage() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login</title>
    <link rel="icon" href="img/favicon.ico" type="image/x-icon">
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0a4b78 0%, #003366 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #333;
        }
        .login-container { width: 400px; max-width: 95%; }
        .login-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .login-header {
            background: #0066cc;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .login-header h1 { margin: 0 0 5px 0; font-size: 28px; }
        .login-header .version { margin: 5px 0 0 0; opacity: 0.8; font-size: 14px; }
        .logo {
            width: 60px;
            height: 60px;
            margin-bottom: 10px;
        }
        .login-form { padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; font-size: 14px; }
        input[type="text"], input[type="password"] {
            width: 100%; padding: 10px; border: 1px solid #ddd;
            border-radius: 4px; font-size: 16px;
        }
        .remember-me { display: flex; align-items: center; }
        .remember-me input { margin-right: 8px; }
        .btn {
            padding: 12px; border: none; border-radius: 4px;
            font-size: 16px; cursor: pointer; transition: background 0.3s;
        }
        .btn-primary { background: #0066cc; color: white; }
        .btn-primary:hover { background: #0055aa; }
        .login-footer {
            text-align: center; padding: 20px; border-top: 1px solid #eee;
            font-size: 12px; color: #777;
        }
        .copyright { margin-top: 10px; font-size: 11px; }
        .error-message {
            color: #e74c3c; margin-top: 10px; text-align: center;
            padding: 8px; border-radius: 4px;
            background-color: rgba(231, 76, 60, 0.1);
        }
        .status-message {
            color: #28a745; margin-top: 10px; text-align: center;
            padding: 8px; border-radius: 4px;
            background-color: rgba(40, 167, 69, 0.1);
        }
        .hidden { display: none !important; }
    </style>
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <img src="img/logo.svg" alt="PersistenceOS Logo" class="logo">
                <h1>PersistenceOS</h1>
                <p class="version">Version: 6.1.0</p>
            </div>
            <div class="login-form">
                <form id="login-form" action="javascript:void(0);">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" placeholder="root" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" placeholder="Enter password" required>
                    </div>
                    <div class="form-group">
                        <div class="remember-me">
                            <input type="checkbox" id="remember" name="remember">
                            <label for="remember">Remember me</label>
                        </div>
                    </div>
                    <div id="login-error" class="error-message hidden">Invalid username or password</div>
                    <div class="form-group">
                        <button type="button" id="login-button" class="btn btn-primary" style="width: 100%;">Log In</button>
                    </div>
                </form>
            </div>
            <div class="login-footer">
                <p>PersistenceOS is a specialized hypervisor and NAS operating system based on SUSE Micro Leap 6.1.</p>
                <p>Default login: <strong>root</strong> / <strong>linux</strong> (same as system login)</p>
                <p class="copyright">&copy; 2024 PersistenceOS Team</p>
            </div>
        </div>
    </div>
</body>
</html>`;
    },

    injectIntoCurrentPage() {
        // Replace the entire document with the login page
        document.open();
        document.write(this.createFullPage());
        document.close();

        // Re-initialize after injection
        setTimeout(() => this.initializeAfterInjection(), 100);
    },

    initializeAfterInjection() {
        // Re-run the login initialization after HTML injection
        if (typeof LoginManager !== 'undefined') {
            LoginManager.initializeLoginPage();
        }
    }
};

// Utility functions (same as your current login.js)
const Logger = {
    log: (message, ...args) => {
        if (LOGIN_CONFIG.DEBUG) {
            console.log(`🔐 [Login] ${message}`, ...args);
        }
    },
    error: (message, ...args) => {
        console.error(`❌ [Login] ${message}`, ...args);
    },
    success: (message, ...args) => {
        if (LOGIN_CONFIG.DEBUG) {
            console.log(`✅ [Login] ${message}`, ...args);
        }
    }
};

// Authentication utilities (same as your current login.js)
const Auth = {
    storeAuth: (data, rememberMe = false) => {
        const storage = rememberMe ? localStorage : sessionStorage;

        // Store authentication data in format compatible with auth.js
        storage.setItem('persistenceos_token', data.access_token);
        storage.setItem('persistenceos_user', JSON.stringify(data.user || {
            username: data.username,
            display_name: data.username === 'root' ? 'Administrator' : data.username,
            roles: data.username === 'root' ? ['admin'] : ['user']
        }));
        storage.setItem('persistenceos_token_expiry', data.expires_at || '');
        storage.setItem('authenticated', 'true');
        storage.setItem('username', data.username || '');

        Logger.success('Authentication data stored', { rememberMe });
    },

    clearAuth: () => {
        ['localStorage', 'sessionStorage'].forEach(storageType => {
            const storage = window[storageType];
            [
                'persistenceos_token', 'persistenceos_user', 'persistenceos_token_expiry',
                'authenticated', 'username'
            ].forEach(key => storage.removeItem(key));
        });
        Logger.log('Authentication data cleared');
    },

    isAuthenticated: () => {
        const localAuth = localStorage.getItem('authenticated') === 'true';
        const sessionAuth = sessionStorage.getItem('authenticated') === 'true';
        return localAuth || sessionAuth;
    }
};

// UI utilities (same as your current login.js)
const UI = {
    showError: (message) => {
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }
        Logger.error('UI Error:', message);
    },

    hideError: () => {
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.classList.add('hidden');
        }
    },

    showStatus: (message) => {
        // Since login.html doesn't have login-status element, we'll use the error element for status
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.color = '#28a745'; // Green for status
            errorElement.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
            errorElement.classList.remove('hidden');
        }
        Logger.log('Status:', message);
    },

    hideStatus: () => {
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.classList.add('hidden');
            // Reset to error styling
            errorElement.style.color = '#e74c3c';
            errorElement.style.backgroundColor = 'rgba(231, 76, 60, 0.1)';
        }
    },

    setButtonLoading: (loading = true) => {
        const button = document.getElementById('login-button');
        if (button) {
            button.disabled = loading;
            button.textContent = loading ? 'Logging in...' : 'Log In';
        }
    }
};

// Main login functionality (same as your current login.js)
const LoginManager = {
    async authenticateWithAPI(username, password) {
        Logger.log('Attempting API authentication...');
        const response = await fetch(`${LOGIN_CONFIG.API_BASE_URL}/auth/token`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({
                'username': username,
                'password': password,
                'grant_type': 'password'
            })
        });

        Logger.log(`API response status: ${response.status}`);
        if (response.ok) {
            const data = await response.json();
            data.username = username;
            return { success: true, data };
        } else {
            const errorData = await response.json().catch(() => ({}));
            return {
                success: false,
                error: errorData.detail || 'Invalid username or password'
            };
        }
    },

    authenticateWithFallback(username, password) {
        Logger.log('Using fallback authentication...');
        if (username === LOGIN_CONFIG.DEFAULT_USERNAME && password === LOGIN_CONFIG.DEFAULT_PASSWORD) {
            return {
                success: true,
                data: {
                    access_token: 'fallback_token',
                    username: username,
                    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
                }
            };
        }
        return { success: false, error: 'Invalid credentials' };
    },

    redirectToApp() {
        Logger.success('Redirecting to application...');
        UI.showStatus('Login successful! Redirecting...');
        setTimeout(() => {
            // Redirect directly without from_login parameter to avoid race conditions
            const redirectUrl = LOGIN_CONFIG.REDIRECT_URL;
            Logger.log(`Redirecting to: ${redirectUrl}`);
            window.location.href = redirectUrl;
        }, 500); // Reduced timeout to minimize delay
    },

    async handleLogin(username, password, rememberMe = false) {
        Logger.log('Starting login process...', { username, rememberMe });
        UI.hideError();
        UI.showStatus('Logging in...');
        UI.setButtonLoading(true);

        try {
            let result = await this.authenticateWithAPI(username, password);
            if (!result.success) {
                Logger.log('API authentication failed, trying fallback...');
                result = this.authenticateWithFallback(username, password);
            }

            if (result.success) {
                Auth.storeAuth(result.data, rememberMe);
                this.redirectToApp();
            } else {
                UI.showError(result.error);
                UI.hideStatus();
            }
        } catch (error) {
            Logger.error('Login process failed:', error);
            const fallbackResult = this.authenticateWithFallback(username, password);
            if (fallbackResult.success) {
                Logger.log('Network error, but fallback succeeded');
                Auth.storeAuth(fallbackResult.data, rememberMe);
                UI.showStatus('Network error, using offline mode...');
                setTimeout(() => this.redirectToApp(), 2000);
            } else {
                UI.showError('Connection error. Please try again.');
                UI.hideStatus();
            }
        } finally {
            UI.setButtonLoading(false);
        }
    },

    initializeLoginPage() {
        Logger.log('Initializing login page...');

        // Check if already authenticated
        if (Auth.isAuthenticated()) {
            Logger.log('User already authenticated, redirecting...');
            this.redirectToApp();
            return;
        }

        // Get form elements
        const loginForm = document.getElementById('login-form');
        const loginButton = document.getElementById('login-button');
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        const rememberField = document.getElementById('remember');

        // Validate required elements exist
        if (!loginButton || !usernameField || !passwordField) {
            Logger.error('Required form elements not found');
            UI.showError('Login form not properly initialized');
            return;
        }

        // Set default username if empty
        if (!usernameField.value) {
            usernameField.value = LOGIN_CONFIG.DEFAULT_USERNAME;
        }

        // Login button click handler
        loginButton.addEventListener('click', async (e) => {
            e.preventDefault();
            const username = usernameField.value.trim();
            const password = passwordField.value;
            const rememberMe = rememberField ? rememberField.checked : false;

            if (!username || !password) {
                UI.showError('Username and password are required');
                return;
            }

            await this.handleLogin(username, password, rememberMe);
        });

        // Form submission handler
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                loginButton.click();
            });
        }

        // Enter key handler
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                loginButton.click();
            }
        });

        // Focus on password field (username is pre-filled)
        if (usernameField.value) {
            passwordField.focus();
        } else {
            usernameField.focus();
        }

        Logger.success('Login page ready');
    }
};

// Initialize based on mode
if (LOGIN_CONFIG.STANDALONE_MODE && document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        LoginHTML.injectIntoCurrentPage();
    });
} else if (document.readyState === 'complete' || document.readyState === 'interactive') {
    const loginForm = document.getElementById('login-form');
    if (!loginForm && LOGIN_CONFIG.STANDALONE_MODE) {
        LoginHTML.injectIntoCurrentPage();
    } else {
        LoginManager.initializeLoginPage();
    }
} else {
    document.addEventListener('DOMContentLoaded', () => {
        const loginForm = document.getElementById('login-form');
        if (!loginForm && LOGIN_CONFIG.STANDALONE_MODE) {
            LoginHTML.injectIntoCurrentPage();
        } else {
            LoginManager.initializeLoginPage();
        }
    });
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LoginManager, Auth, UI, Logger, LoginHTML };
}