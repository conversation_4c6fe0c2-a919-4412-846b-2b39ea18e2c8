[    0s] Using BUILD_ROOT=/var/cache/obs/worker/root_24/.mount
[    0s] Using BUILD_ARCH=x86_64:i686:i586:i486:i386
[    0s] Doing kvm build in /var/cache/obs/worker/root_24/root
[    0s] 
[    0s] 
[    0s] h02-ch2a started "build PersistenceOS.kiwi" at Thu May 29 00:17:48 UTC 2025.
[    0s] 
[    0s] Building PersistenceOS for project 'home:Ivoslav:branches:SUSE:Templates:Images:SL-Micro-6.1' repository 'images' arch 'x86_64' srcmd5 'cd5e37229422cdb30f9efb73972a82a4'
[    0s] 
[    0s] processing recipe /var/cache/obs/worker/root_24/.build-srcdir/PersistenceOS.kiwi ...
[    0s] running changelog2spec --target rpm --emailonly --file /var/cache/obs/worker/root_24/.build-srcdir/PersistenceOS.kiwi
[    0s] init_buildsystem --configdir /var/run/obs/worker/24/build/configs --cachedir /var/cache/build --prepare --clean --rpmlist /var/cache/obs/worker/root_24/.build.rpmlist /var/cache/obs/worker/root_24/.build-srcdir/PersistenceOS.kiwi build ...
[    1s] cycle: rpm-config-SUSE -> rpm
[    1s]   breaking dependency rpm-config-SUSE -> rpm
[    1s] [1/34] preinstalling filesystem...
[    1s] [2/34] preinstalling permissions...
[    1s] [3/34] preinstalling glibc...
[    1s] [4/34] preinstalling diffutils...
[    1s] [5/34] preinstalling fillup...
[    1s] [6/34] preinstalling libacl1...
[    1s] [7/34] preinstalling libattr1...
[    1s] [8/34] preinstalling libbz2-1...
[    1s] [9/34] preinstalling libcap2...
[    1s] [10/34] preinstalling libeconf0...
[    1s] [11/34] preinstalling libgcc_s1...
[    1s] [12/34] preinstalling libgpg-error0...
[    1s] [13/34] preinstalling liblua5_4-5...
[    1s] [14/34] preinstalling liblzma5...
[    1s] [15/34] preinstalling libpcre2-8-0...
[    1s] [16/34] preinstalling libpopt0...
[    1s] [17/34] preinstalling libz1...
[    1s] [18/34] preinstalling libzstd1...
[    1s] [19/34] preinstalling attr...
[    1s] [20/34] preinstalling libgcrypt20...
[    1s] [21/34] preinstalling libncurses6...
[    1s] [22/34] preinstalling libselinux1...
[    1s] [23/34] preinstalling libelf1...
[    1s] [24/34] preinstalling libreadline8...
[    1s] [25/34] preinstalling sed...
[    1s] [26/34] preinstalling tar...
[    2s] [27/34] preinstalling bash...
[    2s] [28/34] preinstalling bash-sh...
[    2s] [29/34] preinstalling grep...
[    2s] [30/34] preinstalling pam...
[    2s] [31/34] preinstalling coreutils...
[    2s] [32/34] preinstalling aaa_base...
[    2s] [33/34] preinstalling rpm-config-SUSE...
[    2s] [34/34] preinstalling rpm...
[    2s] 
[    2s] [1/9] preinstalling kernel-obs-build...
[    2s] [2/9] preinstalling libblkid1...
[    2s] [3/9] preinstalling libcrypt1...
[    2s] [4/9] preinstalling libdb-4_8...
[    2s] [5/9] preinstalling libsmartcols1...
[    2s] [6/9] preinstalling libuuid1...
[    2s] [7/9] preinstalling libmount1...
[    2s] [8/9] preinstalling perl-base...
[    2s] [9/9] preinstalling util-linux...
[    2s] copying packages...
[    2s] reordering...cycle: libncurses6 -> terminfo-base
[    2s]   breaking dependency terminfo-base -> libncurses6
[    2s] cycle: binutils -> libctf0
[    2s]   breaking dependency binutils -> libctf0
[    2s] cycle: gio-branding-upstream -> libgio-2_0-0
[    2s]   breaking dependency libgio-2_0-0 -> gio-branding-upstream
[    2s] cycle: libgio-2_0-0 -> glib2-tools
[    2s]   breaking dependency libgio-2_0-0 -> glib2-tools
[    2s] cycle: openssl -> openssl-3
[    2s]   breaking dependency openssl-3 -> openssl
[    2s] cycle: python311-base -> libpython3_11-1_0
[    2s]   breaking dependency python311-base -> libpython3_11-1_0
[    2s] cycle: grub2 -> grub2-i386-pc
[    2s]   breaking dependency grub2 -> grub2-i386-pc
[    2s] cycle: rpm -> rpm-config-SUSE
[    2s]   breaking dependency rpm -> rpm-config-SUSE
[    2s] cycle: libcrack2 -> cracklib
[    2s]   breaking dependency cracklib -> libcrack2
[    2s] done
[    3s] Detected virtio-serial support
[    4s] booting kvm...
[    4s] ### VM INTERACTION START ###
[    4s] Using virtio-serial support and enabling console input
[    4s] /usr/bin/qemu-kvm -nodefaults -no-reboot -nographic -vga none -cpu host -M pc,accel=kvm,usb=off,dump-guest-core=off,vmport=off -sandbox on -bios /usr/share/qemu/qboot.rom -object rng-random,filename=/dev/random,id=rng0 -device virtio-rng-pci,rng=rng0 -object iothread,id=io0 -runas qemu -net none -kernel /var/cache/obs/worker/root_24/.mount/boot/kernel -initrd /var/cache/obs/worker/root_24/.mount/boot/initrd -append root=/dev/disk/by-id/virtio-0 rootfstype=ext4 rootflags=noatime elevator=noop nmi_watchdog=0 rw ia32_emulation=1 oops=panic panic=1 quiet console=hvc0 init=/.build/build -m 16384 -drive file=/var/cache/obs/worker/root_24/root,format=raw,if=none,id=disk,cache=unsafe,aio=io_uring -device virtio-blk-pci,iothread=io0,drive=disk,serial=0 -drive file=/var/cache/obs/worker/root_24/swap,format=raw,if=none,id=swap,cache=unsafe,aio=io_uring -device virtio-blk-pci,iothread=io0,drive=swap,serial=1 -device virtio-serial,max_ports=2 -device virtconsole,chardev=virtiocon0 -chardev stdio,mux=on,id=virtiocon0 -mon chardev=virtiocon0 -chardev socket,id=monitor,server=on,wait=off,path=/var/cache/obs/worker/root_24/root.qemu/monitor -mon chardev=monitor,mode=readline -smp 4
[    4s] qemu-kvm: -runas qemu: warning: -runas is deprecated, use '-run-with user=...' instead
[    5s] [    0.558845][    T1] systemd[1]: Failed to start Virtual Console Setup.
[    5s] [[0;1;31mFAILED[0m] Failed to start [0;1;39mVirtual Console Setup[0m.
[    6s] [[0;1;31mFAILED[0m] Failed to start [0;1;39mVirtual Console Setup[0m.
[    6s] ### VM INTERACTION END ###
[    6s] 2nd stage started in virtual machine
[    6s] machine type: x86_64
[    6s] Linux version: 6.4.0-29-default #1 SMP PREEMPT_DYNAMIC Tue May 13 08:46:46 UTC 2025 (59fa11f)
[    6s] Time: Thu May 29 00:17:54 UTC 2025
[    6s] Increasing log level from now on...
[    6s] [    2.101862][  T408] sysrq: Changing Loglevel
[    6s] [    2.101921][  T408] sysrq: Loglevel set to 4
[    6s] Enable sysrq operations
[    6s] Setting up swapspace version 1, size = 4 GiB (********** bytes)
[    6s] no label, UUID=e3432cb2-76fd-420e-ac4b-26514e108091
[    6s] WARNING: udev not running, creating extra device nodes
[    6s] logging output to //.build.log...
[    6s] processing recipe //.build-srcdir/PersistenceOS.kiwi ...
[    6s] init_buildsystem --configdir /.build/configs --cachedir /var/cache/build //.build-srcdir/PersistenceOS.kiwi build ...
[    6s] initializing rpm db...
[    6s] querying package ids...
[    7s] [1/293] cumulate boost-license1_84_0-1.84.0-slfo.1.1_1.4
[    7s] [2/293] cumulate btrfsprogs-udev-rules-6.8.1-slfo.1.1_1.2
[    7s] [3/293] cumulate compat-usrmerge-tools-84.87-slfo.1.1_1.5
[    7s] [4/293] cumulate cracklib-dict-small-2.9.11-slfo.1.1_1.2
[    7s] [5/293] cumulate crypto-policies-20230920.570ea89-slfo.1.1_1.2
[    7s] [6/293] cumulate file-magic-5.44-slfo.1.1_1.4
[    7s] [7/293] cumulate kernel-obs-build-6.4.0-29.1
[    7s] [8/293] cumulate libsemanage-conf-3.5-slfo.1.1_1.3
[    7s] [9/293] cumulate libssh-config-0.10.6-slfo.1.1_1.3
[    7s] [10/293] cumulate libtirpc-netconfig-1.3.4-slfo.1.1_1.2
[    7s] [11/293] cumulate pkgconf-m4-1.8.0-slfo.1.1_1.5
[    7s] [12/293] cumulate python-rpm-macros-20240618.1e386da-slfo.1.1_1.2
[    7s] [13/293] cumulate system-user-root-20190513-slfo.1.1_1.2
[    7s] [14/293] cumulate filesystem-84.87-slfo.1.1_1.2
[    7s] [15/293] cumulate glibc-2.38-slfo.1.1_4.1
[    7s] [16/293] cumulate diffutils-3.10-slfo.1.1_1.3
[    7s] [17/293] cumulate dosfstools-4.2-slfo.1.1_1.2
[    7s] [18/293] cumulate fillup-1.42-slfo.1.1_2.2
[    7s] [19/293] cumulate libacl1-2.3.1-slfo.1.1_1.3
[    7s] [20/293] cumulate libaio1-0.3.113-slfo.1.1_1.2
[    7s] [21/293] cumulate libalternatives1-1.2+30.a5431e9-slfo.1.1_1.3
[    7s] [22/293] cumulate libargon2-1-20190702-slfo.1.1_1.2
[    7s] [23/293] cumulate libatomic1-14.2.0+git10526-slfo.1.1_2.1
[    7s] [24/293] cumulate libattr1-2.5.1-slfo.1.1_1.3
[    7s] [25/293] cumulate libaudit1-3.1.1-slfo.1.1_1.3
[    7s] [26/293] cumulate libbrotlicommon1-1.1.0-slfo.1.1_1.3
[    7s] [27/293] cumulate libburn4-1.5.4-slfo.1.1_1.2
[    7s] [28/293] cumulate libbz2-1-1.0.8-slfo.1.1_1.4
[    7s] [29/293] cumulate libcap-ng0-0.8.3-slfo.1.1_1.4
[    7s] [30/293] cumulate libcap2-2.69-slfo.1.1_1.3
[    7s] [31/293] cumulate libcom_err2-1.47.0-slfo.1.1_1.2
[    7s] [32/293] cumulate libcrypt1-4.4.36-slfo.1.1_1.4
[    7s] [33/293] cumulate libeconf0-0.7.2-slfo.1.1_1.3
[    7s] [34/293] cumulate libefivar1-38-slfo.1.1_1.2
[    7s] [35/293] cumulate libexpat1-2.7.1-slfo.1.1_1.1
[    7s] [36/293] cumulate libfa1-1.14.1-slfo.1.1_1.3
[    7s] [37/293] cumulate libffi8-3.4.6-slfo.1.1_1.4
[    7s] [38/293] cumulate libfuse2-2.9.9-slfo.1.1_1.2
[    7s] [39/293] cumulate libgcc_s1-14.2.0+git10526-slfo.1.1_2.1
[    7s] [40/293] cumulate libgdbm6-1.23-slfo.1.1_1.3
[    7s] [41/293] cumulate libgmp10-6.3.0-slfo.1.1_1.5
[    7s] [42/293] cumulate libgomp1-14.2.0+git10526-slfo.1.1_2.1
[    7s] [43/293] cumulate libgpg-error0-1.47-slfo.1.1_1.3
[    7s] [44/293] cumulate libitm1-14.2.0+git10526-slfo.1.1_2.1
[    7s] [45/293] cumulate libjitterentropy3-3.4.1-slfo.1.1_1.3
[    7s] [46/293] cumulate libjson-c5-0.16-slfo.1.1_1.2
[    7s] [47/293] cumulate liblua5_4-5-5.4.6-slfo.1.1_1.3
[    7s] [48/293] cumulate liblz4-1-1.9.4-slfo.1.1_1.2
[    7s] [49/293] cumulate liblzma5-5.4.3-slfo.1.1_1.4
[    7s] [50/293] cumulate liblzo2-2-2.10-slfo.1.1_1.3
[    7s] [51/293] cumulate libmpdec3-2.5.1-slfo.1.1_1.4
[    7s] [52/293] cumulate libnettle8-3.9.1-slfo.1.1_1.2
[    7s] [53/293] cumulate libnghttp2-14-1.52.0-slfo.1.1_1.4
[    7s] [54/293] cumulate libnpth0-1.6-slfo.1.1_1.2
[    7s] [55/293] cumulate libnuma1-********.g3871b1c-slfo.1.1_1.2
[    7s] [56/293] cumulate libparted-fs-resize0-3.5-slfo.1.1_1.2
[    7s] [57/293] cumulate libpcre2-8-0-10.42-slfo.1.1_1.4
[    7s] [58/293] cumulate libpkgconf3-1.8.0-slfo.1.1_1.5
[    7s] [59/293] cumulate libpopt0-1.19-slfo.1.1_1.3
[    7s] [60/293] cumulate libsasl2-3-2.1.28-slfo.1.1_1.2
[    7s] [61/293] cumulate libseccomp2-2.5.4-slfo.1.1_1.4
[    7s] [62/293] cumulate libsepol2-3.5-slfo.1.1_1.3
[    7s] [63/293] cumulate libsmartcols1-2.40.4-slfo.1.1_1.1
[    7s] [64/293] cumulate libsqlite3-0-3.44.2-slfo.1.1_1.2
[    7s] [65/293] cumulate libtasn1-6-4.19.0-slfo.1.1_2.1
[    7s] [66/293] cumulate libunistring5-1.1-slfo.1.1_1.2
[    7s] [67/293] cumulate liburcu8-0.14.0-slfo.1.1_1.3
[    7s] [68/293] cumulate liburing2-2.6-slfo.1.1_1.3
[    7s] [69/293] cumulate libuuid1-2.40.4-slfo.1.1_1.1
[    7s] [70/293] cumulate libverto1-0.3.2-slfo.1.1_1.2
[    7s] [71/293] cumulate libxxhash0-0.8.1-slfo.1.1_2.1
[    7s] [72/293] cumulate libyaml-0-2-0.2.5-slfo.1.1_1.2
[    7s] [73/293] cumulate libz1-1.2.13-slfo.1.1_1.3
[    7s] [74/293] cumulate libzstd1-1.5.5-slfo.1.1_1.4
[    7s] [75/293] cumulate patch-2.7.6-slfo.1.1_1.4
[    7s] [76/293] cumulate update-alternatives-1.22.0-slfo.1.1_1.4
[    7s] [77/293] cumulate alts-1.2+30.a5431e9-slfo.1.1_1.3
[    7s] [78/293] cumulate attr-2.5.1-slfo.1.1_1.3
[    7s] [79/293] cumulate libassuan0-2.5.6-slfo.1.1_1.2
[    7s] [80/293] cumulate libblkid1-2.40.4-slfo.1.1_1.1
[    7s] [81/293] cumulate libbrotlidec1-1.1.0-slfo.1.1_1.3
[    7s] [82/293] cumulate libctf-nobfd0-2.43-slfo.1.1_1.7
[    7s] [83/293] cumulate libext2fs2-1.47.0-slfo.1.1_1.2
[    7s] [84/293] cumulate libgcrypt20-1.10.3-slfo.1.1_1.10
[    7s] [85/293] cumulate libgdbm_compat4-1.23-slfo.1.1_1.3
[    7s] [86/293] cumulate libglib-2_0-0-2.78.6-slfo.1.1_2.1
[    7s] [87/293] cumulate libidn2-0-2.3.4-slfo.1.1_1.2
[    7s] [88/293] cumulate libisl23-0.26-slfo.1.1_1.4
[    7s] [89/293] cumulate libksba8-1.6.4-slfo.1.1_1.2
[    7s] [90/293] cumulate libmpfr6-4.2.1-slfo.1.1_1.4
[    7s] [91/293] cumulate libp11-kit0-0.25.3-slfo.1.1_1.2
[    7s] [92/293] cumulate libpng16-16-1.6.43-slfo.1.1_1.2
[    7s] [93/293] cumulate libselinux1-3.5-slfo.1.1_1.3
[    7s] [94/293] cumulate libstdc++6-14.2.0+git10526-slfo.1.1_2.1
[    7s] [95/293] cumulate libudev-mini1-254.24-slfo.1.1_1.1
[    7s] [96/293] cumulate perl-base-5.38.2-slfo.1.1_1.4
[    7s] [97/293] cumulate pkgconf-1.8.0-slfo.1.1_1.5
[    7s] [98/293] cumulate chkstat-1600_20240206-slfo.1.1_1.5
[    7s] [99/293] cumulate efibootmgr-18-slfo.1.1_1.2
[    7s] [100/293] cumulate libelf1-0.189-slfo.1.1_1.5
[    7s] [101/293] cumulate libhogweed6-3.9.1-slfo.1.1_1.2
[    7s] [102/293] cumulate libjte2-1.22-slfo.1.1_1.2
[    7s] [103/293] cumulate libxml2-2-2.11.6-slfo.1.1_3.1
[    7s] [104/293] cumulate libopenssl3-3.1.4-slfo.1.1_4.1
[    7s] [105/293] cumulate virtiofsd-1.10.1-slfo.1.1_1.2
[    7s] [106/293] cumulate libzio1-1.08-slfo.1.1_1.3
[    7s] [107/293] cumulate libmagic1-5.44-slfo.1.1_1.4
[    7s] [108/293] cumulate squashfs-4.6.1-slfo.1.1_1.2
[    7s] [109/293] cumulate build-mkbaselibs-20240221-slfo.1.1_1.3
[    7s] [110/293] cumulate rpm-build-perl-4.18.0-slfo.1.1_1.5
[    7s] [111/293] cumulate dwz-0.15-slfo.1.1_1.5
[    7s] [112/293] cumulate findutils-4.9.0-slfo.1.1_2.1
[    7s] [113/293] cumulate libgmodule-2_0-0-2.78.6-slfo.1.1_2.1
[    7s] [114/293] cumulate libusb-1_0-0-1.0.27-slfo.1.1_1.2
[    7s] [115/293] cumulate libxslt1-1.1.38-slfo.1.1_2.1
[    7s] [116/293] cumulate file-5.44-slfo.1.1_1.4
[    7s] [117/293] cumulate libasan8-14.2.0+git10526-slfo.1.1_2.1
[    7s] [118/293] cumulate libaugeas0-1.14.1-slfo.1.1_1.3
[    7s] [119/293] cumulate libdb-4_8-4.8.30-slfo.1.1_1.4
[    7s] [120/293] cumulate libfdisk1-2.40.4-slfo.1.1_1.1
[    7s] [121/293] cumulate libgobject-2_0-0-2.78.6-slfo.1.1_2.1
[    7s] [122/293] cumulate libhwasan0-14.2.0+git10526-slfo.1.1_2.1
[    7s] [123/293] cumulate libinih0-56-slfo.1.1_1.3
[    7s] [124/293] cumulate libldap2-2.6.4-slfo.1.1_1.2
[    7s] [125/293] cumulate liblsan0-14.2.0+git10526-slfo.1.1_2.1
[    7s] [126/293] cumulate libmount1-2.40.4-slfo.1.1_1.1
[    7s] [127/293] cumulate libmpc3-1.3.1-slfo.1.1_1.4
[    7s] [128/293] cumulate libpsl5-0.21.2-slfo.1.1_1.2
[    7s] [129/293] cumulate libsigc-2_0-0-2.12.0-slfo.1.1_1.3
[    7s] [130/293] cumulate libsubid4-4.15.1-slfo.1.1_1.3
[    7s] [131/293] cumulate libtsan2-14.2.0+git10526-slfo.1.1_2.1
[    7s] [132/293] cumulate libubsan1-14.2.0+git10526-slfo.1.1_2.1
[    7s] [133/293] cumulate libyaml-cpp0_8-0.8.0-slfo.1.1_1.3
[    7s] [134/293] cumulate libzck1-1.3.2-slfo.1.1_1.2
[    7s] [135/293] cumulate p11-kit-0.25.3-slfo.1.1_1.2
[    7s] [136/293] cumulate p11-kit-tools-0.25.3-slfo.1.1_1.2
[    7s] [137/293] cumulate sed-4.9-slfo.1.1_1.2
[    7s] [138/293] cumulate tar-1.35-slfo.1.1_1.2
[    7s] [139/293] cumulate libboost_thread1_84_0-1.84.0-slfo.1.1_1.4
[    7s] [140/293] cumulate libisofs6-1.5.4-slfo.1.1_1.2
[    7s] [141/293] cumulate libfreetype6-2.13.3-slfo.1.1_1.1
[    7s] [142/293] cumulate libdw1-0.189-slfo.1.1_1.5
[    7s] [143/293] cumulate libsemanage2-3.5-slfo.1.1_1.3
[    7s] [144/293] cumulate libsystemd0-254.24-slfo.1.1_1.1
[    7s] [145/293] cumulate libgnutls30-3.8.3-slfo.1.1_2.1
[    7s] [146/293] cumulate libasm1-0.189-slfo.1.1_1.5
[    7s] [147/293] cumulate libexslt0-1.1.38-slfo.1.1_2.1
[    7s] [148/293] cumulate cpp13-13.3.0+git8781-slfo.1.1_1.8
[    7s] [149/293] cumulate perl-5.38.2-slfo.1.1_1.4
[    7s] [150/293] cumulate brp-check-suse-84.87+git20230324.8680ce4-slfo.1.1_1.3
[    7s] [151/293] cumulate terminfo-base-6.4.20240224-slfo.1.1_1.5
[    7s] [152/293] cumulate libncurses6-6.4.20240224-slfo.1.1_1.5
[    7s] [153/293] cumulate libedit0-20210910.3.1-slfo.1.1_1.3
[    7s] [154/293] cumulate libreadline8-8.2-slfo.1.1_1.4
[    7s] [155/293] cumulate ncurses-utils-6.4.20240224-slfo.1.1_1.5
[    7s] [156/293] cumulate gptfdisk-1.0.9-slfo.1.1_1.4
[    7s] [157/293] cumulate bash-5.2.15-slfo.1.1_1.6
[    7s] [158/293] cumulate libisoburn1-1.5.4-slfo.1.1_1.2
[    7s] [159/293] cumulate bash-sh-5.2.15-slfo.1.1_1.6
[    7s] [160/293] cumulate login_defs-4.15.1-slfo.1.1_1.3
[    7s] [161/293] cumulate sysuser-shadow-3.1-slfo.1.1_1.2
[    7s] [162/293] cumulate cpio-2.15-slfo.1.1_2.4
[    7s] [163/293] cumulate cpp-build-13-slfo.1.1_2.1
[    7s] [164/293] cumulate gzip-1.13-slfo.1.1_2.4
[    7s] [165/293] cumulate make-4.4.1-slfo.1.1_1.4
[    7s] [166/293] cumulate which-2.21-slfo.1.1_1.4
[    7s] [167/293] cumulate bzip2-1.0.8-slfo.1.1_1.4
[    7s] [168/293] cumulate gettext-runtime-mini-0.21.1-slfo.1.1_2.1
[    7s] [169/293] cumulate grep-3.11-slfo.1.1_1.2
[    7s] [170/293] cumulate pkgconf-pkg-config-1.8.0-slfo.1.1_1.5
[    7s] [171/293] cumulate cracklib-2.9.11-slfo.1.1_1.2
[    7s] [172/293] cumulate libdevmapper1_03-2.03.22_1.02.196-slfo.1.1_1.3
[    7s] [173/293] cumulate shared-mime-info-2.2-slfo.1.1_1.2
[    7s] [174/293] cumulate xz-5.4.3-slfo.1.1_1.4
[    7s] [175/293] cumulate gawk-5.3.0-slfo.1.1_1.8
[    7s] [176/293] cumulate lua54-5.4.6-slfo.1.1_1.3
[    7s] [177/293] cumulate elfutils-0.189-slfo.1.1_1.5
[    7s] [178/293] cumulate coreutils-9.4-slfo.1.1_1.4
[    7s] [179/293] cumulate gpg2-2.4.4-slfo.1.1_1.2
[    7s] [180/293] cumulate python311-base-3.11.8-slfo.1.1_3.12
[    7s] [181/293] cumulate compat-usrmerge-build-84.87-slfo.1.1_1.5
[    7s] [182/293] cumulate python311-cssselect-1.2.0-slfo.1.1_1.2
[    7s] [183/293] cumulate python311-docopt-0.6.2-slfo.1.1_1.2
[    7s] [184/293] cumulate python311-idna-3.7-slfo.1.1_1.2
[    7s] [185/293] cumulate python311-packaging-24.1-slfo.1.1_1.2
[    7s] [186/293] cumulate python311-pycparser-2.21-slfo.1.1_1.2
[    7s] [187/293] cumulate python311-xmltodict-0.13.0-slfo.1.1_1.2
[    7s] [188/293] cumulate systemd-rpm-macros-24-slfo.1.1_1.2
[    7s] [189/293] cumulate libdevmapper-event1_03-2.03.22_1.02.196-slfo.1.1_1.3
[    7s] [190/293] cumulate libmediacheck6-6.1-slfo.1.1_1.2
[    7s] [191/293] cumulate libpython3_11-1_0-3.11.8-slfo.1.1_3.12
[    7s] [192/293] cumulate libxcrypt-devel-4.4.36-slfo.1.1_1.4
[    7s] [193/293] cumulate linux-glibc-devel-6.4-slfo.1.1_1.2
[    7s] [194/293] cumulate python311-setuptools-70.0.0-slfo.1.1_1.2
[    7s] [195/293] cumulate python311-simplejson-3.19.1-slfo.1.1_1.2
[    7s] [196/293] cumulate system-group-hardware-20170617-slfo.1.1_1.2
[    7s] [197/293] cumulate system-group-kvm-20170617-slfo.1.1_1.2
[    7s] [198/293] cumulate glibc-locale-base-2.38-slfo.1.1_4.1
[    7s] [199/293] cumulate libcrack2-2.9.11-slfo.1.1_1.2
[    7s] [200/293] cumulate python311-PyYAML-6.0.1-slfo.1.1_1.2
[    7s] [201/293] cumulate python311-charset-normalizer-3.1.0-slfo.1.1_2.1
[    7s] [202/293] cumulate xkeyboard-config-2.40-slfo.1.1_1.2
[    7s] [203/293] cumulate info-7.0.3-slfo.1.1_1.3
[    7s] [204/293] cumulate libgpgme11-1.23.0-slfo.1.1_1.5
[    7s] [205/293] cumulate libparted2-3.5-slfo.1.1_1.2
[    7s] [206/293] cumulate python311-3.11.8-slfo.1.1_3.3
[    7s] [207/293] cumulate ca-certificates-2+git20240805.fd24d50-slfo.1.1_1.2
[    7s] [208/293] cumulate gettext-tools-mini-0.21.1-slfo.1.1_2.1
[    7s] [209/293] cumulate permissions-config-1600_20240206-slfo.1.1_1.5
[    7s] [210/293] cumulate polkit-default-privs-1550+20231129.269abcd-slfo.1.1_1.2
[    7s] [211/293] cumulate mdadm-4.2-slfo.1.1_1.3
[    7s] [212/293] cumulate e2fsprogs-1.47.0-slfo.1.1_1.2
[    7s] [213/293] cumulate libcryptsetup12-2.6.1-slfo.1.1_1.2
[    7s] [214/293] cumulate openssl-3-3.1.4-slfo.1.1_4.1
[    7s] [215/293] cumulate krb5-mini-1.21.3-slfo.1.1_2.1
[    7s] [216/293] cumulate libmpath0-0.10.0+103+suse.0fc97cd-slfo.1.1_1.3
[    7s] [217/293] cumulate thin-provisioning-tools-0.9.0-slfo.1.1_1.4
[    7s] [218/293] cumulate aaa_base-84.87+git20240906.742565b-slfo.1.1_1.2
[    7s] [219/293] cumulate xfsprogs-6.5.0-slfo.1.1_1.2
[    7s] [220/293] cumulate binutils-2.43-slfo.1.1_1.7
[    7s] [221/293] cumulate grub2-2.12-slfo.1.1_1.17
[    7s] [222/293] cumulate libgio-2_0-0-2.78.6-slfo.1.1_2.1
[    7s] [223/293] cumulate rsync-3.3.0-slfo.1.1_3.1
[    7s] [224/293] cumulate btrfsprogs-6.8.1-slfo.1.1_1.2
[    7s] [225/293] cumulate rpm-4.18.0-slfo.1.1_1.5
[    7s] [226/293] cumulate aaa_base-malloccheck-84.87+git20240906.742565b-slfo.1.1_1.2
[    7s] [227/293] cumulate gio-branding-upstream-2.78.6-slfo.1.1_2.1
[    7s] [228/293] cumulate openssl-3.1.4-slfo.1.1_1.2
[    7s] [229/293] cumulate ca-certificates-mozilla-2.74-slfo.1.1_1.1
[    7s] [230/293] cumulate libxkbcommon0-1.5.0-slfo.1.1_1.2
[    7s] [231/293] cumulate permissions-1600_20240206-slfo.1.1_1.5
[    7s] [232/293] cumulate checkmedia-6.1-slfo.1.1_1.2
[    7s] [233/293] cumulate glibc-devel-2.38-slfo.1.1_4.1
[    7s] [234/293] cumulate grub2-i386-pc-2.12-slfo.1.1_1.17
[    7s] [235/293] cumulate grub2-x86_64-efi-2.12-slfo.1.1_1.17
[    7s] [236/293] cumulate libctf0-2.43-slfo.1.1_1.7
[    7s] [237/293] cumulate libpwquality1-1.4.5-slfo.1.1_1.2
[    7s] [238/293] cumulate libtirpc3-1.3.4-slfo.1.1_1.2
[    7s] [239/293] cumulate mtools-4.0.43-slfo.1.1_1.2
[    7s] [240/293] cumulate rpm-config-SUSE-20240214-slfo.1.1_1.2
[    7s] [241/293] cumulate python311-cffi-1.15.1-slfo.1.1_1.3
[    7s] [242/293] cumulate xorriso-1.5.4-slfo.1.1_1.2
[    7s] [243/293] cumulate libssh4-0.10.6-slfo.1.1_1.3
[    7s] [244/293] cumulate python-rpm-packaging-20210526+a18ca48-slfo.1.1_1.2
[    7s] [245/293] cumulate rpmlint-mini-2.6.1+git20240807.9387990-slfo.1.1_1.3
[    7s] [246/293] cumulate device-mapper-2.03.22_1.02.196-slfo.1.1_1.3
[    7s] [247/293] cumulate libmodulemd2-2.14.0-slfo.1.1_1.3
[    7s] [248/293] cumulate python311-lxml-4.9.3-slfo.1.1_1.2
[    7s] [249/293] cumulate glib2-tools-2.78.6-slfo.1.1_2.1
[    7s] [250/293] cumulate liblvm2cmd2_03-2.03.22-slfo.1.1_1.3
[    7s] [251/293] cumulate libsolv-tools-base-0.7.30-slfo.1.1_1.3
[    7s] [252/293] cumulate parted-3.5-slfo.1.1_1.2
[    7s] [253/293] cumulate build-compare-20230617T171717.50241a8-slfo.1.1_1.5
[    7s] [254/293] cumulate librpmbuild9-4.18.0-slfo.1.1_1.5
[    7s] [255/293] cumulate suse-module-tools-16.0.43-slfo.1.1_1.2
[    7s] [256/293] cumulate debugedit-5.0-slfo.1.1_1.4
[    7s] [257/293] cumulate python311-pyOpenSSL-24.0.0-slfo.1.1_1.2
[    7s] [258/293] cumulate lsof-4.99.4-slfo.1.1_1.1
[    7s] [259/293] cumulate python311-certifi-2024.7.4-slfo.1.1_1.5
[    7s] [260/293] cumulate kpartx-0.10.0+103+suse.0fc97cd-slfo.1.1_1.3
[    7s] [261/293] cumulate libutempter0-1.2.1-slfo.1.1_1.3
[    7s] [262/293] cumulate python311-cryptography-42.0.4-slfo.1.1_1.2
[    7s] [263/293] cumulate cryptsetup-2.6.1-slfo.1.1_1.2
[    7s] [264/293] cumulate pam-1.6.1-slfo.1.1_2.1
[    7s] [265/293] cumulate post-build-checks-84.87+git20240327.7996a0f-slfo.1.1_1.2
[    7s] [266/293] cumulate kmod-32-slfo.1.1_1.2
[    7s] [267/293] cumulate libcurl4-8.12.1-slfo.1.1_1.1
[    7s] [268/293] cumulate gcc13-13.3.0+git8781-slfo.1.1_1.8
[    7s] [269/293] cumulate gcc13-PIE-13.3.0+git8781-slfo.1.1_1.8
[    7s] [270/293] cumulate gcc-build-13-slfo.1.1_2.1
[    7s] [271/293] cumulate python311-urllib3-2.1.0-slfo.1.1_1.2
[    7s] [272/293] cumulate screen-4.9.1-slfo.1.1_1.3
[    7s] [273/293] cumulate qemu-pr-helper-8.2.9-slfo.1.1_1.1
[    7s] [274/293] cumulate systemd-mini-254.24-slfo.1.1_1.1
[    7s] [275/293] cumulate qemu-img-8.2.9-slfo.1.1_1.1
[    7s] [276/293] cumulate shadow-4.15.1-slfo.1.1_1.3
[    7s] [277/293] cumulate libcreaterepo_c1-1.0.2-slfo.1.1_1.2
[    7s] [278/293] cumulate util-linux-2.40.4-slfo.1.1_1.1
[    7s] [279/293] cumulate libzypp-17.35.16-slfo.1.1_1.1
[    7s] [280/293] cumulate gcc-build-PIE-13-slfo.1.1_2.1
[    7s] [281/293] cumulate createrepo_c-1.0.2-slfo.1.1_1.2
[    7s] [282/293] cumulate python311-requests-2.32.3-slfo.1.1_2.1
[    7s] [283/293] cumulate zypper-1.14.77-slfo.1.1_1.7
[    7s] [284/293] cumulate util-linux-systemd-2.40.4-slfo.1.1_1.1
[    7s] [285/293] cumulate qemu-tools-8.2.9-slfo.1.1_1.1
[    7s] [286/293] cumulate lvm2-2.03.22-slfo.1.1_1.3
[    7s] [287/293] cumulate rpm-build-4.18.0-slfo.1.1_1.5
[    7s] [288/293] cumulate kiwi-systemdeps-core-10.2.12-slfo.1.1_1.1
[    7s] [289/293] cumulate kiwi-systemdeps-bootloaders-10.2.12-slfo.1.1_1.1
[    7s] [290/293] cumulate kiwi-systemdeps-filesystems-10.2.12-slfo.1.1_1.1
[    7s] [291/293] cumulate python3-kiwi-10.2.12-slfo.1.1_1.1
[    7s] [292/293] cumulate kiwi-systemdeps-iso-media-10.2.12-slfo.1.1_1.1
[    7s] [293/293] cumulate kiwi-systemdeps-disk-images-10.2.12-slfo.1.1_1.1
[    7s] now installing cumulated packages
[    7s] Preparing...                          ########################################
[    7s] Updating / installing...
[    7s] system-user-root-20190513-slfo.1.1_1.2########################################
[    7s] crypto-policies-20230920.570ea89-slfo.########################################
[    7s] cracklib-dict-small-2.9.11-slfo.1.1_1.########################################
[    7s] python-rpm-macros-20240618.1e386da-slf########################################
[    7s] pkgconf-m4-1.8.0-slfo.1.1_1.5         ########################################
[    7s] libtirpc-netconfig-1.3.4-slfo.1.1_1.2 ########################################
[    7s] libssh-config-0.10.6-slfo.1.1_1.3     ########################################
[    7s] libsemanage-conf-3.5-slfo.1.1_1.3     ########################################
[    7s] file-magic-5.44-slfo.1.1_1.4          ########################################
[    7s] compat-usrmerge-tools-84.87-slfo.1.1_1########################################
[    7s] filesystem-84.87-slfo.1.1_1.2         ########################################
[    7s] glibc-2.38-slfo.1.1_4.1               ########################################
[    7s] libz1-1.2.13-slfo.1.1_1.3             ########################################
[    7s] libgcc_s1-14.2.0+git10526-slfo.1.1_2.1########################################
[    7s] libzstd1-1.5.5-slfo.1.1_1.4           ########################################
[    7s] libstdc++6-14.2.0+git10526-slfo.1.1_2.########################################
[    7s] libbz2-1-1.0.8-slfo.1.1_1.4           ########################################
[    7s] liblzma5-5.4.3-slfo.1.1_1.4           ########################################
[    7s] terminfo-base-6.4.20240224-slfo.1.1_1.########################################
[    7s] libncurses6-6.4.20240224-slfo.1.1_1.5 ########################################
[    7s] ncurses-utils-6.4.20240224-slfo.1.1_1.########################################
[    7s] libuuid1-2.40.4-slfo.1.1_1.1          ########################################
[    7s] libreadline8-8.2-slfo.1.1_1.4         ########################################
[    7s] bash-5.2.15-slfo.1.1_1.6              ########################################
[    7s] bash-sh-5.2.15-slfo.1.1_1.6           ########################################
[    7s] libxml2-2-2.11.6-slfo.1.1_3.1         ########################################
[    7s] libcrypt1-4.4.36-slfo.1.1_1.4         ########################################
[    7s] perl-base-5.38.2-slfo.1.1_1.4         ########################################
[    7s] libgmp10-6.3.0-slfo.1.1_1.5           ########################################
[    7s] libelf1-0.189-slfo.1.1_1.5            ########################################
[    7s] libacl1-2.3.1-slfo.1.1_1.3            ########################################
[    7s] libcap2-2.69-slfo.1.1_1.3             ########################################
[    7s] libudev-mini1-254.24-slfo.1.1_1.1     ########################################
[    7s] libpopt0-1.19-slfo.1.1_1.3            ########################################
[    7s] fillup-1.42-slfo.1.1_2.2              ########################################
[    8s] xz-5.4.3-slfo.1.1_1.4                 ########################################
[    8s] libaio1-0.3.113-slfo.1.1_1.2          ########################################
[    8s] libaudit1-3.1.1-slfo.1.1_1.3          ########################################
[    8s] libeconf0-0.7.2-slfo.1.1_1.3          ########################################
[    8s] libblkid1-2.40.4-slfo.1.1_1.1         ########################################
[    8s] libgpg-error0-1.47-slfo.1.1_1.3       ########################################
[    8s] libgcrypt20-1.10.3-slfo.1.1_1.10      ########################################
[    8s] libdw1-0.189-slfo.1.1_1.5             ########################################
[    8s] libmpfr6-4.2.1-slfo.1.1_1.4           ########################################
[    8s] gawk-5.3.0-slfo.1.1_1.8               ########################################
[    8s] cpio-2.15-slfo.1.1_2.4                ########################################
[    8s] libcap-ng0-0.8.3-slfo.1.1_1.4         ########################################
[    8s] libcom_err2-1.47.0-slfo.1.1_1.2       ########################################
[    8s] libffi8-3.4.6-slfo.1.1_1.4            ########################################
[    8s] libtasn1-6-4.19.0-slfo.1.1_2.1        ########################################
[    8s] update-alternatives-1.22.0-slfo.1.1_1.########################################
[    8s] libp11-kit0-0.25.3-slfo.1.1_1.2       ########################################
[    8s] login_defs-4.15.1-slfo.1.1_1.3        ########################################
[    8s] gzip-1.13-slfo.1.1_2.4                ########################################
[    8s] libmagic1-5.44-slfo.1.1_1.4           ########################################
[    8s] libattr1-2.5.1-slfo.1.1_1.3           ########################################
[    8s] libgomp1-14.2.0+git10526-slfo.1.1_2.1 ########################################
[    8s] liblua5_4-5-5.4.6-slfo.1.1_1.3        ########################################
[    8s] liblz4-1-1.9.4-slfo.1.1_1.2           ########################################
[    8s] libsystemd0-254.24-slfo.1.1_1.1       ########################################
[    8s] libnuma1-********.g3871b1c-slfo.1.1_1.########################################
[    8s] libpcre2-8-0-10.42-slfo.1.1_1.4       ########################################
[    8s] libselinux1-3.5-slfo.1.1_1.3          ########################################
[    8s] coreutils-9.4-slfo.1.1_1.4            ########################################
[    8s] libglib-2_0-0-2.78.6-slfo.1.1_2.1     ########################################
[    8s] rpm-config-SUSE-20240214-slfo.1.1_1.2 ########################################
[    8s] rpm-4.18.0-slfo.1.1_1.5               ########################################
[    8s] Updating /etc/sysconfig/services ...
[    8s] sed-4.9-slfo.1.1_1.2                  ########################################
[    8s] libdevmapper1_03-2.03.22_1.02.196-slfo########################################
[    8s] grep-3.11-slfo.1.1_1.2                ########################################
[    8s] libgmodule-2_0-0-2.78.6-slfo.1.1_2.1  ########################################
[    8s] libgobject-2_0-0-2.78.6-slfo.1.1_2.1  ########################################
[    8s] libmount1-2.40.4-slfo.1.1_1.1         ########################################
[    8s] findutils-4.9.0-slfo.1.1_2.1          ########################################
[    8s] libdevmapper-event1_03-2.03.22_1.02.19########################################
[    8s] libsqlite3-0-3.44.2-slfo.1.1_1.2      ########################################
[    8s] libunistring5-1.1-slfo.1.1_1.2        ########################################
[    8s] libidn2-0-2.3.4-slfo.1.1_1.2          ########################################
[    8s] aaa_base-84.87+git20240906.742565b-slf########################################
[    8s] Updating /etc/sysconfig/language ...
[    8s] Updating /etc/sysconfig/proxy ...
[    8s] systemd-rpm-macros-24-slfo.1.1_1.2    ########################################
[    8s] glibc-locale-base-2.38-slfo.1.1_4.1   ########################################
[    8s] mtools-4.0.43-slfo.1.1_1.2            ########################################
[    8s] tar-1.35-slfo.1.1_1.2                 ########################################
[    8s] libext2fs2-1.47.0-slfo.1.1_1.2        ########################################
[    8s] libmpc3-1.3.1-slfo.1.1_1.4            ########################################
[    8s] libassuan0-2.5.6-slfo.1.1_1.2         ########################################
[    8s] chkstat-1600_20240206-slfo.1.1_1.5    ########################################
[    8s] dwz-0.15-slfo.1.1_1.5                 ########################################
[    8s] libisl23-0.26-slfo.1.1_1.4            ########################################
[    8s] cpp13-13.3.0+git8781-slfo.1.1_1.8     ########################################
[    8s] libxslt1-1.1.38-slfo.1.1_2.1          ########################################
[    8s] gettext-runtime-mini-0.21.1-slfo.1.1_2########################################
[    8s] libjte2-1.22-slfo.1.1_1.2             ########################################
[    8s] diffutils-3.10-slfo.1.1_1.3           ########################################
[    8s] libexpat1-2.7.1-slfo.1.1_1.1          ########################################
[    8s] libgdbm6-1.23-slfo.1.1_1.3            ########################################
[    8s] libjitterentropy3-3.4.1-slfo.1.1_1.3  ########################################
[    8s] liblzo2-2-2.10-slfo.1.1_1.3           ########################################
[    8s] libnettle8-3.9.1-slfo.1.1_1.2         ########################################
[    8s] libseccomp2-2.5.4-slfo.1.1_1.4        ########################################
[    8s] file-5.44-slfo.1.1_1.4                ########################################
[    8s] libsmartcols1-2.40.4-slfo.1.1_1.1     ########################################
[    8s] liburcu8-0.14.0-slfo.1.1_1.3          ########################################
[    8s] liburing2-2.6-slfo.1.1_1.3            ########################################
[    8s] libyaml-0-2-0.2.5-slfo.1.1_1.2        ########################################
[    8s] libmodulemd2-2.14.0-slfo.1.1_1.3      ########################################
[    8s] libmpath0-0.10.0+103+suse.0fc97cd-slfo########################################
[    8s] virtiofsd-1.10.1-slfo.1.1_1.2         ########################################
[    8s] libhogweed6-3.9.1-slfo.1.1_1.2        ########################################
[    8s] libgnutls30-3.8.3-slfo.1.1_2.1        ########################################
[    8s] squashfs-4.6.1-slfo.1.1_1.2           ########################################
[    8s] libgdbm_compat4-1.23-slfo.1.1_1.3     ########################################
[    8s] thin-provisioning-tools-0.9.0-slfo.1.1########################################
[    8s] device-mapper-2.03.22_1.02.196-slfo.1.########################################
[    8s] kpartx-0.10.0+103+suse.0fc97cd-slfo.1.########################################
[    8s] libisofs6-1.5.4-slfo.1.1_1.2          ########################################
[    8s] gettext-tools-mini-0.21.1-slfo.1.1_2.1########################################
[    8s] libexslt0-1.1.38-slfo.1.1_2.1         ########################################
[    8s] cpp-build-13-slfo.1.1_2.1             ########################################
[    8s] permissions-config-1600_20240206-slfo.########################################
[    8s] Updating /etc/sysconfig/security ...
[    8s] Checking permissions and ownerships - using the permissions files
[    8s] 	/usr/share/permissions/permissions
[    8s] 	/usr/share/permissions/permissions.easy
[    8s] 	/etc/permissions.local
[    8s] /usr/sbin/unix2_chkpwd: setting to root:shadow 4755 (wrong owner/group root:root)
[    8s] /usr/sbin/unix_chkpwd: setting to root:shadow 4755 (wrong owner/group root:root)
[    8s] permissions-1600_20240206-slfo.1.1_1.5########################################
[    8s] pam-1.6.1-slfo.1.1_2.1                ########################################
[    9s] qemu-pr-helper-8.2.9-slfo.1.1_1.1     ########################################
[    9s] qemu-img-8.2.9-slfo.1.1_1.1           ########################################
[    9s] e2fsprogs-1.47.0-slfo.1.1_1.2         ########################################
[    9s] aaa_base-malloccheck-84.87+git20240906########################################
[    9s] libpsl5-0.21.2-slfo.1.1_1.2           ########################################
[    9s] liblvm2cmd2_03-2.03.22-slfo.1.1_1.3   ########################################
[    9s] libparted2-3.5-slfo.1.1_1.2           ########################################
[    9s] libsolv-tools-base-0.7.30-slfo.1.1_1.3########################################
[    9s] librpmbuild9-4.18.0-slfo.1.1_1.5      ########################################
[    9s] shared-mime-info-2.2-slfo.1.1_1.2     ########################################
[    9s] gio-branding-upstream-2.78.6-slfo.1.1_########################################
[    9s] libgio-2_0-0-2.78.6-slfo.1.1_2.1      ########################################
[    9s] glib2-tools-2.78.6-slfo.1.1_2.1       ########################################
[    9s] No schema files found: doing nothing.
[    9s] linux-glibc-devel-6.4-slfo.1.1_1.2    ########################################
[    9s] polkit-default-privs-1550+20231129.269########################################
[    9s] Updating /etc/sysconfig/security ...
[    9s] can't open /etc/polkit-1/rules.d/90-default-privs.rules.new: No such file or directory
[    9s] warning: %post(polkit-default-privs-1550+20231129.269abcd-slfo.1.1_1.2.noarch) scriptlet failed, exit status 2
[    9s] mdadm-4.2-slfo.1.1_1.3                ########################################
[    9s] Updating /etc/sysconfig/mdadm ...
[    9s] libsubid4-4.15.1-slfo.1.1_1.3         ########################################
[    9s] lua54-5.4.6-slfo.1.1_1.3              ########################################
[    9s] update-alternatives: using /usr/bin/lua5.4 to provide /usr/bin/lua (lua) in auto mode
[    9s] p11-kit-0.25.3-slfo.1.1_1.2           ########################################
[    9s] p11-kit-tools-0.25.3-slfo.1.1_1.2     ########################################
[    9s] ca-certificates-2+git20240805.fd24d50-########################################
[    9s] ca-certificates-mozilla-2.74-slfo.1.1_########################################
[   10s] libopenssl3-3.1.4-slfo.1.1_4.1        ########################################
[   10s] libzck1-1.3.2-slfo.1.1_1.2            ########################################
[   10s] openssl-3.1.4-slfo.1.1_1.2            ########################################
[   10s] openssl-3-3.1.4-slfo.1.1_4.1          ########################################
[   10s] libasm1-0.189-slfo.1.1_1.5            ########################################
[   10s] elfutils-0.189-slfo.1.1_1.5           ########################################
[   10s] libksba8-1.6.4-slfo.1.1_1.2           ########################################
[   10s] libfdisk1-2.40.4-slfo.1.1_1.1         ########################################
[   10s] gptfdisk-1.0.9-slfo.1.1_1.4           ########################################
[   10s] libusb-1_0-0-1.0.27-slfo.1.1_1.2      ########################################
[   10s] rpm-build-perl-4.18.0-slfo.1.1_1.5    ########################################
[   10s] make-4.4.1-slfo.1.1_1.4               ########################################
[   10s] which-2.21-slfo.1.1_1.4               ########################################
[   10s] bzip2-1.0.8-slfo.1.1_1.4              ########################################
[   10s] cracklib-2.9.11-slfo.1.1_1.2          ########################################
[   10s] libcrack2-2.9.11-slfo.1.1_1.2         ########################################
[   10s] libpwquality1-1.4.5-slfo.1.1_1.2      ########################################
[   10s] libedit0-20210910.3.1-slfo.1.1_1.3    ########################################
[   10s] libzio1-1.08-slfo.1.1_1.3             ########################################
[   10s] info-7.0.3-slfo.1.1_1.3               ########################################
[   10s] libasan8-14.2.0+git10526-slfo.1.1_2.1 ########################################
[   10s] libdb-4_8-4.8.30-slfo.1.1_1.4         ########################################
[   10s] perl-5.38.2-slfo.1.1_1.4              ########################################
[   10s] libhwasan0-14.2.0+git10526-slfo.1.1_2.########################################
[   10s] libinih0-56-slfo.1.1_1.3              ########################################
[   10s] xfsprogs-6.5.0-slfo.1.1_1.2           ########################################
[   10s] liblsan0-14.2.0+git10526-slfo.1.1_2.1 ########################################
[   10s] libsigc-2_0-0-2.12.0-slfo.1.1_1.3     ########################################
[   10s] libtsan2-14.2.0+git10526-slfo.1.1_2.1 ########################################
[   10s] libubsan1-14.2.0+git10526-slfo.1.1_2.1########################################
[   10s] libyaml-cpp0_8-0.8.0-slfo.1.1_1.3     ########################################
[   10s] libctf-nobfd0-2.43-slfo.1.1_1.7       ########################################
[   11s] binutils-2.43-slfo.1.1_1.7            ########################################
[   11s] update-alternatives: using /usr/bin/ld.bfd to provide /usr/bin/ld (ld) in auto mode
[   11s] libctf0-2.43-slfo.1.1_1.7             ########################################
[   11s] debugedit-5.0-slfo.1.1_1.4            ########################################
[   11s] libpng16-16-1.6.43-slfo.1.1_1.2       ########################################
[   11s] dosfstools-4.2-slfo.1.1_1.2           ########################################
[   11s] libalternatives1-1.2+30.a5431e9-slfo.1########################################
[   11s] alts-1.2+30.a5431e9-slfo.1.1_1.3      ########################################
[   11s] libargon2-1-20190702-slfo.1.1_1.2     ########################################
[   11s] libatomic1-14.2.0+git10526-slfo.1.1_2.########################################
[   11s] libbrotlicommon1-1.1.0-slfo.1.1_1.3   ########################################
[   11s] libbrotlidec1-1.1.0-slfo.1.1_1.3      ########################################
[   11s] libfreetype6-2.13.3-slfo.1.1_1.1      ########################################
[   11s] libburn4-1.5.4-slfo.1.1_1.2           ########################################
[   11s] libisoburn1-1.5.4-slfo.1.1_1.2        ########################################
[   11s] xorriso-1.5.4-slfo.1.1_1.2            ########################################
[   11s] libefivar1-38-slfo.1.1_1.2            ########################################
[   11s] efibootmgr-18-slfo.1.1_1.2            ########################################
[   11s] libfa1-1.14.1-slfo.1.1_1.3            ########################################
[   11s] libaugeas0-1.14.1-slfo.1.1_1.3        ########################################
[   11s] libfuse2-2.9.9-slfo.1.1_1.2           ########################################
[   11s] grub2-2.12-slfo.1.1_1.17              ########################################
[   11s] grub2-i386-pc-2.12-slfo.1.1_1.17      ########################################
[   11s] grub2-x86_64-efi-2.12-slfo.1.1_1.17   ########################################
[   11s] libitm1-14.2.0+git10526-slfo.1.1_2.1  ########################################
[   11s] libjson-c5-0.16-slfo.1.1_1.2          ########################################
[   11s] libcryptsetup12-2.6.1-slfo.1.1_1.2    ########################################
[   11s] cryptsetup-2.6.1-slfo.1.1_1.2         ########################################
[   11s] libmpdec3-2.5.1-slfo.1.1_1.4          ########################################
[   11s] libpython3_11-1_0-3.11.8-slfo.1.1_3.12########################################
[   11s] python311-base-3.11.8-slfo.1.1_3.12   ########################################
[   12s] python311-3.11.8-slfo.1.1_3.3         ########################################
[   12s] python311-idna-3.7-slfo.1.1_1.2       ########################################
[   12s] python311-certifi-2024.7.4-slfo.1.1_1.########################################
[   12s] python311-cssselect-1.2.0-slfo.1.1_1.2########################################
[   12s] python311-lxml-4.9.3-slfo.1.1_1.2     ########################################
[   12s] python311-docopt-0.6.2-slfo.1.1_1.2   ########################################
[   12s] python311-packaging-24.1-slfo.1.1_1.2 ########################################
[   12s] python-rpm-packaging-20210526+a18ca48-########################################
[   12s] python311-pycparser-2.21-slfo.1.1_1.2 ########################################
[   12s] python311-cffi-1.15.1-slfo.1.1_1.3    ########################################
[   12s] python311-cryptography-42.0.4-slfo.1.1########################################
[   12s] python311-pyOpenSSL-24.0.0-slfo.1.1_1.########################################
[   12s] python311-urllib3-2.1.0-slfo.1.1_1.2  ########################################
[   12s] python311-xmltodict-0.13.0-slfo.1.1_1.########################################
[   12s] python311-setuptools-70.0.0-slfo.1.1_1########################################
[   12s] python311-simplejson-3.19.1-slfo.1.1_1########################################
[   12s] python311-PyYAML-6.0.1-slfo.1.1_1.2   ########################################
[   12s] python311-charset-normalizer-3.1.0-slf########################################
[   12s] python311-requests-2.32.3-slfo.1.1_2.1########################################
[   12s] libnghttp2-14-1.52.0-slfo.1.1_1.4     ########################################
[   12s] libnpth0-1.6-slfo.1.1_1.2             ########################################
[   12s] gpg2-2.4.4-slfo.1.1_1.2               ########################################
[   12s] libmediacheck6-6.1-slfo.1.1_1.2       ########################################
[   12s] checkmedia-6.1-slfo.1.1_1.2           ########################################
[   12s] libgpgme11-1.23.0-slfo.1.1_1.5        ########################################
[   12s] libparted-fs-resize0-3.5-slfo.1.1_1.2 ########################################
[   12s] parted-3.5-slfo.1.1_1.2               ########################################
[   12s] libpkgconf3-1.8.0-slfo.1.1_1.5        ########################################
[   12s] pkgconf-1.8.0-slfo.1.1_1.5            ########################################
[   12s] pkgconf-pkg-config-1.8.0-slfo.1.1_1.5 ########################################
[   12s] systemd-mini-254.24-slfo.1.1_1.1      warning: group systemd-journal does not exist - using root
[   12s] ########################################
[   12s] Running in chroot, ignoring command 'daemon-reexec'
[   12s] util-linux-systemd-2.40.4-slfo.1.1_1.1########################################
[   12s] util-linux-2.40.4-slfo.1.1_1.1        ########################################
[   12s] suse-module-tools-16.0.43-slfo.1.1_1.2########################################
[   12s] kmod-32-slfo.1.1_1.2                  ########################################
[   12s] lvm2-2.03.22-slfo.1.1_1.3             ########################################
[   12s] libxcrypt-devel-4.4.36-slfo.1.1_1.4   ########################################
[   12s] glibc-devel-2.38-slfo.1.1_4.1         ########################################
[   13s] gcc13-13.3.0+git8781-slfo.1.1_1.8     ########################################
[   13s] gcc13-PIE-13.3.0+git8781-slfo.1.1_1.8 ########################################
[   13s] gcc-build-13-slfo.1.1_2.1             ########################################
[   13s] xkeyboard-config-2.40-slfo.1.1_1.2    ########################################
[   13s] libxkbcommon0-1.5.0-slfo.1.1_1.2      ########################################
[   13s] libsasl2-3-2.1.28-slfo.1.1_1.2        ########################################
[   13s] libldap2-2.6.4-slfo.1.1_1.2           ########################################
[   13s] libsepol2-3.5-slfo.1.1_1.3            ########################################
[   13s] libsemanage2-3.5-slfo.1.1_1.3         ########################################
[   13s] shadow-4.15.1-slfo.1.1_1.3            ########################################
[   13s] /usr/bin/newgidmap: setting to root:root 0755 "cap_setgid=ep". (wrong owner/group root:shadow, wrong permissions 4755, missing capabilities)
[   13s] /usr/bin/newuidmap: setting to root:root 0755 "cap_setuid=ep". (wrong owner/group root:shadow, wrong permissions 4755, missing capabilities)
[   13s] sysuser-shadow-3.1-slfo.1.1_1.2       ########################################
[   13s] /usr/bin/systemd-sysusers --replace=/usr/lib/sysusers.d/system-group-hardware.conf -
[   13s] Creating group 'kmem' with GID 499.
[   13s] Creating group 'lock' with GID 498.
[   13s] Creating group 'tty' with GID 5.
[   13s] Creating group 'utmp' with GID 497.
[   13s] Creating group 'audio' with GID 496.
[   13s] Creating group 'cdrom' with GID 495.
[   13s] Creating group 'dialout' with GID 494.
[   13s] Creating group 'disk' with GID 493.
[   13s] Creating group 'input' with GID 492.
[   13s] Creating group 'lp' with GID 491.
[   13s] Creating group 'render' with GID 490.
[   13s] Creating group 'sgx' with GID 489.
[   13s] Creating group 'tape' with GID 488.
[   13s] Creating group 'video' with GID 487.
[   13s] Creating group 'systemd-journal' with GID 486.
[   13s] system-group-hardware-20170617-slfo.1.########################################
[   13s] libutempter0-1.2.1-slfo.1.1_1.3       ########################################
[   13s] screen-4.9.1-slfo.1.1_1.3             ########################################
[   13s] /usr/bin/systemd-sysusers --replace=/usr/lib/sysusers.d/system-group-kvm.conf -
[   13s] Creating group 'kvm' with GID 36.
[   13s] system-group-kvm-20170617-slfo.1.1_1.2########################################
[   13s] qemu-tools-8.2.9-slfo.1.1_1.1         ########################################
[   13s] libverto1-0.3.2-slfo.1.1_1.2          ########################################
[   13s] krb5-mini-1.21.3-slfo.1.1_2.1         ########################################
[   13s] Updating /etc/sysconfig/kadmind ...
[   13s] Updating /etc/sysconfig/krb5kdc ...
[   13s] libtirpc3-1.3.4-slfo.1.1_1.2          ########################################
[   13s] lsof-4.99.4-slfo.1.1_1.1              ########################################
[   13s] libssh4-0.10.6-slfo.1.1_1.3           ########################################
[   13s] libcurl4-8.12.1-slfo.1.1_1.1          ########################################
[   13s] libcreaterepo_c1-1.0.2-slfo.1.1_1.2   ########################################
[   13s] libxxhash0-0.8.1-slfo.1.1_2.1         ########################################
[   13s] rsync-3.3.0-slfo.1.1_3.1              ########################################
[   13s] patch-2.7.6-slfo.1.1_1.4              ########################################
[   13s] btrfsprogs-udev-rules-6.8.1-slfo.1.1_1########################################
[   13s] btrfsprogs-6.8.1-slfo.1.1_1.2         ########################################
[   13s] boost-license1_84_0-1.84.0-slfo.1.1_1.########################################
[   13s] libboost_thread1_84_0-1.84.0-slfo.1.1_########################################
[   13s] libzypp-17.35.16-slfo.1.1_1.1         ########################################
[   13s] zypper-1.14.77-slfo.1.1_1.7           ########################################
[   13s] kiwi-systemdeps-core-10.2.12-slfo.1.1_########################################
[   13s] kiwi-systemdeps-bootloaders-10.2.12-sl########################################
[   13s] kiwi-systemdeps-filesystems-10.2.12-sl########################################
[   13s] kiwi-systemdeps-iso-media-10.2.12-slfo########################################
[   13s] kiwi-systemdeps-disk-images-10.2.12-sl########################################
[   13s] python3-kiwi-10.2.12-slfo.1.1_1.1     ########################################
[   13s] rpm-build-4.18.0-slfo.1.1_1.5         ########################################
[   13s] createrepo_c-1.0.2-slfo.1.1_1.2       ########################################
[   13s] gcc-build-PIE-13-slfo.1.1_2.1         ########################################
[   13s] brp-check-suse-84.87+git20230324.8680c########################################
[   13s] compat-usrmerge-build-84.87-slfo.1.1_1########################################
[   13s] rpmlint-mini-2.6.1+git20240807.9387990########################################
[   13s] post-build-checks-84.87+git20240327.79########################################
[   13s] build-compare-20230617T171717.50241a8-########################################
[   13s] attr-2.5.1-slfo.1.1_1.3               ########################################
[   13s] build-mkbaselibs-20240221-slfo.1.1_1.3########################################
[   13s] kernel-obs-build-6.4.0-29.1           ########################################
[   14s] now finalizing build dir...
[   14s] ... running 01-add_abuild_user_to_trusted_group
[   14s] ... running 02-set_timezone_to_utc
[   14s] ... running 03-set-permissions-secure
[   14s] ... running 11-hack_uname_version_to_kernel_version
[   15s] Running build time source services...
[   15s] -----------------------------------------------------------------
[   15s] ----- building PersistenceOS.kiwi
[   15s] -----------------------------------------------------------------
[   15s] -----------------------------------------------------------------
[   15s] creating repodata for SUSE:SLFO:1.1:Build/standard
[   16s] Directory walk started
[   16s] Directory walk done - 498 packages
[   16s] Temporary output repo path: /usr/src/packages/SOURCES/repos/SUSE:/SLFO:/1.1:/Build/standard/.repodata/
[   16s] Pool started (with 5 workers)
[   16s] Pool finished
[   16s] creating repodata for SUSE:SLFO:Kernel:1.0:Build/standard
[   16s] Directory walk started
[   16s] Directory walk done - 1 packages
[   16s] Temporary output repo path: /usr/src/packages/SOURCES/repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard/.repodata/
[   16s] Pool started (with 5 workers)
[   16s] Pool finished
[   16s] creating repodata for SUSE:SLFO:Products:SL-Micro:6.1/standard
[   16s] Directory walk started
[   16s] Directory walk done - 1 packages
[   16s] Temporary output repo path: /usr/src/packages/SOURCES/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard/.repodata/
[   16s] Pool started (with 5 workers)
[   16s] Pool finished
[   16s] creating repodata for home:Ivoslav:branches:SUSE:Templates:Images:SL-Micro-6.1/images
[   16s] Directory walk started
[   16s] Directory walk done - 0 packages
[   16s] Temporary output repo path: /usr/src/packages/SOURCES/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images/.repodata/
[   16s] Pool started (with 5 workers)
[   16s] Pool finished
[   16s] running kiwi system build for oem...
[   16s] cd /usr/src/packages/SOURCES && rm -rf /usr/src/packages/KIWI-oem && LANG=en_US.UTF-8 /usr/bin/kiwi --debug  system build --allow-existing-root --description /usr/src/packages/SOURCES --target-dir /usr/src/packages/KIWI-oem  --ignore-repos-used-for-build --add-repo dir:///./repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images,rpm-md,,1 --add-repo dir:///./repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard,rpm-md,,2 --add-repo dir:///./repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard,rpm-md,,3 --add-repo dir:///./repos/SUSE:/SLFO:/1.1:/Build/standard,rpm-md,,4
[   16s] WARNING could not set SOURCE_DATE_EPOCH, ensure mtime is in $TOPDIR/SOURCES/_scmsync.obsinfo or BUILD_CHANGELOG_TIMESTAMP is set in /.buildenv
[   16s] WARNING could not set SOURCE_DATE_EPOCH, ensure BUILD_RELEASE is set in /.buildenv
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build
[   17s] [ INFO    ]: 00:18:04 | Loading XML description
[   17s] [ INFO    ]: 00:18:04 | Support for XML markup available
[   17s] [ INFO    ]: 00:18:04 | --> loaded /usr/src/packages/SOURCES/config.xml
[   17s] [ INFO    ]: 00:18:04 | --> Selected build type: oem
[   17s] [ INFO    ]: 00:18:04 | Preparing new root system
[   17s] [ INFO    ]: 00:18:04 | Setup root directory: /usr/src/packages/KIWI-oem/build/image-root
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root
[   17s] [ DEBUG   ]: 00:18:04 | Check for extended attributes on /usr/src/packages/KIWI-oem/build/image-root said: [Errno 61] No data available: '/usr/src/packages/KIWI-oem/build/image-root'
[   17s] [ DEBUG   ]: 00:18:04 | Looking for rsync in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [rsync -a --ignore-existing /var/tmp/kiwi_root.sawbjq07/ /usr/src/packages/KIWI-oem/build/image-root]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for cp in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [cp /etc/hosts /usr/src/packages/KIWI-oem/build/image-root/etc/hosts.kiwi]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for ln in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [ln -s -f hosts.kiwi /usr/src/packages/KIWI-oem/build/image-root/etc/hosts]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for cp in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [cp /etc/sysconfig/proxy /usr/src/packages/KIWI-oem/build/image-root/etc/sysconfig/proxy.kiwi]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for ln in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [ln -s -f proxy.kiwi /usr/src/packages/KIWI-oem/build/image-root/etc/sysconfig/proxy]
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mount -n --bind /usr/src/packages/KIWI-oem/build/image-root /usr/src/packages/KIWI-oem/build/image-root]
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/proc
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/proc]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mount -n --bind /proc /usr/src/packages/KIWI-oem/build/image-root/proc]
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/dev
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/dev]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mount -n --bind /dev /usr/src/packages/KIWI-oem/build/image-root/dev]
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/sys
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/sys]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mount -n --bind /sys /usr/src/packages/KIWI-oem/build/image-root/sys]
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory //var/cache/kiwi
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mount -n --bind /var/cache/kiwi /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi]
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/repos
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/credentials
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/solv
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/raw
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper
[   17s] [ DEBUG   ]: 00:18:04 | Looking for rpmdb in /usr/src/packages/KIWI-oem/build/image-root/sbin:/usr/src/packages/KIWI-oem/build/image-root/usr/sbin:/usr/src/packages/KIWI-oem/build/image-root/usr/local/sbin:/usr/src/packages/KIWI-oem/build/image-root/root/bin:/usr/src/packages/KIWI-oem/build/image-root/usr/local/bin:/usr/src/packages/KIWI-oem/build/image-root/usr/bin:/usr/src/packages/KIWI-oem/build/image-root/bin
[   17s] [ DEBUG   ]: 00:18:04 | Looking for rpmdb in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [rpmdb --showrc]
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/usr/lib/rpm/macros.d
[   17s] [ DEBUG   ]: 00:18:04 | Looking for rpm in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [rpm --root /usr/src/packages/KIWI-oem/build/image-root --initdb]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for rpm in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [rpm -E %_dbpath]
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/var/lib
[   17s] [ DEBUG   ]: 00:18:04 | Looking for ln in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [ln -s --no-target-directory ../../usr/lib/sysimage/rpm /usr/src/packages/KIWI-oem/build/image-root/var/lib/rpm]
[   17s] [ INFO    ]: 00:18:04 | Setting up repository dir:///./repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images
[   17s] [ INFO    ]: 00:18:04 | --> Type: rpm-md
[   17s] [ INFO    ]: 00:18:04 | --> Priority: 1
[   17s] [ INFO    ]: 00:18:04 | --> Translated: /repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images
[   17s] [ INFO    ]: 00:18:04 | --> Alias: 39a7064a7b6d40419ca338d1530ca867
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory //repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mount -n --bind /repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mv in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mv -f /var/cache/kiwi/packages /var/cache/kiwi/packages.moved]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for zypper in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [zypper --non-interactive --gpg-auto-import-keys --pkg-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages --reposd-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/repos --solv-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/solv --cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper --raw-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/raw --config /usr/src/packages/KIWI-oem/build/image-root/kiwi_zypper.conf --root /usr/src/packages/KIWI-oem/build/image-root addrepo --refresh --no-keep-packages --no-check /repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images 39a7064a7b6d40419ca338d1530ca867]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mv in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mv -f /var/cache/kiwi/packages.moved /var/cache/kiwi/packages]
[   17s] [ INFO    ]: 00:18:04 | Setting up repository dir:///./repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard
[   17s] [ INFO    ]: 00:18:04 | --> Type: rpm-md
[   17s] [ INFO    ]: 00:18:04 | --> Priority: 2
[   17s] [ INFO    ]: 00:18:04 | --> Translated: /repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard
[   17s] [ INFO    ]: 00:18:04 | --> Alias: a5dfde9bdc9f42af8a90cad6d62e6be4
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory //repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mount -n --bind /repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mv in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mv -f /var/cache/kiwi/packages /var/cache/kiwi/packages.moved]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for zypper in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [zypper --non-interactive --gpg-auto-import-keys --pkg-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages --reposd-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/repos --solv-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/solv --cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper --raw-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/raw --config /usr/src/packages/KIWI-oem/build/image-root/kiwi_zypper.conf --root /usr/src/packages/KIWI-oem/build/image-root addrepo --refresh --no-keep-packages --no-check /repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard a5dfde9bdc9f42af8a90cad6d62e6be4]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mv in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mv -f /var/cache/kiwi/packages.moved /var/cache/kiwi/packages]
[   17s] [ INFO    ]: 00:18:04 | Setting up repository dir:///./repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard
[   17s] [ INFO    ]: 00:18:04 | --> Type: rpm-md
[   17s] [ INFO    ]: 00:18:04 | --> Priority: 3
[   17s] [ INFO    ]: 00:18:04 | --> Translated: /repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard
[   17s] [ INFO    ]: 00:18:04 | --> Alias: b43a2a9d8b49482b98a2976fb75e52e0
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory //repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mount -n --bind /repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mv in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mv -f /var/cache/kiwi/packages /var/cache/kiwi/packages.moved]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for zypper in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [zypper --non-interactive --gpg-auto-import-keys --pkg-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages --reposd-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/repos --solv-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/solv --cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper --raw-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/raw --config /usr/src/packages/KIWI-oem/build/image-root/kiwi_zypper.conf --root /usr/src/packages/KIWI-oem/build/image-root addrepo --refresh --no-keep-packages --no-check /repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard b43a2a9d8b49482b98a2976fb75e52e0]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mv in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mv -f /var/cache/kiwi/packages.moved /var/cache/kiwi/packages]
[   17s] [ INFO    ]: 00:18:04 | Setting up repository dir:///./repos/SUSE:/SLFO:/1.1:/Build/standard
[   17s] [ INFO    ]: 00:18:04 | --> Type: rpm-md
[   17s] [ INFO    ]: 00:18:04 | --> Priority: 4
[   17s] [ INFO    ]: 00:18:04 | --> Translated: /repos/SUSE:/SLFO:/1.1:/Build/standard
[   17s] [ INFO    ]: 00:18:04 | --> Alias: 6de94335089d4c52933dd6a1358ea051
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:/Build/standard
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory //repos/SUSE:/SLFO:/1.1:/Build/standard
[   17s] [ DEBUG   ]: 00:18:04 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:/Build/standard
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:/Build/standard]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mount -n --bind /repos/SUSE:/SLFO:/1.1:/Build/standard /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:/Build/standard]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for mv in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [mv -f /var/cache/kiwi/packages /var/cache/kiwi/packages.moved]
[   17s] [ DEBUG   ]: 00:18:04 | Looking for zypper in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:04 | EXEC: [zypper --non-interactive --gpg-auto-import-keys --pkg-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages --reposd-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/repos --solv-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/solv --cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper --raw-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/raw --config /usr/src/packages/KIWI-oem/build/image-root/kiwi_zypper.conf --root /usr/src/packages/KIWI-oem/build/image-root addrepo --refresh --no-keep-packages --no-check /repos/SUSE:/SLFO:/1.1:/Build/standard 6de94335089d4c52933dd6a1358ea051]
[   17s] [ DEBUG   ]: 00:18:05 | Looking for mv in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:05 | EXEC: [mv -f /var/cache/kiwi/packages.moved /var/cache/kiwi/packages]
[   17s] [ DEBUG   ]: 00:18:05 | Looking for rm in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ DEBUG   ]: 00:18:05 | EXEC: [rm -r -f /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/solv/@System]
[   17s] [ INFO    ]: 00:18:05 | Using package manager backend: zypper
[   17s] [ INFO    ]: 00:18:05 | Installing bootstrap packages
[   17s] [ INFO    ]: 00:18:05 | --> collection type: onlyRequired
[   17s] [ INFO    ]: 00:18:05 | --> package: ca-certificates
[   17s] [ INFO    ]: 00:18:05 | --> package: filesystem
[   17s] [ INFO    ]: 00:18:05 | --> package: glibc-locale
[   17s] [ INFO    ]: 00:18:05 | --> package: zypper
[   17s] [ DEBUG   ]: 00:18:05 | EXEC: [zypper --non-interactive --gpg-auto-import-keys --pkg-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages --reposd-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/repos --solv-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/solv --cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper --raw-cache-dir /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/zypper/raw --config /usr/src/packages/KIWI-oem/build/image-root/kiwi_zypper.conf --root /usr/src/packages/KIWI-oem/build/image-root install --download in-advance --auto-agree-with-licenses --no-recommends -- ca-certificates filesystem glibc-locale zypper]
[   17s] [ DEBUG   ]: 00:18:05 | Looking for zypper in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   17s] [ INFO    ]: Processing: [                                        ] 0%[ DEBUG   ]: 00:18:05 | bootstrap: Retrieving repository '39a7064a7b6d40419ca338d1530ca867' metadata [.done]
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Building repository '39a7064a7b6d40419ca338d1530ca867' cache [....done]
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving repository '6de94335089d4c52933dd6a1358ea051' metadata [.done]
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Building repository '6de94335089d4c52933dd6a1358ea051' cache [....done]
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving repository 'a5dfde9bdc9f42af8a90cad6d62e6be4' metadata [.done]
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Building repository 'a5dfde9bdc9f42af8a90cad6d62e6be4' cache [....done]
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving repository 'b43a2a9d8b49482b98a2976fb75e52e0' metadata [.done]
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Building repository 'b43a2a9d8b49482b98a2976fb75e52e0' cache [....done]
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Loading repository data...
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Reading installed packages...
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Resolving package dependencies...
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: The following 77 NEW packages are going to be installed:
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap:   bash bash-sh boost-license1_84_0 ca-certificates compat-usrmerge-tools coreutils crypto-policies filesystem fillup findutils glibc glibc-locale glibc-locale-base gpg2 krb5 libacl1 libassuan0 libattr1 libaugeas0 libboost_thread1_84_0 libbrotlicommon1 libbrotlidec1 libbz2-1 libcap2 libcom_err2 libcurl4 libfa1 libffi8 libgcc_s1 libgcrypt20 libglib-2_0-0 libgmp10 libgpg-error0 libgpgme11 libidn2-0 libjitterentropy3 libkeyutils1 libksba8 libldap2 liblua5_4-5 liblzma5 libncurses6 libnghttp2-14 libnpth0 libopenssl3 libp11-kit0 libpcre2-8-0 libpopt0 libpsl5 libreadline8 libsasl2-3 libselinux1 libsigc-2_0-0 libsolv-tools-base libsqlite3-0 libssh-config libssh4 libstdc++6 libtasn1-6 libudev1 libunistring5 libusb-1_0-0 libverto1 libxml2-2 libyaml-cpp0_8 libz1 libzck1 libzstd1 libzypp p11-kit p11-kit-tools pinentry rpm rpm-config-SUSE system-user-root terminfo-base zypper
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: 77 new packages to install.
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Package download size:    48.5 MiB
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Package install size change:
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap:               |     302.5 MiB  required by packages that will be installed
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap:    302.5 MiB  |  -      0 B    released by packages that will be removed
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Backend:  classic_rpmtrans
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Continue? [y/n/v/...? shows all options] (y): y
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: boost-license1_84_0-1.84.0-slfo.1.1_1.4.noarch (6de94335089d4c52933dd6a1358ea051) (1/77),  76.7 KiB    
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: compat-usrmerge-tools-84.87-slfo.1.1_1.5.x86_64 (6de94335089d4c52933dd6a1358ea051) (2/77), 286.5 KiB    
[   17s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: crypto-policies-20230920.570ea89-slfo.1.1_1.2.noarch (6de94335089d4c52933dd6a1358ea051) (3/77),  70.0 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libssh-config-0.10.6-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (4/77),  36.1 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: system-user-root-20190513-slfo.1.1_1.2.noarch (6de94335089d4c52933dd6a1358ea051) (5/77),   8.2 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: filesystem-84.87-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (6/77), 100.3 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: glibc-2.38-slfo.1.1_4.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (7/77),   2.1 MiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libzstd1-1.5.5-slfo.1.1_1.4.x86_64 (6de94335089d4c52933dd6a1358ea051) (8/77), 318.1 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libz1-1.2.13-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (9/77),  80.7 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libverto1-0.3.2-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (10/77),  19.4 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libunistring5-1.1-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (11/77), 542.3 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libtasn1-6-4.19.0-slfo.1.1_2.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (12/77),  70.1 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libsqlite3-0-3.44.2-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (13/77), 815.6 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libsasl2-3-2.1.28-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (14/77),  76.1 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libpopt0-1.19-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (15/77),  36.6 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libpcre2-8-0-10.42-slfo.1.1_1.4.x86_64 (6de94335089d4c52933dd6a1358ea051) (16/77), 346.7 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libnpth0-1.6-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (17/77),  14.2 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libnghttp2-14-1.52.0-slfo.1.1_1.4.x86_64 (6de94335089d4c52933dd6a1358ea051) (18/77), 129.6 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: liblzma5-5.4.3-slfo.1.1_1.4.x86_64 (6de94335089d4c52933dd6a1358ea051) (19/77), 154.9 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: liblua5_4-5-5.4.6-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (20/77), 134.6 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libkeyutils1-1.6.3-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (21/77),  29.8 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libjitterentropy3-3.4.1-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (22/77),  26.1 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libgpg-error0-1.47-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (23/77), 293.1 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libgmp10-6.3.0-slfo.1.1_1.5.x86_64 (6de94335089d4c52933dd6a1358ea051) (24/77), 316.0 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libgcc_s1-14.2.0+git10526-slfo.1.1_2.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (25/77),  88.7 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libffi8-3.4.6-slfo.1.1_1.4.x86_64 (6de94335089d4c52933dd6a1358ea051) (26/77),  30.9 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libfa1-1.14.1-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (27/77), 102.2 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libcom_err2-1.47.0-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (28/77),  87.1 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libcap2-2.69-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (29/77),  51.0 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libbz2-1-1.0.8-slfo.1.1_1.4.x86_64 (6de94335089d4c52933dd6a1358ea051) (30/77),  51.9 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libbrotlicommon1-1.1.0-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (31/77),  69.6 KiB    
[   18s] [ DEBUG   ]: 00:18:05 | bootstrap: Retrieving: libattr1-2.5.1-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (32/77),  35.7 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libacl1-2.3.1-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (33/77),  42.6 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: fillup-1.42-slfo.1.1_2.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (34/77),  26.9 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libidn2-0-2.3.4-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (35/77),  72.0 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libselinux1-3.5-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (36/77),  99.1 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libglib-2_0-0-2.78.6-slfo.1.1_2.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (37/77), 941.6 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libxml2-2-2.11.6-slfo.1.1_3.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (38/77), 712.5 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libopenssl3-3.1.4-slfo.1.1_4.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (39/77),   2.0 MiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libksba8-1.6.4-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (40/77), 160.9 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libgcrypt20-1.10.3-slfo.1.1_1.10.x86_64 (6de94335089d4c52933dd6a1358ea051) (41/77), 748.2 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libassuan0-2.5.6-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (42/77),  79.9 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libstdc++6-14.2.0+git10526-slfo.1.1_2.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (43/77), 729.7 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libp11-kit0-0.25.3-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (44/77), 453.2 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libudev1-254.24-slfo.1.1_1.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (45/77), 552.4 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libbrotlidec1-1.1.0-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (46/77),  33.0 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libpsl5-0.21.2-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (47/77),  63.8 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: findutils-4.9.0-slfo.1.1_2.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (48/77), 306.2 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libaugeas0-1.14.1-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (49/77), 203.7 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libzck1-1.3.2-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (50/77),  50.2 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libldap2-2.6.4-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (51/77), 289.7 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: krb5-1.21.3-slfo.1.1_2.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (52/77), 752.1 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libyaml-cpp0_8-0.8.0-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (53/77), 114.9 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libsigc-2_0-0-2.12.0-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (54/77),  61.9 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libncurses6-6.4.20240224-slfo.1.1_1.5.x86_64 (6de94335089d4c52933dd6a1358ea051) (55/77), 679.3 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: terminfo-base-6.4.20240224-slfo.1.1_1.5.x86_64 (6de94335089d4c52933dd6a1358ea051) (56/77), 433.6 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libboost_thread1_84_0-1.84.0-slfo.1.1_1.4.x86_64 (6de94335089d4c52933dd6a1358ea051) (57/77), 111.0 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: p11-kit-0.25.3-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (58/77), 221.2 KiB    
[   18s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: p11-kit-tools-0.25.3-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (59/77), 145.8 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libusb-1_0-0-1.0.27-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (60/77),  83.6 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libssh4-0.10.6-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (61/77), 221.2 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libreadline8-8.2-slfo.1.1_1.4.x86_64 (6de94335089d4c52933dd6a1358ea051) (62/77), 250.6 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libcurl4-8.12.1-slfo.1.1_1.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (63/77), 629.4 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: bash-5.2.15-slfo.1.1_1.6.x86_64 (6de94335089d4c52933dd6a1358ea051) (64/77), 670.4 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: bash-sh-5.2.15-slfo.1.1_1.6.noarch (6de94335089d4c52933dd6a1358ea051) (65/77), 118.4 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: pinentry-1.2.1-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (66/77), 115.9 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: coreutils-9.4-slfo.1.1_1.4.x86_64 (6de94335089d4c52933dd6a1358ea051) (67/77),   1.2 MiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: gpg2-2.4.4-slfo.1.1_1.2.x86_64 (6de94335089d4c52933dd6a1358ea051) (68/77),   2.5 MiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: rpm-config-SUSE-20240214-slfo.1.1_1.2.noarch (6de94335089d4c52933dd6a1358ea051) (69/77),  30.7 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: rpm-4.18.0-slfo.1.1_1.5.x86_64 (6de94335089d4c52933dd6a1358ea051) (70/77), 947.8 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: glibc-locale-base-2.38-slfo.1.1_4.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (71/77),   2.0 MiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: ca-certificates-2+git20240805.fd24d50-slfo.1.1_1.2.noarch (6de94335089d4c52933dd6a1358ea051) (72/77),  29.3 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libgpgme11-1.23.0-slfo.1.1_1.5.x86_64 (6de94335089d4c52933dd6a1358ea051) (73/77), 192.6 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libsolv-tools-base-0.7.30-slfo.1.1_1.3.x86_64 (6de94335089d4c52933dd6a1358ea051) (74/77), 307.1 KiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: glibc-locale-2.38-slfo.1.1_4.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (75/77),  18.3 MiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: libzypp-17.35.16-slfo.1.1_1.1.x86_64 (6de94335089d4c52933dd6a1358ea051) (76/77),   2.9 MiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Retrieving: zypper-1.14.77-slfo.1.1_1.7.x86_64 (6de94335089d4c52933dd6a1358ea051) (77/77),   1.7 MiB    
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: Checking for file conflicts: [..done]
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: ( 1/77) Installing: boost-license1_84_0-1.84.0-slfo.1.1_1.4.noarch [.
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/boost-license1_84_0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: ( 2/77) Installing: compat-usrmerge-tools-84.87-slfo.1.1_1.5.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/compat-usrmerge-tools.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: ( 3/77) Installing: crypto-policies-20230920.570ea89-slfo.1.1_1.2.noarch [.
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/crypto-policies.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: ( 4/77) Installing: libssh-config-0.10.6-slfo.1.1_1.3.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libssh-config.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:06 | bootstrap: ( 5/77) Installing: system-user-root-20190513-slfo.1.1_1.2.noarch [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/system-user-root.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: ( 6/77) Installing: filesystem-84.87-slfo.1.1_1.2.x86_64 [.
[   19s] [ INFO    ]: Processing: [##########                              ] 25%[ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/filesystem.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: ( 7/77) Installing: glibc-2.38-slfo.1.1_4.1.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/glibc.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: ( 8/77) Installing: libzstd1-1.5.5-slfo.1.1_1.4.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libzstd1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: ( 9/77) Installing: libz1-1.2.13-slfo.1.1_1.3.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libz1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: (10/77) Installing: libverto1-0.3.2-slfo.1.1_1.2.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libverto1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: (11/77) Installing: libunistring5-1.1-slfo.1.1_1.2.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libunistring5.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: (12/77) Installing: libtasn1-6-4.19.0-slfo.1.1_2.1.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libtasn1-6.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: (13/77) Installing: libsqlite3-0-3.44.2-slfo.1.1_1.2.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libsqlite3-0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: (14/77) Installing: libsasl2-3-2.1.28-slfo.1.1_1.2.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libsasl2-3.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: (15/77) Installing: libpopt0-1.19-slfo.1.1_1.3.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libpopt0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: (16/77) Installing: libpcre2-8-0-10.42-slfo.1.1_1.4.x86_64 [.
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libpcre2-8-0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   19s] [ DEBUG   ]: 00:18:07 | bootstrap: (17/77) Installing: libnpth0-1.6-slfo.1.1_1.2.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libnpth0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (18/77) Installing: libnghttp2-14-1.52.0-slfo.1.1_1.4.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libnghttp2-14.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (19/77) Installing: liblzma5-5.4.3-slfo.1.1_1.4.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/liblzma5.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (20/77) Installing: liblua5_4-5-5.4.6-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/liblua5_4-5.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (21/77) Installing: libkeyutils1-1.6.3-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libkeyutils1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (22/77) Installing: libjitterentropy3-3.4.1-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libjitterentropy3.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (23/77) Installing: libgpg-error0-1.47-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libgpg-error0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (24/77) Installing: libgmp10-6.3.0-slfo.1.1_1.5.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libgmp10.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (25/77) Installing: libgcc_s1-14.2.0+git10526-slfo.1.1_2.1.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libgcc_s1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (26/77) Installing: libffi8-3.4.6-slfo.1.1_1.4.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libffi8.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (27/77) Installing: libfa1-1.14.1-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libfa1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (28/77) Installing: libcom_err2-1.47.0-slfo.1.1_1.2.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libcom_err2.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: (29/77) Installing: libcap2-2.69-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libcap2.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:07 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (30/77) Installing: libbz2-1-1.0.8-slfo.1.1_1.4.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libbz2-1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (31/77) Installing: libbrotlicommon1-1.1.0-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libbrotlicommon1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (32/77) Installing: libattr1-2.5.1-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libattr1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (33/77) Installing: libacl1-2.3.1-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libacl1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (34/77) Installing: fillup-1.42-slfo.1.1_2.2.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/fillup.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (35/77) Installing: libidn2-0-2.3.4-slfo.1.1_1.2.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libidn2-0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (36/77) Installing: libselinux1-3.5-slfo.1.1_1.3.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libselinux1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (37/77) Installing: libglib-2_0-0-2.78.6-slfo.1.1_2.1.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libglib-2_0-0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (38/77) Installing: libxml2-2-2.11.6-slfo.1.1_3.1.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libxml2-2.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (39/77) Installing: libopenssl3-3.1.4-slfo.1.1_4.1.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libopenssl3.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (40/77) Installing: libksba8-1.6.4-slfo.1.1_1.2.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libksba8.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (41/77) Installing: libgcrypt20-1.10.3-slfo.1.1_1.10.x86_64 [.
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libgcrypt20.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   20s] [ DEBUG   ]: 00:18:08 | bootstrap: (42/77) Installing: libassuan0-2.5.6-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libassuan0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (43/77) Installing: libstdc++6-14.2.0+git10526-slfo.1.1_2.1.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libstdc++6.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (44/77) Installing: libp11-kit0-0.25.3-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libp11-kit0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (45/77) Installing: libudev1-254.24-slfo.1.1_1.1.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libudev1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (46/77) Installing: libbrotlidec1-1.1.0-slfo.1.1_1.3.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libbrotlidec1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (47/77) Installing: libpsl5-0.21.2-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libpsl5.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (48/77) Installing: findutils-4.9.0-slfo.1.1_2.1.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/findutils.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (49/77) Installing: libaugeas0-1.14.1-slfo.1.1_1.3.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libaugeas0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (50/77) Installing: libzck1-1.3.2-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libzck1.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (51/77) Installing: libldap2-2.6.4-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libldap2.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (52/77) Installing: krb5-1.21.3-slfo.1.1_2.1.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/krb5.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (53/77) Installing: libyaml-cpp0_8-0.8.0-slfo.1.1_1.3.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libyaml-cpp0_8.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (54/77) Installing: libsigc-2_0-0-2.12.0-slfo.1.1_1.3.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libsigc-2_0-0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (55/77) Installing: libncurses6-6.4.20240224-slfo.1.1_1.5.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libncurses6.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (56/77) Installing: terminfo-base-6.4.20240224-slfo.1.1_1.5.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/terminfo-base.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (57/77) Installing: libboost_thread1_84_0-1.84.0-slfo.1.1_1.4.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libboost_thread1_84_0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (58/77) Installing: p11-kit-0.25.3-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/p11-kit.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (59/77) Installing: p11-kit-tools-0.25.3-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/p11-kit-tools.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (60/77) Installing: libusb-1_0-0-1.0.27-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libusb-1_0-0.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (61/77) Installing: libssh4-0.10.6-slfo.1.1_1.3.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libssh4.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (62/77) Installing: libreadline8-8.2-slfo.1.1_1.4.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libreadline8.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (63/77) Installing: libcurl4-8.12.1-slfo.1.1_1.1.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libcurl4.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (64/77) Installing: bash-5.2.15-slfo.1.1_1.6.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/bash.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (65/77) Installing: bash-sh-5.2.15-slfo.1.1_1.6.noarch [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/bash-sh.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (66/77) Installing: pinentry-1.2.1-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/pinentry.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (67/77) Installing: coreutils-9.4-slfo.1.1_1.4.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/coreutils.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (68/77) Installing: gpg2-2.4.4-slfo.1.1_1.2.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/gpg2.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (69/77) Installing: rpm-config-SUSE-20240214-slfo.1.1_1.2.noarch [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/rpm-config-SUSE.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (70/77) Installing: rpm-4.18.0-slfo.1.1_1.5.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/rpm.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: ..
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: Updating /etc/sysconfig/services ...
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (71/77) Installing: glibc-locale-base-2.38-slfo.1.1_4.1.x86_64 [.
[   21s] [ INFO    ]: Processing: [####################                    ] 50%[ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/glibc-locale-base.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (72/77) Installing: ca-certificates-2+git20240805.fd24d50-slfo.1.1_1.2.noarch [.
[   21s] [ INFO    ]: Processing: [##############################          ] 75%[ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/ca-certificates.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (73/77) Installing: libgpgme11-1.23.0-slfo.1.1_1.5.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libgpgme11.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (74/77) Installing: libsolv-tools-base-0.7.30-slfo.1.1_1.3.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libsolv-tools-base.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (75/77) Installing: glibc-locale-2.38-slfo.1.1_4.1.x86_64 [.
[   21s] [ INFO    ]: Processing: [########################################] 100%[ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/glibc-locale.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: ..........done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (76/77) Installing: libzypp-17.35.16-slfo.1.1_1.1.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/libzypp.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: (77/77) Installing: zypper-1.14.77-slfo.1.1_1.7.x86_64 [.
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: warning: /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi/packages/6de94335089d4c52933dd6a1358ea051/zypper.rpm: Header V3 RSA/SHA256 Signature, key ID 09d9ea69: NOKEY
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: .done]
[   21s] [ DEBUG   ]: 00:18:08 | bootstrap: Running post-transaction scripts [...done]
[   21s] [ INFO    ]: Processing: [########################################] 100%
[   21s] [ DEBUG   ]: 00:18:08 | Looking for rpmdb in /usr/src/packages/KIWI-oem/build/image-root/sbin:/usr/src/packages/KIWI-oem/build/image-root/usr/sbin:/usr/src/packages/KIWI-oem/build/image-root/usr/local/sbin:/usr/src/packages/KIWI-oem/build/image-root/root/bin:/usr/src/packages/KIWI-oem/build/image-root/usr/local/bin:/usr/src/packages/KIWI-oem/build/image-root/usr/bin:/usr/src/packages/KIWI-oem/build/image-root/bin
[   21s] [ DEBUG   ]: 00:18:08 | Looking for chroot in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [chroot /usr/src/packages/KIWI-oem/build/image-root rpmdb --rebuilddb]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for rm in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [rm -r -f /usr/src/packages/KIWI-oem/build/image-root/usr/lib/rpm/macros.d/macros.kiwi-bootstrap-config]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for chroot in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [chroot /usr/src/packages/KIWI-oem/build/image-root rpm -E %_dbpath]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for rpm in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [rpm -E %_dbpath]
[   21s] [ INFO    ]: 00:18:08 | Importing Image description to system tree
[   21s] [ INFO    ]: 00:18:08 | --> Importing state XML description to /usr/src/packages/KIWI-oem/build/image-root/image/config.xml
[   21s] [ DEBUG   ]: 00:18:08 | Creating directory /usr/src/packages/KIWI-oem/build/image-root/image
[   21s] [ INFO    ]: 00:18:08 | --> Importing config.sh script to /usr/src/packages/KIWI-oem/build/image-root/image/config.sh
[   21s] [ DEBUG   ]: 00:18:08 | Looking for cp in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [cp /usr/src/packages/SOURCES/config.sh /usr/src/packages/KIWI-oem/build/image-root/image/config.sh]
[   21s] [ INFO    ]: 00:18:08 | --> Importing script helper functions
[   21s] [ DEBUG   ]: 00:18:08 | Looking for cp in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [cp /usr/lib/python3.11/site-packages/kiwi/config/functions.sh /usr/src/packages/KIWI-oem/build/image-root/.kconfig]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:/Build/standard]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:/Build/standard]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for umount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [umount -l /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:/Build/standard]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for umount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [umount -l /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for umount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [umount -l /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for umount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [umount -l /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for umount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [umount -l /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/sys]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/sys]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for umount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [umount -l /usr/src/packages/KIWI-oem/build/image-root/sys]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/dev]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/dev]
[   21s] [ DEBUG   ]: 00:18:08 | Looking for umount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:08 | EXEC: [umount -l /usr/src/packages/KIWI-oem/build/image-root/dev]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/proc]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root/proc]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for umount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [umount -l /usr/src/packages/KIWI-oem/build/image-root/proc]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for mountpoint in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [mountpoint -q /usr/src/packages/KIWI-oem/build/image-root]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for umount in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [umount -l /usr/src/packages/KIWI-oem/build/image-root]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:/Build/standard]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:/Build]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/1.1:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1/standard]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:/6.1]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:/SL-Micro:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Products:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:/Build/standard]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:/Build]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:/1.0:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:/Kernel:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:/SLFO:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/SUSE:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1/images]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:/SL-Micro-6.1]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:/Images:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:/Templates:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:/SUSE:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:/branches:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/home:/Ivoslav:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos/home:]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/repos]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/var/cache/kiwi]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/var/cache]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rmdir in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rmdir --ignore-fail-on-non-empty /usr/src/packages/KIWI-oem/build/image-root/var]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rm in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rm -f /usr/src/packages/KIWI-oem/build/image-root/etc/hosts]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rm in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rm -f /usr/src/packages/KIWI-oem/build/image-root/etc/sysconfig/proxy]
[   21s] [ DEBUG   ]: 00:18:09 | Looking for rm in /sbin:/usr/sbin:/usr/local/sbin:/root/bin:/usr/local/bin:/usr/bin:/bin
[   21s] [ DEBUG   ]: 00:18:09 | EXEC: [rm -f /usr/src/packages/KIWI-oem/build/image-root/etc/hosts.kiwi /usr/src/packages/KIWI-oem/build/image-root/etc/hosts.sha /usr/src/packages/KIWI-oem/build/image-root/etc/sysconfig/proxy.kiwi /usr/src/packages/KIWI-oem/build/image-root/etc/sysconfig/proxy.sha]
[   21s] [ ERROR   ]: 00:18:09 | KiwiImportDescriptionError: Specified archive /usr/src/packages/SOURCES/root.tar.gz does not exist
[   21s] ### VM INTERACTION START ###
[   21s] Powering off.
[   21s] [   16.950957][ T2141] reboot: Power down
[   21s] ### VM INTERACTION END ###
[   21s] 
[   21s] h02-ch2a failed "build PersistenceOS.kiwi" at Thu May 29 00:18:09 UTC 2025.
[   21s] 