# PersistenceOS Project - Estimated File Count with Explanations

## Core System Scripts (7)
1. install-manager.sh - Consolidated installation script with command-based interface for all installation tasks (prepare, bootloader, system, post, packages, scripts, all, verify, status, cleanup). This script handles partitioning (including BTRFS subvolumes like @/ , @/home, @/opt, @/srv, @/usr/local, @/var, @/boot_writable, @/tmp), formatting, OS image deployment (e.g., from a tarball), bootloader installation (GRUB2), fstab creation (using UUIDs and correct BTRFS/XFS options), system configuration (hostname, timezone, locale, keyboard), Snapper setup (conditional based on installer.conf), service enablement (SSH, Libvirt, Podman - conditional based on installer.conf), welcome message and profile script creation, and interacts with `installer.conf` for various settings (LOG_FILE, HOSTNAME, TIMEZONE, etc.). It replaces and consolidates functionality from multiple previous scripts:
   - Oldeditbootinstaller.sh - Previous bootloader configuration script
   - editbootinstaller.sh - Bootloader configuration script
   - persistenceos-install.sh - Main installation script
   - install-bootloader.sh - Bootloader installation script
   - install-system.sh - System installation script
   - install-packages.sh - Package installation script (now called by install-manager.sh)
   - install-scripts.sh - Script installation script (now called by install-manager.sh)
2. config.sh - Primary system configuration script executed during OS image creation; sets up directories, services, and initial configuration
3. util-common.sh - Common utility functions used by all scripts; provides logging, error handling, and shared functionality
4. config-manager.sh - Manages system configuration settings; provides a unified interface for configuration tasks
5. config-firewall.sh - Configures nftables firewall rules; called by config.sh and install-manager.sh
6. system-manager.sh - Consolidated script for system management operations; provides commands for status, health, updates, etc.
7. system-health.sh - Monitors system health metrics; called by health-manager.sh and system-manager.sh
8. service-generator.sh - Generates standardized service files from templates; used during installation and system updates

## Web UI Management Scripts (3)
1. ui-manager.sh - Consolidated script that handles all UI operations (prepare, extract, initialize, reset, check, bundle); replaces multiple individual UI scripts
2. ui-migration.sh - Facilitates migration from individual UI scripts to the consolidated ui-manager.sh; creates compatibility wrappers
3. UI-Version-Check.sh - Checks for updates to UI files; will eventually be replaced by ui-manager.sh check command

## Network Management Scripts (1)
1. network-manager.sh - Consolidated script for network configuration and management; interfaces with NetworkManager and nftables; includes update-motd command to display system IP address in welcome message

## Utility Scripts (4)
1. fix-network.sh - Repairs network configuration issues; can be called manually when network connectivity is lost or improperly configured
2. health-check.sh - System health diagnostics utility; performs comprehensive system checks and generates health reports; non-essential for web UI installation
3. health-fix.sh - Automated issue resolution utility; fixes common system problems identified by health-check.sh; non-essential for web UI installation
4. service-health.sh - Service monitoring and management utility; checks status of critical services and can restart failed services; non-essential for web UI installation

## Storage Management Scripts (2)
1. storage-manager.sh - Consolidated script for storage management; handles pools, volumes, and snapshots using btrfs, xfs, and lvm2
2. persistence-storage.sh - Legacy script for storage operations; functionality being migrated to storage-manager.sh

## Health Check System Scripts (7)
1. health-manager.sh - Coordinates all health check operations; aggregates health status from different components; called by persistenceos-health.service
2. health-check-api.sh - Monitors API health and performance; checks API endpoints and response times; called by health-manager.sh
3. health-check-system.sh - Monitors system resource health (CPU, memory, disk, services); called by health-manager.sh
4. health-check-storage.sh - Monitors storage health and performance; checks SMART status and filesystem integrity; called by health-manager.sh
5. health-check-network.sh - Monitors network connectivity and performance; checks interfaces and DNS; called by health-manager.sh
6. health-check-vm.sh - Monitors virtual machine health; checks VM status and resource usage; called by health-manager.sh
7. vm-health.sh - Utility script for VM health monitoring; provides detailed VM diagnostics; non-essential for web UI installation

## Backup System Scripts (5)
1. backup-manager.sh - Coordinates all backup operations using snapper and btrfs; schedules and manages snapshots; called by persistenceos-backup.service
2. backup-system.sh - Creates system configuration backups using btrfs snapshots; called by backup-manager.sh
3. backup-vm.sh - Backs up virtual machines using libvirt snapshots coordinated with btrfs; called by backup-manager.sh
4. backup-storage.sh - Backs up storage configuration with snapper integration; called by backup-manager.sh
5. backup-restore.sh - Restores from btrfs snapshots; can restore system, VMs, or storage; called manually or via web UI

## Upgrade System Scripts (4)
1. upgrade-manager.sh - Coordinates the entire upgrade process; handles version detection, prerequisite checking, and upgrade orchestration; creates pre-upgrade snapshots; called by persistenceos-upgrade.service
2. version-migration.sh - Handles specific migration tasks between version pairs; contains version-specific logic for configuration changes; called by upgrade-manager.sh
3. upgrade-check.sh - Checks for available updates; verifies system compatibility with upgrades; called by upgrade-manager.sh or API
4. upgrade-rollback.sh - Handles rollback to previous version if upgrade fails; restores from pre-upgrade snapshot; called by upgrade-manager.sh

## Service Files (18)
1. persistenceos-installer.service - Main installer service that runs install-system.sh when triggered from bootloader; starts on boot with install parameter
2. persistenceos-installer-debug.service - Debug service that collects diagnostic information during installation; runs before main installer
3. persistenceos-installer-environment.service - Sets up the installation environment; ensures proper runlevel and console access
4. persistenceos-api.service - Runs the FastAPI/Uvicorn backend service; provides API endpoints for the web UI
5. persistenceos-firstboot.service - Performs first boot configuration; runs once after installation completes
6. persistenceos-ui-setup.service - Sets up the web UI during first boot; calls ui-manager.sh initialize
7. persistenceos-network-manager.service - Updates the MOTD with the current IP address; runs network-manager.sh update-motd after network is available
8. persistenceos-nftables-setup.service - Configures firewall rules using nftables; runs during boot
9. persistenceos-services-init.service - Initializes and enables required services; runs during first boot
10. persistenceos-health.service - Runs health-manager.sh to monitor system health; scheduled service that runs periodically
11. persistenceos-health-api.service - Monitors API health specifically; runs alongside the main health service
12. persistenceos-backup.service - Runs backup-manager.sh to create snapshots; triggered by timer or manually
13. persistenceos-backup.timer - Schedules regular execution of the backup service; runs daily by default
14. persistenceos-snapper-config.service - Configures snapper for different subvolumes; runs before backup service
15. persistenceos-health-daemon.service - Long-running daemon that continuously monitors system health; provides real-time alerts
16. persistenceos-upgrade.service - Handles the system upgrade process; runs upgrade-manager.sh when triggered manually or via API
17. persistenceos-upgrade-check.service - Periodically checks for available updates; runs upgrade-check.sh
18. graphical.target.d/graphical.conf - Systemd drop-in configuration for graphical target; ensures proper boot mode during installation

## Web UI Files (14)
1. login.html - Authentication page; first page users see; submits credentials to auth API endpoint
2. index.html - Main interface with hash-based navigation; contains all UI sections and sidebar
3. style.css - Main stylesheet with all core styles; imported by both login.html and index.html
4. components.css - Component-specific styles for cards, buttons, forms, etc.; imported by index.html
5. loading-indicators.css - Styles for loading spinners and overlays; used during API calls
6. notifications.css - Styles for error and success notifications; used by error-handlers.js
7. variables.css - CSS custom properties (variables) for consistent styling and theming; defines colors, spacing, typography, etc.
8. normalize.css - CSS reset file that normalizes styles across different browsers; provides consistent baseline
9. layout.css - Layout-specific styles for grid, flexbox, and responsive design; controls page structure
10. theme.css - Theme-specific styles for light/dark mode and customization; supports theme switching
11. persistenceos.conf - Nginx configuration file; sets up static file serving and API proxying
12. version.json - Contains UI version information; used for update checking
13. favicon.ico - Browser tab icon; displayed in browser tabs and bookmarks
14. logo.svg - PersistenceOS logo used throughout the UI; displayed in sidebar and login page

## JavaScript Modules (8)
1. auth.js - Handles authentication and token management; used by both login.html and index.html
2. api.js - Centralized API communication module; makes fetch requests to backend endpoints
3. app.js - Main application logic; handles navigation, initialization, and UI updates
4. error-handlers.js - Centralized error handling; displays notifications and handles API errors
5. loading-indicators.js - Manages loading states during API calls; shows spinners and prevents multiple submissions
6. theme-manager.js - Manages theme switching between light and dark modes; persists user preferences
7. dashboard-widgets.js - Handles dashboard widget rendering and data updates; creates charts and status displays
8. system-monitor.js - Real-time system monitoring; updates metrics and status indicators via WebSockets

## Python API Files (14)
1. app.py - Main FastAPI application entry point; sets up the API server and imports the package modules
2. persistenceos/package_init.py - Package initialization file (renamed from __init__.py for OBS compatibility); defines package metadata
3. persistenceos/main.py - Core FastAPI application module; sets up routes, middleware, and authentication
4. persistenceos/routers/routers_init.py - Routers package initialization (renamed from __init__.py for OBS compatibility)
5. persistenceos/utils/utils_init.py - Utils package initialization (renamed from __init__.py for OBS compatibility)
6. system_router.py - API endpoints for system information and management; calls system-manager.sh
7. vm_router.py - API endpoints for VM management; interfaces with libvirt through shell scripts
8. storage_router.py - API endpoints for storage management; calls storage-manager.sh
9. network_router.py - API endpoints for network configuration; calls network-manager.sh
10. auth_router.py - API endpoints for authentication and user management
11. settings_router.py - API endpoints for system settings and configuration
12. snapshots_router.py - API endpoints for snapshot management; coordinates with backup scripts
13. upgrade_router.py - API endpoints for system upgrades; interfaces with upgrade-manager.sh
14. shell_integration.py - Utility for executing shell scripts and parsing their output

## Configuration Files (5)
1. PersistenceOS.kiwi - Main build file for KIWI image builder; defines packages, scripts, and configuration
2. service-dependencies.json - Defines relationships between services; used by service-generator.sh
3. packages-req.md - List of required packages with explanations; referenced during installation
4. persistenceos-nginx.conf - Nginx configuration template; used to generate the final nginx configuration
5. persistenceos-api.service - Service file template for the API service; used by service-generator.sh

## Documentation Files (2)
1. PersistenceOS-Structure.md - Comprehensive documentation of project structure, architecture, and implementation details
2. rebuildProject.md - Project plan with phases, components, and implementation guidelines

## Total File Count: 99 files

### Breakdown by Category:
- System Scripts: 34 (34.3%) - Including 4 new upgrade scripts, minus 1 consolidated
- Service Files: 18 (18.2%) - Including 2 new upgrade services and network-manager service
- Web UI & JavaScript: 22 (22.2%) - Including CSS foundation files and new JavaScript modules
- Python API Files: 14 (14.1%) - Including __init__.py files and app.py entry point
- Configuration Files: 5 (5.1%)
- Utility Scripts: 4 (4.0%) - Including health check and service monitoring utilities
- Documentation Files: 2 (2.0%)

### Key File Relationships and Workflows

1. **Installation Flow**:
   - Boot with install parameter → persistenceos-installer.service → install-manager.sh all
   - install-manager.sh all sequence: prepare -> system -> packages -> bootloader -> post (fstab, system, scripts, services, welcome, profile) -> verify
   - The consolidated install-manager.sh script handles all installation tasks with a command-based interface
   - All installation operations use util-common.sh for shared functions
   - The installation process follows standard Linux installation practices with logical ordering
   - Explicit verification steps ensure critical components are properly installed

2. **Web UI Flow**:
   - User accesses server → Nginx (persistenceos.conf) → login.html
   - login.html → auth.js → API (auth_router.py) → authentication
   - Successful login → index.html → app.js → API endpoints
   - API requests use loading-indicators.js and error-handlers.js

3. **API Backend Flow**:
   - API request → Nginx → FastAPI (app.py) → persistenceos/main.py → specific router
   - Router → shell_integration.py → appropriate shell script
   - Shell script → MicroOS packages → result
   - Result → Router → JSON response → JavaScript → UI update

4. **Health Monitoring Flow**:
   - persistenceos-health.service → health-manager.sh
   - health-manager.sh → individual health check scripts
   - Health data → API (system_router.py) → Web UI

5. **Backup Flow**:
   - persistenceos-backup.timer → persistenceos-backup.service
   - persistenceos-backup.service → backup-manager.sh
   - backup-manager.sh → individual backup scripts
   - Snapshots → btrfs/snapper → consistent system state

6. **Upgrade Flow**:
   - User initiates upgrade → Web UI → API (upgrade_router.py) → upgrade-manager.sh
   - upgrade-manager.sh → upgrade-check.sh → verify compatibility
   - upgrade-manager.sh → backup-manager.sh → create pre-upgrade snapshot
   - upgrade-manager.sh → transactional-update → apply system updates
   - upgrade-manager.sh → version-migration.sh → migrate configurations
   - If failure → upgrade-rollback.sh → restore from snapshot
   - System reboot → upgraded PersistenceOS

This count represents the core files needed for the PersistenceOS project. The actual implementation might include additional helper files, assets, or generated files. The consolidation strategy (using manager scripts with command-based interfaces) has helped reduce the total number of scripts from what could have been a much larger count.

### Consolidation Benefits

The consolidated manager scripts (ui-manager.sh, system-manager.sh, etc.) provide several advantages:
- Reduced file count (from potentially 100+ files to 85)
- Consistent command-line interfaces across different subsystems
- Shared code for common operations
- Simplified maintenance and updates
- Clearer documentation and usage patterns
- Better error handling and logging

## Future Implementation Files

These files represent planned future enhancements to extend PersistenceOS functionality beyond the core implementation.

### Advanced Networking (SDN and Network Virtualization) (12)
1. network-sdn-manager.sh - Central script for SDN management; integrates with Open vSwitch and OVN
2. network-virtualization.sh - Script for virtual network management; creates and manages virtual networks
3. persistenceos-sdn-controller.service - Service for running SDN controller; starts and monitors the SDN controller
4. persistenceos-network-virtualization.service - Service for virtual network management; manages virtual network lifecycle
5. network-topology.js - Frontend visualization of network topology; provides interactive network diagram
6. sdn_router.py - API endpoints for SDN management; interfaces with network-sdn-manager.sh
7. virtual_network.py - API models for virtual networks; defines data structures for network objects
8. ovs-integration.sh - Integration with Open vSwitch; provides low-level OVS operations
9. ovn-integration.sh - Integration with OVN; provides logical network operations
10. network-firewall.sh - Firewall management for virtual networks; creates and manages firewall rules
11. network-qos.sh - Quality of Service for virtual networks; manages bandwidth and priority
12. vm-network-templates.xml - Templates for VM network configurations; used by libvirt

### Testing Framework (8)
1. test-runner.sh - Automated test execution script; runs all tests and generates reports
2. unit-tests/ - Directory containing unit tests for scripts; tests individual script functions
3. api-tests/ - Directory containing API integration tests; tests API endpoints
4. ui-tests/ - Directory containing UI tests; tests web interface functionality
5. test-report-generator.sh - Creates test reports in HTML format; summarizes test results
6. test-fixtures.sh - Creates test fixtures and environments; sets up test conditions
7. test-cleanup.sh - Cleans up after tests; removes test artifacts
8. test-config.json - Configuration for test framework; defines test parameters

### Clustering and High Availability (10)
1. cluster-manager.sh - Management of multi-node clusters; coordinates cluster operations
2. ha-manager.sh - High availability configuration and monitoring; manages failover
3. node-sync.sh - Synchronization between cluster nodes; keeps nodes in sync
4. persistenceos-cluster.service - Cluster management service; runs cluster operations
5. persistenceos-ha-monitor.service - High availability monitoring service; detects failures
6. cluster_router.py - API endpoints for cluster management; interfaces with cluster-manager.sh
7. cluster-storage.sh - Shared storage for clusters; manages distributed storage
8. cluster-network.sh - Network configuration for clusters; manages cluster networking
9. cluster-quorum.sh - Quorum management for clusters; handles split-brain scenarios
10. cluster-migrate.sh - VM migration between nodes; moves VMs for load balancing or failover

### Enhanced Container Support (8)
1. container-manager.sh - Advanced container management; manages container lifecycle
2. container-network.sh - Container-specific networking; creates container networks
3. container-storage.sh - Optimized storage for containers; manages container volumes
4. persistenceos-container.service - Container management service; runs container operations
5. container_router.py - API endpoints for container management; interfaces with container-manager.sh
6. container-templates/ - Directory containing container templates; defines container configurations
7. container-compose.sh - Multi-container deployment; similar to docker-compose
8. container-registry.sh - Private container registry; manages container images
