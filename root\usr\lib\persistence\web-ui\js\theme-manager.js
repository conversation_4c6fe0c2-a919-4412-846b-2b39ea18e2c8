/**
 * PersistenceOS Web UI - Theme Manager
 * 
 * This module handles theme switching between light, dark, and high-contrast modes.
 * It persists user preferences and provides an API for theme management.
 */

// Self-executing function to create a module scope
(function() {
  'use strict';

  // Theme constants
  const THEMES = {
    LIGHT: 'light',
    DARK: 'dark',
    HIGH_CONTRAST: 'high-contrast'
  };

  // Local storage key for theme preference
  const THEME_STORAGE_KEY = 'persistenceos-theme-preference';

  // Default theme
  const DEFAULT_THEME = THEMES.LIGHT;

  // Current theme
  let currentTheme = DEFAULT_THEME;

  /**
   * Initialize the theme manager
   * Sets up event listeners and loads saved preferences
   */
  function initialize() {
    // Load saved theme preference
    loadThemePreference();

    // Set up theme toggle buttons
    setupThemeToggleButtons();

    // Apply the current theme
    applyTheme(currentTheme);

    // Listen for system preference changes
    listenForSystemPreferenceChanges();

    console.log('Theme Manager initialized with theme:', currentTheme);
  }

  /**
   * Load the user's theme preference from local storage
   */
  function loadThemePreference() {
    const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
    
    if (savedTheme && Object.values(THEMES).includes(savedTheme)) {
      currentTheme = savedTheme;
    } else {
      // If no saved preference, check system preference
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        currentTheme = THEMES.DARK;
      }
    }
  }

  /**
   * Set up event listeners for theme toggle buttons
   */
  function setupThemeToggleButtons() {
    // Theme toggle in header
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.addEventListener('change', function() {
        toggleTheme();
      });
      
      // Set initial state of toggle
      themeToggle.checked = currentTheme === THEMES.DARK;
    }

    // Theme selector in settings
    const themeSelectors = document.querySelectorAll('.theme-selector');
    themeSelectors.forEach(selector => {
      selector.addEventListener('click', function() {
        const theme = this.dataset.theme;
        if (theme && Object.values(THEMES).includes(theme)) {
          setTheme(theme);
        }
      });
    });
  }

  /**
   * Listen for changes in system color scheme preference
   */
  function listenForSystemPreferenceChanges() {
    if (window.matchMedia) {
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', event => {
        // Only change theme if user hasn't explicitly set a preference
        if (!localStorage.getItem(THEME_STORAGE_KEY)) {
          setTheme(event.matches ? THEMES.DARK : THEMES.LIGHT);
        }
      });
    }
  }

  /**
   * Toggle between light and dark themes
   */
  function toggleTheme() {
    const newTheme = currentTheme === THEMES.DARK ? THEMES.LIGHT : THEMES.DARK;
    setTheme(newTheme);
  }

  /**
   * Set a specific theme
   * @param {string} theme - The theme to set
   */
  function setTheme(theme) {
    if (!Object.values(THEMES).includes(theme)) {
      console.error('Invalid theme:', theme);
      return;
    }

    currentTheme = theme;
    applyTheme(theme);
    saveThemePreference(theme);

    // Update UI elements
    updateThemeToggleState();

    // Dispatch theme change event
    dispatchThemeChangeEvent(theme);
  }

  /**
   * Apply the theme to the document
   * @param {string} theme - The theme to apply
   */
  function applyTheme(theme) {
    // Remove all theme data attributes
    document.documentElement.removeAttribute('data-theme');
    
    // Add the current theme data attribute
    if (theme !== THEMES.LIGHT) {
      document.documentElement.setAttribute('data-theme', theme);
    }
  }

  /**
   * Save the theme preference to local storage
   * @param {string} theme - The theme to save
   */
  function saveThemePreference(theme) {
    localStorage.setItem(THEME_STORAGE_KEY, theme);
  }

  /**
   * Update the state of theme toggle buttons
   */
  function updateThemeToggleState() {
    // Update theme toggle in header
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
      themeToggle.checked = currentTheme === THEMES.DARK;
    }

    // Update theme selectors in settings
    const themeSelectors = document.querySelectorAll('.theme-selector');
    themeSelectors.forEach(selector => {
      const selectorTheme = selector.dataset.theme;
      if (selectorTheme === currentTheme) {
        selector.classList.add('active');
      } else {
        selector.classList.remove('active');
      }
    });
  }

  /**
   * Dispatch a custom event when the theme changes
   * @param {string} theme - The new theme
   */
  function dispatchThemeChangeEvent(theme) {
    const event = new CustomEvent('themechange', {
      detail: { theme: theme }
    });
    document.dispatchEvent(event);
  }

  /**
   * Get the current theme
   * @returns {string} The current theme
   */
  function getCurrentTheme() {
    return currentTheme;
  }

  /**
   * Check if the current theme is dark
   * @returns {boolean} True if the current theme is dark
   */
  function isDarkTheme() {
    return currentTheme === THEMES.DARK;
  }

  /**
   * Check if the current theme is high contrast
   * @returns {boolean} True if the current theme is high contrast
   */
  function isHighContrastTheme() {
    return currentTheme === THEMES.HIGH_CONTRAST;
  }

  // Public API
  window.ThemeManager = {
    initialize: initialize,
    setTheme: setTheme,
    toggleTheme: toggleTheme,
    getCurrentTheme: getCurrentTheme,
    isDarkTheme: isDarkTheme,
    isHighContrastTheme: isHighContrastTheme,
    THEMES: THEMES
  };

  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', initialize);
})();
