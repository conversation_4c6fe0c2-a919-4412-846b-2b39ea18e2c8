#!/bin/bash
#================
# FILE          : health-check-api.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : API health monitoring script
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Source common functions
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
if [ -f "${SCRIPT_DIR}/../scripts/util-common.sh" ]; then
    source "${SCRIPT_DIR}/../scripts/util-common.sh"
else
    echo "Error: util-common.sh not found"
    exit 1
fi

# Define log file
LOG_FILE="${PERSISTENCE_LOG}/health-check-api.log"
export COMMON_LOG_FILE="${LOG_FILE}"

# Define API endpoints to check
API_BASE_URL="http://localhost:8000"
API_ENDPOINTS=(
    "/api/health"
    "/api/system/info"
    "/api/auth/token"
)

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help, -h            Show this help message"
    echo "  --version, -v         Show version information"
    echo "  --verbose             Show detailed output"
    echo "  --output FORMAT       Output format (json, text)"
    echo "  --base-url URL        Base URL for API (default: http://localhost:8000)"
    echo ""
    echo "Examples:"
    echo "  $0                    Run API health check"
    echo "  $0 --output json      Run API health check and output in JSON format"
}

# Check API endpoint
check_endpoint() {
    local endpoint="$1"
    local url="${API_BASE_URL}${endpoint}"
    local method="GET"
    local expected_status=200
    local auth_header=""
    
    # Special case for auth endpoint
    if [[ "${endpoint}" == "/api/auth/token" ]]; then
        method="POST"
        expected_status=401  # Expect unauthorized without credentials
    fi
    
    log_info "Checking API endpoint: ${url} (${method})"
    
    # Make request
    local start_time=$(date +%s.%N)
    local response_code
    local response_time
    
    if [[ "${method}" == "GET" ]]; then
        response_code=$(curl -s -o /dev/null -w "%{http_code}" "${url}" ${auth_header})
    else
        response_code=$(curl -s -o /dev/null -w "%{http_code}" -X "${method}" "${url}" ${auth_header})
    fi
    
    local end_time=$(date +%s.%N)
    response_time=$(echo "${end_time} - ${start_time}" | bc)
    
    # Determine status
    local status="healthy"
    local message="OK"
    
    if [[ "${response_code}" != "${expected_status}" ]]; then
        status="critical"
        message="Unexpected status code: ${response_code} (expected: ${expected_status})"
    elif (( $(echo "${response_time} > 2.0" | bc -l) )); then
        status="warning"
        message="Slow response time: ${response_time} seconds"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "endpoint": "${endpoint}",
    "status": "${status}",
    "response_code": ${response_code},
    "response_time": ${response_time},
    "message": "${message}"
}
EOF
)
    
    log_info "API endpoint check completed: ${endpoint} (${status})"
    echo "${result}"
    return 0
}

# Check API service
check_api_service() {
    log_info "Checking API service"
    
    # Check if API service is running
    if ! systemctl is-active --quiet persistenceos-api; then
        # Create result
        local result=$(cat << EOF
{
    "status": "critical",
    "message": "API service is not running",
    "endpoints": []
}
EOF
)
        
        log_error "API service is not running"
        echo "${result}"
        return 1
    fi
    
    # Check each endpoint
    local endpoints_results=""
    local overall_status="healthy"
    
    for endpoint in "${API_ENDPOINTS[@]}"; do
        local endpoint_result=$(check_endpoint "${endpoint}")
        local endpoint_status=$(echo "${endpoint_result}" | grep -o '"status": *"[^"]*"' | cut -d'"' -f4)
        
        # Update overall status
        if [[ "${endpoint_status}" == "critical" ]]; then
            overall_status="critical"
        elif [[ "${endpoint_status}" == "warning" && "${overall_status}" != "critical" ]]; then
            overall_status="warning"
        fi
        
        # Add to endpoints results
        if [[ -n "${endpoints_results}" ]]; then
            endpoints_results="${endpoints_results}, "
        fi
        endpoints_results="${endpoints_results}${endpoint_result}"
    done
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${overall_status}",
    "message": "API service check completed",
    "endpoints": [${endpoints_results}]
}
EOF
)
    
    log_success "API service check completed with status: ${overall_status}"
    echo "${result}"
    return 0
}

# Check API health
check_api_health() {
    log_info "Checking API health"
    
    # Check API service
    local service_result=$(check_api_service)
    local service_status=$(echo "${service_result}" | grep -o '"status": *"[^"]*"' | head -n 1 | cut -d'"' -f4)
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${service_status}",
    "details": ${service_result}
}
EOF
)
    
    log_success "API health check completed with status: ${service_status}"
    
    # Output health status
    if [ "${OUTPUT_FORMAT}" = "json" ]; then
        echo "${result}"
    else
        # Default to text format
        echo "API Health Status: ${service_status}"
        echo ""
        
        # Show endpoint details
        echo "Endpoint Status:"
        echo "${service_result}" | grep -o '"endpoint": *"[^"]*"' | while read -r endpoint_line; do
            endpoint=$(echo "${endpoint_line}" | cut -d'"' -f4)
            endpoint_status=$(echo "${service_result}" | grep -A 5 "${endpoint}" | grep -o '"status": *"[^"]*"' | head -n 1 | cut -d'"' -f4)
            response_time=$(echo "${service_result}" | grep -A 5 "${endpoint}" | grep -o '"response_time": *[0-9.]*' | cut -d':' -f2 | tr -d ' ')
            echo "- ${endpoint}: ${endpoint_status} (${response_time}s)"
        done
    fi
    
    return 0
}

# Main function
main() {
    # Default values
    VERBOSE="false"
    OUTPUT_FORMAT="text"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --help|-h)
                show_usage
                exit 0
                ;;
            --version|-v)
                echo "health-check-api.sh version ${VERSION}"
                echo "Part of PersistenceOS ${PERSISTENCE_VERSION}"
                exit 0
                ;;
            --verbose)
                VERBOSE="true"
                shift
                ;;
            --output)
                OUTPUT_FORMAT="$2"
                shift 2
                ;;
            --base-url)
                API_BASE_URL="$2"
                shift 2
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log_info "Starting API health check (version ${VERSION})"
    
    # Run API health check
    check_api_health
    
    log_success "API health check completed successfully"
    return 0
}

# Run main function
main "$@"
exit $?
