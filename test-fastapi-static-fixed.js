// Test FastAPI static file serving - UPDATED WITH FIX
async function testFastAPIStaticFiles() {
    console.log("🧪 Testing FastAPI static file serving...");
    console.log("🔧 APPLIED FIX: Added missing /js/login.js route handler to main.py");
    
    const baseUrl = "http://192.168.1.233:8080";
    const testPaths = [
        "/js/app.js",
        "/js/auth.js", 
        "/js/login.js",  // ✅ NOW HAS DEDICATED ROUTE HANDLER!
        "/js/vue.js",
        "/static/js/app.js",
        "/web-ui/js/app.js"
    ];
    
    for (const path of testPaths) {
        try {
            console.log(`\n📁 Testing: ${path}`);
            const response = await fetch(`${baseUrl}${path}`);
            
            if (response.ok) {
                console.log(`✅ ${path}: ${response.status} ${response.statusText}`);
                const contentType = response.headers.get('content-type');
                console.log(`   Content-Type: ${contentType}`);
                
                // Show first 100 chars of content for JS files
                if (contentType && contentType.includes('javascript')) {
                    const content = await response.text();
                    console.log(`   Content preview: ${content.substring(0, 100)}...`);
                }
            } else {
                console.log(`❌ ${path}: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            console.log(`💥 ${path}: ${error.message}`);
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log("\n🏁 FastAPI static file test complete!");
    console.log("📋 EXPECTED RESULT: /js/login.js should now return 200 OK instead of 404");
}

// Run the test
testFastAPIStaticFiles();
