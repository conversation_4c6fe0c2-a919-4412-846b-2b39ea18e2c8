/**
 * PersistenceOS Web UI - Theme Styles
 * 
 * This file contains theme-specific styles for light/dark mode and customization.
 * It supports theme switching and provides theme-specific overrides.
 */

/* Import variables */
@import 'variables.css';

/* Light theme (default) */
:root {
  /* Background colors */
  --body-bg: #f5f7fa;
  --sidebar-bg: #2c3e50;
  --header-bg: #ffffff;
  --main-bg: #f5f7fa;
  --card-bg: #ffffff;
  --modal-bg: #ffffff;
  --dropdown-bg: #ffffff;
  --tooltip-bg: #333333;
  
  /* Text colors */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-muted: #999999;
  --text-light: #ffffff;
  --sidebar-text: #ecf0f1;
  --sidebar-text-active: #ffffff;
  
  /* Border colors */
  --border-color: #e0e0e0;
  --border-color-dark: #cccccc;
  
  /* Status colors */
  --status-success-bg: #e6f7ed;
  --status-warning-bg: #fff8e6;
  --status-error-bg: #fde7e9;
  --status-info-bg: #e6f3ff;
  
  /* Shadow */
  --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  --box-shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Dark theme */
[data-theme="dark"] {
  /* Background colors */
  --body-bg: #1a1a1a;
  --sidebar-bg: #1a1a1a;
  --header-bg: #252525;
  --main-bg: #1a1a1a;
  --card-bg: #252525;
  --modal-bg: #252525;
  --dropdown-bg: #252525;
  --tooltip-bg: #4a4a4a;
  
  /* Text colors */
  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --text-muted: #808080;
  --text-light: #ffffff;
  --sidebar-text: #b0b0b0;
  --sidebar-text-active: #ffffff;
  
  /* Border colors */
  --border-color: #333333;
  --border-color-dark: #444444;
  
  /* Status colors */
  --status-success-bg: rgba(40, 167, 69, 0.2);
  --status-warning-bg: rgba(255, 193, 7, 0.2);
  --status-error-bg: rgba(220, 53, 69, 0.2);
  --status-info-bg: rgba(23, 162, 184, 0.2);
  
  /* Shadow */
  --box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  --box-shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* High contrast theme */
[data-theme="high-contrast"] {
  /* Background colors */
  --body-bg: #000000;
  --sidebar-bg: #000000;
  --header-bg: #000000;
  --main-bg: #000000;
  --card-bg: #000000;
  --modal-bg: #000000;
  --dropdown-bg: #000000;
  --tooltip-bg: #ffffff;
  
  /* Text colors */
  --text-primary: #ffffff;
  --text-secondary: #ffffff;
  --text-muted: #ffffff;
  --text-light: #000000;
  --sidebar-text: #ffffff;
  --sidebar-text-active: #ffff00;
  
  /* Border colors */
  --border-color: #ffffff;
  --border-color-dark: #ffffff;
  
  /* Status colors */
  --status-success-bg: #00ff00;
  --status-warning-bg: #ffff00;
  --status-error-bg: #ff0000;
  --status-info-bg: #00ffff;
  
  /* Shadow */
  --box-shadow: none;
  --box-shadow-hover: none;
}

/* Theme-specific component styles */

/* Cards */
.card {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  box-shadow: var(--box-shadow);
}

/* Sidebar */
.sidebar {
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
}

.sidebar-item {
  color: var(--sidebar-text);
}

.sidebar-item.active, 
.sidebar-item:hover {
  color: var(--sidebar-text-active);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Header */
.header {
  background-color: var(--header-bg);
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
}

/* Main content */
.main-content {
  background-color: var(--main-bg);
  color: var(--text-primary);
}

/* Buttons */
.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-light);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-light);
}

.btn-outline {
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

/* Form elements */
input, select, textarea {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

/* Tables */
table {
  background-color: var(--card-bg);
  color: var(--text-primary);
  border-color: var(--border-color);
}

th {
  background-color: var(--border-color);
}

tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.03);
}

[data-theme="dark"] tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.03);
}

/* Status indicators */
.status-success {
  background-color: var(--status-success-bg);
  color: var(--success-color);
}

.status-warning {
  background-color: var(--status-warning-bg);
  color: var(--warning-color);
}

.status-error {
  background-color: var(--status-error-bg);
  color: var(--error-color);
}

.status-info {
  background-color: var(--status-info-bg);
  color: var(--info-color);
}

/* Theme switcher */
.theme-switcher {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.theme-switcher-label {
  margin-right: var(--spacing-sm);
  color: var(--text-secondary);
}

.theme-switcher-toggle {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
}

.theme-switcher-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.theme-switcher-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color);
  transition: .4s;
  border-radius: 30px;
}

.theme-switcher-slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: var(--card-bg);
  transition: .4s;
  border-radius: 50%;
}

input:checked + .theme-switcher-slider {
  background-color: var(--primary-color);
}

input:checked + .theme-switcher-slider:before {
  transform: translateX(30px);
}
