/**
 * FILE          : loading-indicators.js
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : Loading state management
 */

/**
 * Loading indicators module for PersistenceOS
 * Manages loading states, spinners, and progress indicators
 */
const LoadingIndicator = (function() {
    // Track active loading overlays
    const activeOverlays = new Map();
    let overlayCounter = 0;
    
    // Default options
    const defaultOptions = {
        text: 'Loading...',
        subText: 'Please wait',
        spinnerType: 'border', // 'border' or 'grow'
        spinnerSize: 'lg',     // 'sm', 'md', 'lg'
        spinnerColor: 'primary',
        showOverlay: true,
        fullscreen: true,
        cancelable: false,
        onCancel: null
    };
    
    /**
     * Shows a loading overlay
     * @param {Object} options - Loading overlay options
     * @returns {number} - The overlay ID for later reference
     */
    function showLoading(options = {}) {
        // Merge options with defaults
        const config = { ...defaultOptions, ...options };
        
        // Create overlay container if fullscreen
        let overlayContainer;
        if (config.fullscreen) {
            overlayContainer = document.createElement('div');
            overlayContainer.className = 'loading-overlay';
            document.body.appendChild(overlayContainer);
        } else if (config.container) {
            // Use specified container
            overlayContainer = config.container;
            
            // Add relative positioning if not already set
            const containerStyle = window.getComputedStyle(overlayContainer);
            if (containerStyle.position === 'static') {
                overlayContainer.style.position = 'relative';
            }
            
            // Create and append overlay
            const elementOverlay = document.createElement('div');
            elementOverlay.className = 'element-loading';
            overlayContainer.appendChild(elementOverlay);
            overlayContainer = elementOverlay;
        } else {
            // No container specified, use body as fallback
            overlayContainer = document.createElement('div');
            overlayContainer.className = 'loading-overlay';
            document.body.appendChild(overlayContainer);
        }
        
        // Create loading content
        const loadingContent = document.createElement('div');
        loadingContent.className = 'loading-content';
        
        // Create spinner
        const spinner = document.createElement('div');
        spinner.className = `spinner${config.spinnerType === 'grow' ? '-grow' : ''} spinner${config.spinnerType === 'grow' ? '-grow' : ''}-${config.spinnerSize} spinner${config.spinnerType === 'grow' ? '-grow' : ''}-${config.spinnerColor} loading-spinner`;
        loadingContent.appendChild(spinner);
        
        // Add loading text
        if (config.text) {
            const loadingText = document.createElement('div');
            loadingText.className = 'loading-text';
            loadingText.textContent = config.text;
            loadingContent.appendChild(loadingText);
        }
        
        // Add subtext
        if (config.subText) {
            const loadingSubText = document.createElement('div');
            loadingSubText.className = 'loading-subtext';
            loadingSubText.textContent = config.subText;
            loadingContent.appendChild(loadingSubText);
        }
        
        // Add cancel button if cancelable
        if (config.cancelable && typeof config.onCancel === 'function') {
            const cancelButton = document.createElement('button');
            cancelButton.className = 'btn btn-sm btn-secondary mt-3';
            cancelButton.textContent = 'Cancel';
            cancelButton.addEventListener('click', () => {
                config.onCancel();
                hideLoading(overlayId);
            });
            loadingContent.appendChild(cancelButton);
        }
        
        // Add content to overlay
        overlayContainer.appendChild(loadingContent);
        
        // Show overlay with animation
        setTimeout(() => {
            overlayContainer.classList.add('active');
        }, 10);
        
        // Generate and store overlay ID
        const overlayId = ++overlayCounter;
        activeOverlays.set(overlayId, {
            element: overlayContainer,
            config
        });
        
        return overlayId;
    }
    
    /**
     * Hides a loading overlay by ID
     * @param {number} overlayId - The overlay ID to hide
     * @returns {boolean} - True if overlay was found and hidden, false otherwise
     */
    function hideLoading(overlayId) {
        if (!activeOverlays.has(overlayId)) {
            return false;
        }
        
        const { element, config } = activeOverlays.get(overlayId);
        
        // Remove active class to trigger fade out animation
        element.classList.remove('active');
        
        // Remove from DOM after animation completes
        setTimeout(() => {
            if (element.parentNode) {
                // If it's a full overlay, remove it completely
                if (config.fullscreen || !config.container) {
                    element.parentNode.removeChild(element);
                } else {
                    // If it's an element overlay, just remove the loading state
                    element.classList.remove('element-loading');
                    
                    // Remove any loading content
                    const loadingContent = element.querySelector('.loading-content');
                    if (loadingContent) {
                        element.removeChild(loadingContent);
                    }
                }
            }
            
            // Remove from active overlays
            activeOverlays.delete(overlayId);
        }, 300);
        
        return true;
    }
    
    /**
     * Hides all active loading overlays
     */
    function hideAllLoading() {
        activeOverlays.forEach((_, overlayId) => {
            hideLoading(overlayId);
        });
    }
    
    /**
     * Sets a button to loading state
     * @param {HTMLButtonElement} button - The button element
     * @param {boolean} isLoading - Whether the button is in loading state
     */
    function setButtonLoading(button, isLoading = true) {
        if (!button) return;
        
        if (isLoading) {
            // Store original text
            button.dataset.originalText = button.textContent;
            
            // Add loading class
            button.classList.add('loading');
            
            // Disable button
            button.disabled = true;
        } else {
            // Remove loading class
            button.classList.remove('loading');
            
            // Restore original text
            if (button.dataset.originalText) {
                button.textContent = button.dataset.originalText;
                delete button.dataset.originalText;
            }
            
            // Enable button
            button.disabled = false;
        }
    }
    
    /**
     * Creates and updates a progress bar
     * @param {HTMLElement} container - The container element
     * @param {number} percent - The progress percentage (0-100)
     * @param {Object} options - Progress bar options
     * @returns {HTMLElement} - The progress bar element
     */
    function updateProgress(container, percent, options = {}) {
        // Default options
        const config = {
            color: 'primary',
            height: null,
            showText: true,
            striped: false,
            animated: false,
            ...options
        };
        
        // Find existing progress bar or create new one
        let progressContainer = container.querySelector('.progress');
        let progressBar;
        
        if (!progressContainer) {
            // Create progress container
            progressContainer = document.createElement('div');
            progressContainer.className = 'progress';
            
            // Set custom height if provided
            if (config.height) {
                progressContainer.style.height = config.height;
            }
            
            // Create progress bar
            progressBar = document.createElement('div');
            progressBar.className = `progress-bar progress-bar-${config.color}`;
            
            // Add striped class if needed
            if (config.striped) {
                progressBar.classList.add('progress-bar-striped');
            }
            
            // Add animated class if needed
            if (config.animated) {
                progressBar.classList.add('progress-bar-animated');
            }
            
            // Add to container
            progressContainer.appendChild(progressBar);
            container.appendChild(progressContainer);
        } else {
            // Use existing progress bar
            progressBar = progressContainer.querySelector('.progress-bar');
            
            // Update classes
            progressBar.className = `progress-bar progress-bar-${config.color}`;
            
            if (config.striped) {
                progressBar.classList.add('progress-bar-striped');
            }
            
            if (config.animated) {
                progressBar.classList.add('progress-bar-animated');
            }
        }
        
        // Ensure percent is between 0 and 100
        const validPercent = Math.max(0, Math.min(100, percent));
        
        // Update progress bar width
        progressBar.style.width = `${validPercent}%`;
        
        // Update text if needed
        if (config.showText) {
            progressBar.textContent = `${Math.round(validPercent)}%`;
        } else {
            progressBar.textContent = '';
        }
        
        return progressContainer;
    }
    
    /**
     * Creates skeleton loading placeholders
     * @param {HTMLElement} container - The container element
     * @param {string} type - The skeleton type (text, avatar, button, card)
     * @param {number} count - Number of skeleton items to create
     * @param {Object} options - Skeleton options
     */
    function createSkeletonLoading(container, type = 'text', count = 1, options = {}) {
        // Clear container
        container.innerHTML = '';
        
        // Default options
        const config = {
            width: null,
            height: null,
            className: '',
            ...options
        };
        
        // Create skeleton items
        for (let i = 0; i < count; i++) {
            const skeleton = document.createElement('div');
            skeleton.className = `skeleton skeleton-${type} ${config.className}`;
            
            // Set custom dimensions if provided
            if (config.width) {
                skeleton.style.width = config.width;
            }
            
            if (config.height) {
                skeleton.style.height = config.height;
            }
            
            container.appendChild(skeleton);
        }
    }
    
    /**
     * Removes skeleton loading placeholders
     * @param {HTMLElement} container - The container element
     */
    function removeSkeletonLoading(container) {
        const skeletons = container.querySelectorAll('.skeleton');
        skeletons.forEach(skeleton => {
            skeleton.parentNode.removeChild(skeleton);
        });
    }
    
    // Public API
    return {
        showLoading,
        hideLoading,
        hideAllLoading,
        setButtonLoading,
        updateProgress,
        createSkeletonLoading,
        removeSkeletonLoading
    };
})();

// Export for use in other modules
window.LoadingIndicator = LoadingIndicator;
