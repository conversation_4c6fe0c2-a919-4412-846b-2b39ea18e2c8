<?xml version="1.0" encoding="utf-8"?>
<image schemaversion="8.3" name="PersistenceOS" displayname="PersistenceOS">
  <description type="system">
    <author>PersistenceOS Team</author>
    <contact><EMAIL></contact>
    <specification>PersistenceOS Installation Image (MicroOS 6.1 Base)</specification>
  </description>

  <preferences>
    <version>6.1.0</version>
    <packagemanager>zypper</packagemanager>
    <locale>en_US</locale>
    <keytable>us</keytable>
    <timezone>UTC</timezone>
    <rpm-check-signatures>false</rpm-check-signatures>
    <rpm-excludedocs>true</rpm-excludedocs>
    <type image="oem" filesystem="btrfs" firmware="uefi" installiso="true" bootpartition="false" btrfs_root_is_snapper_snapshot="false">
        <bootloader name="grub2" console="serial" timeout="10"/>
        <size unit="G">8</size>
    </type>
  </preferences>

  <profiles>
    <profile name="PersistenceOS" description="PersistenceOS Image Profile" arch="x86_64"/>
  </profiles>

  <users>
    <user password="$1$wYJUgpM5$RXMMeASDc035eX.NbYWFl0" home="/root" name="root" groups="root"/>
  </users>

  <repository type="rpm-md">
    <source path="obsrepositories:/"/>
  </repository>

  <packages type="image">
    <!-- Base system packages -->
    <package name="patterns-microos-base"/>
    <package name="patterns-microos-defaults"/>
    <package name="systemd"/>
    <package name="dracut"/>
    <package name="grub2"/>
    <package name="kernel-default"/>
    <package name="openssh"/>
    <package name="firewalld"/>
    <package name="NetworkManager"/>

    <!-- Health checker packages -->
    <package name="health-checker"/>
    <package name="health-checker-plugins-MicroOS"/>

    <!-- Filesystem-related packages -->
    <package name="btrfsprogs"/>
    <package name="e2fsprogs"/>
    <package name="xfsprogs"/>
    <package name="lvm2"/>

    <!-- Virtualization packages -->
    <package name="libvirt-daemon"/>
    <package name="libvirt-client"/>
    <package name="qemu-kvm"/>
    <package name="qemu-tools"/>

    <!-- Web UI packages -->
    <package name="python3-base"/>
    <!-- Python & API Runtime - FastAPI/Uvicorn serves both static files and API endpoints directly -->
    <package name="python311-base"/>
    <package name="python311-pip"/> <!-- Ensure pip is available for package installation -->
    <!-- Try RPM packages first, fallback to pip installation in config.sh -->
    <package name="python311-fastapi"/>
    <package name="python311-uvicorn"/>
    <package name="python311-psutil"/>
    <package name="python311-pydantic"/>
    <package name="python311-typing-extensions"/>
    <package name="python311-websockets"/> <!-- for WebSocket support -->
    <package name="python311-aiofiles"/> <!-- for async file operations -->
    <package name="python311-python-multipart"/> <!-- for form data parsing -->
    <package name="python311-starlette"/> <!-- Core dependency for FastAPI -->
    <package name="python311-httptools"/> <!-- For better HTTP parsing performance -->
    <package name="python311-ujson"/> <!-- For faster JSON handling -->
    <package name="python311-netifaces"/>

    <!-- Security packages -->
    <package name="policycoreutils"/>
    <package name="selinux-policy"/>
    <package name="openssl"/> <!-- For SSL certificate generation -->
    <package name="pam_pwquality"/>

    <!-- Utilities -->
    <package name="vim"/>
    <package name="less"/>
    <package name="tar"/>
    <package name="gzip"/>
    <package name="zip"/>
    <package name="unzip"/>
    <package name="wget"/>
    <package name="curl"/>
    <package name="rsync"/>
    <package name="sudo"/>
    <package name="timezone"/>
    <package name="bash-completion"/>
    <package name="dracut-kiwi-oem-repart"/>
    <package name="dracut-kiwi-oem-dump"/>
    <!-- Ensure these tools are available -->
    <package name="util-linux"/> <!-- for lscpu, uptime -->
    <package name="hostname"/> <!-- replacement for inetutils -->
    <package name="procps"/> <!-- for free, ps commands -->
    <package name="jq"/> <!-- for JSON processing -->
    <package name="figlet"/> <!-- for enhanced welcome screen -->
    <package name="iproute2"/> <!-- for ip command -->
    <package name="iputils"/> <!-- for ping command -->

    <!-- Additional Virtualization packages -->
    <package name="libvirt-daemon-qemu"/>
    <package name="dnsmasq"/>
    <package name="iproute2"/> <!-- replacement for bridge-utils -->

    <!-- Additional Storage packages -->
    <package name="snapper"/>
    <package name="smartmontools"/>
    <package name="mdadm"/>
    <!-- Hardware detection tools -->
    <package name="pciutils"/>

    <!-- Add this line -->
    <package name="python3-Jinja2"/>

    <!-- KIWI Overlay Archive - Contains all web UI files -->
    <archive name="root.tar.gz"/>
  </packages>

  <packages type="bootstrap">
    <package name="filesystem"/>
    <package name="glibc-locale"/>
    <package name="ca-certificates"/>
  </packages>

  <!-- Configuration script execution -->
  <config>
    <script name="config.sh"/>
  </config>
</image>

