// Virtualization Component
app.component('Virtualization', {
    props: {
        vms: Array
    },
    emits: ['refresh-data'],
    data() {
        return {
            isLoading: false,
            searchTerm: '',
            statusFilter: 'all'
        };
    },
    computed: {
        filteredVMs() {
            return this.vms.filter(vm => {
                const matchesSearch = vm.name.toLowerCase().includes(this.searchTerm.toLowerCase());
                const matchesStatus = this.statusFilter === 'all' || vm.status === this.statusFilter;
                return matchesSearch && matchesStatus;
            });
        }
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');
            
            // Reset loading state after animation
            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        
        vmAction(action, vmId) {
            console.log(`VM action: ${action} on VM ${vmId}`);
            // In a real implementation, this would call an API
            
            // For demo purposes, handle some actions locally
            if (action === 'start' || action === 'stop') {
                const vmIndex = this.vms.findIndex(vm => vm.id === vmId);
                if (vmIndex !== -1) {
                    // Create a copy of the VMs array to maintain reactivity
                    const updatedVMs = [...this.vms];
                    updatedVMs[vmIndex] = {
                        ...updatedVMs[vmIndex],
                        status: action === 'start' ? 'running' : 'stopped',
                        metrics: action === 'start' 
                            ? { cpu: 10, memory: 20 } 
                            : { cpu: 0, memory: 0 }
                    };
                    
                    // In a real implementation, we would update via the parent component
                    // For now, just trigger a refresh
                    setTimeout(() => {
                        this.refreshData();
                    }, 500);
                }
            }
        }
    },
    template: `
        <div id="vms-section" class="content-section">
            <div class="section-header">
                <h2>Virtual Machines</h2>
                <div class="section-actions">
                    <button class="btn btn-primary" id="create-vm-btn">
                        <i class="fas fa-plus"></i> Create VM
                    </button>
                    <button class="btn btn-primary" id="import-vm-btn">
                        <i class="fas fa-file-import"></i> Import VM
                    </button>
                    <button class="btn btn-secondary" id="refresh-vms-btn" @click="refreshData"
                        :class="{'spinning': isLoading}">
                        <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i> Refresh
                    </button>
                </div>
            </div>

            <div class="vm-filters">
                <div class="search-container">
                    <input type="text" id="vm-search" v-model="searchTerm" placeholder="Search VMs...">
                    <i class="fas fa-search"></i>
                </div>
                <div class="filter-options">
                    <select id="vm-status-filter" v-model="statusFilter">
                        <option value="all">All Status</option>
                        <option value="running">Running</option>
                        <option value="stopped">Stopped</option>
                    </select>
                </div>
            </div>

            <div class="vm-list">
                <!-- VM Card for each VM -->
                <div v-for="vm in filteredVMs" :key="vm.id" class="vm-card" :class="vm.status">
                    <div class="vm-header">
                        <div class="vm-name">{{ vm.name }}</div>
                        <div class="vm-status">
                            <div class="status-indicator" :class="{ 'healthy': vm.status === 'running' }"></div>
                            {{ vm.status.charAt(0).toUpperCase() + vm.status.slice(1) }}
                        </div>
                    </div>
                    <div class="vm-body">
                        <div class="vm-specs">
                            <div class="spec-item">
                                <i class="fas fa-microchip"></i>
                                <span>{{ vm.specs.cpu }} vCPU{{ vm.specs.cpu > 1 ? 's' : '' }}</span>
                            </div>
                            <div class="spec-item">
                                <i class="fas fa-memory"></i>
                                <span>{{ vm.specs.memory }} GB</span>
                            </div>
                            <div class="spec-item">
                                <i class="fas fa-hdd"></i>
                                <span>{{ vm.specs.storage }} GB</span>
                            </div>
                        </div>
                        <div class="vm-metrics">
                            <div class="metric">
                                <div class="metric-label">CPU</div>
                                <div class="progress-bar">
                                    <div class="progress" :style="{ width: vm.metrics.cpu + '%' }"></div>
                                </div>
                                <div class="metric-value">{{ vm.metrics.cpu }}%</div>
                            </div>
                            <div class="metric">
                                <div class="metric-label">Memory</div>
                                <div class="progress-bar">
                                    <div class="progress" :style="{ width: vm.metrics.memory + '%' }"></div>
                                </div>
                                <div class="metric-value">{{ vm.metrics.memory }}%</div>
                            </div>
                        </div>
                    </div>
                    <div class="vm-actions">
                        <!-- Show appropriate actions based on VM status -->
                        <button v-if="vm.status === 'stopped'" class="vm-action-btn" @click="vmAction('start', vm.id)" title="Start">
                            <i class="fas fa-play"></i>
                        </button>
                        <button v-if="vm.status === 'running'" class="vm-action-btn" @click="vmAction('stop', vm.id)" title="Stop">
                            <i class="fas fa-stop"></i>
                        </button>
                        <button v-if="vm.status === 'running'" class="vm-action-btn" @click="vmAction('restart', vm.id)" title="Restart">
                            <i class="fas fa-redo"></i>
                        </button>
                        <button class="vm-action-btn" @click="vmAction('console', vm.id)" :disabled="vm.status !== 'running'" title="Console">
                            <i class="fas fa-terminal"></i>
                        </button>
                        <button class="vm-action-btn" @click="vmAction('edit', vm.id)" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="vm-action-btn" @click="vmAction('snapshot', vm.id)" title="Snapshot">
                            <i class="fas fa-camera"></i>
                        </button>
                        <button class="vm-action-btn" @click="vmAction('delete', vm.id)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Show message if no VMs match filters -->
                <div v-if="filteredVMs.length === 0" class="no-results">
                    <p>No virtual machines found matching your filters.</p>
                </div>
            </div>
        </div>
    `
}); 