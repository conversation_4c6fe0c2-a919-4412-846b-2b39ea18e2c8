# PersistenceOS File Flow and Interactions

This document outlines how the various files in the PersistenceOS project interact with each other, from the initial build process through installation and runtime operation.

## File Interaction Matrix

| File | Build Stage | Installation Stage | Runtime Stage | Interacts With | Purpose |
|------|-------------|-------------------|--------------|----------------|---------|
| **PersistenceOS.kiwi** | ✅ | ❌ | ❌ | _constraints, PersistenceOS.spec | Defines the image build process, packages, and repositories |
| **PersistenceOS.spec** | ✅ | ❌ | ❌ | _constraints, _service | RPM spec file defining package build, dependencies, and installation |
| **_constraints** | ✅ | ❌ | ❌ | PersistenceOS.kiwi | Defines build requirements for OBS (memory, disk space) |
| **_service** | ✅ | ❌ | ❌ | PersistenceOS.spec | OBS service configuration for pre-build actions |
| **scripts/config.sh** | ✅ | ✅ | ❌ | main.py, login.js, app.js, persistenceos-api.service | Central configuration script that sets up the entire system structure |
| **usr/lib/persistence/api/main.py** | ❌ | ❌ | ✅ | run_api.sh, auth.py, system.py, login.js, app.js | Main FastAPI application handling API requests and serving the web UI |
| **usr/lib/persistence/api/run_api.sh** | ❌ | ❌ | ✅ | main.py, persistenceos-api.service | Script that launches the FastAPI application using Uvicorn |
| **usr/lib/persistence/api/app.py** | ❌ | ❌ | ✅ | main.py | Simplified or alternative API entry point |
| **usr/lib/persistence/api/__init__.py** | ❌ | ❌ | ✅ | main.py, app.py | Python package initialization for the API module |
| **python/persistenceos/utils/system.py** | ❌ | ❌ | ✅ | main.py | Utility module for system-level operations and information gathering |
| **python/persistenceos/utils/auth.py** | ❌ | ❌ | ✅ | main.py | Authentication utility module for backend authentication |
| **services/persistenceos-api.service** | ❌ | ✅ | ✅ | run_api.sh, main.py | Systemd service definition for the FastAPI backend |
| **services/persistenceos-core-services.service** | ❌ | ✅ | ✅ | system.py | Systemd service definition for core system services |
| **usr/lib/persistence/web-ui/js/app.js** | ❌ | ❌ | ✅ | main.py, auth.js | Main Vue.js application implementing the dashboard |
| **usr/lib/persistence/web-ui/login/js/login.js** | ❌ | ❌ | ✅ | main.py, auth.js | Handles user authentication on the frontend |
| **usr/lib/persistence/web-ui/static/css/style.css** | ❌ | ❌ | ✅ | app.js, login.js | Main CSS stylesheet for the web UI |
| **usr/lib/persistence/web-ui/404.html** | ❌ | ❌ | ✅ | main.py | Custom 404 error page served by FastAPI |
| **usr/lib/persistence/web-ui/50x.html** | ❌ | ❌ | ✅ | main.py | Custom 50x error page served by FastAPI |
| **usr/lib/persistence/web-ui/js/auth.js** | ❌ | ❌ | ✅ | login.js, app.js | Frontend authentication utility for token management |

## Process Flow

### 1. Build Process Flow

```
PersistenceOS.kiwi → _constraints → PersistenceOS.spec → _service → scripts/config.sh
```

1. **PersistenceOS.kiwi** defines the base image and packages
2. **_constraints** sets build resource requirements
3. **PersistenceOS.spec** controls package building
4. **_service** performs pre-build actions
5. **scripts/config.sh** runs during build to set up the system structure

### 2. Installation Process Flow

```
scripts/config.sh → services/persistenceos-api.service → services/persistenceos-core-services.service
```

1. **scripts/config.sh** creates directory structure and deploys files
2. **services/persistenceos-api.service** is installed for the API backend
3. **services/persistenceos-core-services.service** is installed for core services

### 3. Runtime Process Flow

#### 3.1 Backend Startup Flow

```
services/persistenceos-api.service → usr/lib/persistence/api/run_api.sh → usr/lib/persistence/api/main.py
```

1. **services/persistenceos-api.service** starts at boot
2. **usr/lib/persistence/api/run_api.sh** is executed to launch the API
3. **usr/lib/persistence/api/main.py** runs as the main FastAPI application

#### 3.2 Authentication Flow

```
usr/lib/persistence/web-ui/login/js/login.js → usr/lib/persistence/api/main.py → python/persistenceos/utils/auth.py → usr/lib/persistence/web-ui/js/auth.js → usr/lib/persistence/web-ui/js/app.js
```

1. **login.js** handles login form submission
2. **main.py** processes authentication request
3. **auth.py** validates credentials on the backend
4. **auth.js** manages token storage and refresh
5. **app.js** uses authentication for API requests

#### 3.3 Web UI Flow

```
usr/lib/persistence/api/main.py → usr/lib/persistence/web-ui/login/js/login.js → usr/lib/persistence/web-ui/js/app.js
```

1. **main.py** serves static files and handles API requests
2. **login.js** manages the login process
3. **app.js** renders the Vue.js dashboard after authentication

#### 3.4 System Management Flow

```
usr/lib/persistence/web-ui/js/app.js → usr/lib/persistence/api/main.py → python/persistenceos/utils/system.py
```

1. **app.js** makes API requests for system operations
2. **main.py** processes these requests
3. **system.py** performs the actual system operations

## Key File Dependencies

### scripts/config.sh Dependencies
- Creates structure used by all runtime files
- Deploys login.js, app.js, and other web UI files
- Configures services that will run main.py

### main.py Dependencies
- Depends on system.py for system operations
- Depends on auth.py for authentication logic
- Serves login.js and app.js to clients
- Processes API requests from app.js

### login.js Dependencies
- Communicates with main.py for authentication
- Uses auth.js for token management
- Redirects to app.js after successful login

### app.js Dependencies
- Makes API requests to main.py
- Uses auth.js for authentication state
- Depends on style.css for UI appearance

## Critical Path for Authentication Fix

For the "Your session has expired" issue, the critical path is:

```
login.js → main.py → auth.js
```

These three files work together to manage authentication state, token validation, and session expiry handling.
