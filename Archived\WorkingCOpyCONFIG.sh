#!/bin/bash
#================
# FILE          : PersistenceOSconfig.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PURPOSE       : Clean configuration script for installation and post-installation setup
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Define paths
PERSISTENCE_ROOT="/usr/lib/persistence"
PERSISTENCE_SCRIPTS="${PERSISTENCE_ROOT}/scripts"
PERSISTENCE_SERVICES="${PERSISTENCE_ROOT}/services"
PERSISTENCE_BIN="${PERSISTENCE_ROOT}/bin"
PERSISTENCE_TOOLS="${PERSISTENCE_ROOT}/tools"
PERSISTENCE_VAR="/var/lib/persistence"
PERSISTENCE_LOG="/var/log/persistence"
PERSISTENCE_CONFIG="${PERSISTENCE_VAR}/config"
NGINX_CONF_DIR="/etc/nginx"
PERSISTENCE_WEB_PORT="8080"
PERSISTENCE_WEB_SSL_PORT="8443"

# Logging functions - simplified
log_info() {
    echo "[INFO] $1"
}

log_success() {
    echo "[SUCCESS] $1"
}

log_warning() {
    echo "[WARNING] $1"
}

log_error() {
    echo "[ERROR] $1"
}

# Ensure directory exists
ensure_directory() {
    local dir="$1"
    if [ ! -d "${dir}" ]; then
        mkdir -p "${dir}"
        log_info "Created directory: ${dir}"
    fi
}

# Set permissions on scripts and directories
set_permissions() {
    log_info "Setting permissions on scripts and directories"
    find "${PERSISTENCE_SCRIPTS}" -type f -name "*.sh" -exec chmod 755 {} \; 2>/dev/null || true
    find "${PERSISTENCE_TOOLS}" -type f -exec chmod 755 {} \; 2>/dev/null || true
    find "${PERSISTENCE_BIN}" -type f -exec chmod 755 {} \; 2>/dev/null || true
    chmod 755 "${PERSISTENCE_ROOT}" 2>/dev/null || true
    log_success "Permissions set successfully"
}

# Improved network detection - focus on the most reliable methods and ens3 interface
get_network_ips() {
    local quiet=${1:-false}
    
    if [ "$quiet" != "true" ]; then
        log_info "Detecting network interfaces and IPs"
    fi
    
    local found_ips=()
    local all_interfaces=()
    local ip_status_list=""
    
    # Get all interfaces (UP or DOWN)
    if command -v ip &>/dev/null; then
        mapfile -t all_interfaces < <(ip -br link show | grep -v "lo:" | awk '{print $1}')
        
        # First check for ens3 interface explicitly (common in VMs)
        if ip -br link show dev ens3 &>/dev/null; then
            local state=$(ip -br link show dev ens3 2>/dev/null | awk '{print $2}')
            local ip_addrs=$(ip -4 addr show dev ens3 2>/dev/null | grep -oP 'inet \K[\d.]+' || echo "")
            
            # If ens3 is UP and has an IP, use it as primary
            if [[ "$state" == *"UP"* ]] && [[ -n "$ip_addrs" ]] && [[ "$ip_addrs" != "127."* ]]; then
                found_ips+=("$ip_addrs")
                ip_status_list+="Interface: ens3 - State: $state - IP: $ip_addrs (primary)"$'\n'
            fi
        fi
        
        # Process all interfaces if ens3 wasn't found or didn't have an IP
        if [ ${#found_ips[@]} -eq 0 ]; then
            # Process each interface
            for interface in "${all_interfaces[@]}"; do
                local state=$(ip -br link show dev "$interface" 2>/dev/null | awk '{print $2}')
                local ip_addrs=$(ip -4 addr show dev "$interface" 2>/dev/null | grep -oP 'inet \K[\d.]+' || echo "")
                
                # Build status entry
                local status_entry="Interface: $interface - State: $state"
                if [ -n "$ip_addrs" ]; then
                    status_entry+=" - IP: $ip_addrs"
                    # If interface is UP and has a non-loopback IP, add to found_ips
                    if [[ "$state" == *"UP"* ]] && [[ "$ip_addrs" != "127."* ]]; then
                        found_ips+=("$ip_addrs")
                    fi
                } else 
                    status_entry+=" - No IP assigned"
                fi
                ip_status_list+="$status_entry"$'\n'
            done
        fi
    fi
    
    # Method 2: Try hostname as fallback for usable IPs
    if [ ${#found_ips[@]} -eq 0 ] && command -v hostname &>/dev/null; then
        local all_ips=$(hostname -I 2>/dev/null || echo "")
        for ip in $all_ips; do
            if [[ "$ip" != "127."* ]] && [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                found_ips+=("$ip")
                break  # Just take the first valid IP
            fi
        done
    fi
    
    # Last resort - use loopback
    if [ ${#found_ips[@]} -eq 0 ]; then
        found_ips+=("127.0.0.1")
    fi
    
    # Save the detected IP to a configuration file
    ensure_directory "${PERSISTENCE_CONFIG}"
    echo "${found_ips[0]}" > "${PERSISTENCE_CONFIG}/web_ip.conf"
    
    # Save full interface status for display in MOTD
    echo "$ip_status_list" > "${PERSISTENCE_CONFIG}/network_status.conf"
    
    # Return the first IP
    echo "${found_ips[0]}"
}

# Generate simple ASCII logo
generate_ascii_logo() {
    cat > "$1" << 'EOF'
 ____               _     _                      ___  ____  
|  _ \ ___ _ __ ___(_)___| |_ ___ _ __   ___ ___|_ _|/ ___| 
| |_) / _ \ '__/ __| / __| __/ _ \ '_ \ / __/ _ \| | \___ \ 
|  __/  __/ |  \__ \ \__ \ ||  __/ | | | (_|  __/| |  ___) |
|_|   \___|_|  |___/_|___/\__\___|_| |_|\___\___|___||____/ 
EOF
}

# Get enhanced system info
get_system_info() {
    # Get hostname with fallback
    local hostname=$(hostname 2>/dev/null || echo "PersistenceOS")
    
    # Get kernel with fallback
    local kernel=$(uname -r 2>/dev/null || echo "Unknown")
    
    # Get uptime with fallback
    local uptime=$(uptime -p 2>/dev/null | sed 's/up //' || echo "Unknown")
    
    # Get CPU info with fallback
    local cpu=$(lscpu 2>/dev/null | grep 'Model name' | cut -d':' -f2 | xargs || echo "Unknown")
    
    # Get memory info with fallback
    local memory=$(free -h 2>/dev/null | awk '/Mem:/ {print $3 "/" $2}' || echo "Unknown")
    
    # Get storage info with fallback
    local storage=$(df -h / 2>/dev/null | awk 'NR==2 {print $3 "/" $2}' || echo "Unknown")
    
    echo "Hostname: ${hostname}"
    echo "Kernel: ${kernel}"
    echo "Uptime: ${uptime}"
    echo "CPU: ${cpu}"
    echo "Memory: ${memory}"
    echo "Storage: ${storage}"
}

# Update MOTD with logo and web UI IP - enhanced with more system info
update_motd() {
    log_info "Updating MOTD with welcome screen"
    # Get the IP first, then use it in the MOTD - don't run get_network_ips inline with echo
    local web_ip=$(get_network_ips "true")
    local motd_file="/etc/motd"
    local tmp_motd=$(mktemp)

    # Generate ASCII logo
    generate_ascii_logo "$tmp_motd"

    # Add version and system info
    echo -e "\nPersistenceOS - Advanced Storage Management and Snapshot Functionality" >> "$tmp_motd"
    echo -e "\nSystem Information:" >> "$tmp_motd"
    get_system_info >> "$tmp_motd"
    
    # Add network interfaces status
    echo -e "\nNetwork Interfaces:" >> "$tmp_motd"
    if [ -f "${PERSISTENCE_CONFIG}/network_status.conf" ]; then
        cat "${PERSISTENCE_CONFIG}/network_status.conf" >> "$tmp_motd"
    else
        # Fallback to direct command output if config file isn't available
        local interfaces
        if command -v ip &>/dev/null; then
            interfaces=$(ip -br link show | grep -v "lo:")
            for iface in $(echo "$interfaces" | awk '{print $1}'); do
                local state=$(ip -br link show dev "$iface" 2>/dev/null | awk '{print $2}')
                local ip_addr=$(ip -4 addr show dev "$iface" 2>/dev/null | grep -oP 'inet \K[\d.]+' || echo "No IP")
                echo "Interface: ${iface} - State: ${state} - IP: ${ip_addr}" >> "$tmp_motd"
            done
        else
            echo "No network information available" >> "$tmp_motd"
        fi
    fi
    
    # Add web UI access info - Use the real IP instead of just localhost
    echo -e "\nWEB INTERFACE ACCESS:" >> "$tmp_motd"
    
    # If we have a real IP (not localhost), use it in the web interface access URLs
    if [ -n "$web_ip" ] && [ "$web_ip" != "127.0.0.1" ]; then
        echo "HTTP:  http://${web_ip}:8080" >> "$tmp_motd"
        echo "HTTPS: https://${web_ip}:8443 (secure connection)" >> "$tmp_motd"
    else
        # Fallback - try to get primary non-loopback IP
        local primary_ip="127.0.0.1"
        local ip_candidates=($(ip -4 addr show | grep -oP 'inet \K[\d.]+' | grep -v '127.0.0.1' | head -1))
        if [ ${#ip_candidates[@]} -gt 0 ]; then
            primary_ip="${ip_candidates[0]}"
        fi
        
        echo "HTTP:  http://${primary_ip}:8080" >> "$tmp_motd"
        echo "HTTPS: https://${primary_ip}:8443 (secure connection)" >> "$tmp_motd"
    fi
    
    echo -e "\nNote: The web interface uses a self-signed certificate." >> "$tmp_motd"
    echo "Default login: root / linux" >> "$tmp_motd"
    echo "Have a lot of fun..." >> "$tmp_motd"

    # Update motd file
    if [ -f "$tmp_motd" ]; then
        cat "$tmp_motd" > "$motd_file"
        rm -f "$tmp_motd"
    fi
    
    log_success "MOTD updated with welcome screen"
}

# Configure FastAPI for web UI - direct file serving without Nginx
configure_fastapi() {
    log_info "Configuring standalone FastAPI for web UI (no Nginx required)"
    
    # Make sure web-ui directory exists
    ensure_directory "${PERSISTENCE_ROOT}/web-ui"
    ensure_directory "${PERSISTENCE_ROOT}/web-ui/css"
    ensure_directory "${PERSISTENCE_ROOT}/web-ui/js"
    ensure_directory "${PERSISTENCE_ROOT}/web-ui/img"
    ensure_directory "${PERSISTENCE_ROOT}/web-ui/config"
    ensure_directory "${PERSISTENCE_ROOT}/api"
    
    # Verify if login.html exists and log warning if it doesn't
    if [ ! -f "${PERSISTENCE_ROOT}/web-ui/login.html" ]; then
        log_warning "login.html not found in ${PERSISTENCE_ROOT}/web-ui/. Web UI may not function correctly."
    fi

    # Generate self-signed SSL certificate if needed for later HTTPS support
    SSL_DIR="${PERSISTENCE_ROOT}/ssl"
    ensure_directory "$SSL_DIR"
    
    if [ ! -f "${SSL_DIR}/persistenceos.crt" ]; then
        openssl req -x509 -nodes -days 3650 -newkey rsa:2048 \
            -keyout "${SSL_DIR}/persistenceos.key" \
            -out "${SSL_DIR}/persistenceos.crt" \
            -subj "/C=US/ST=State/L=City/O=PersistenceOS/CN=persistenceos.local" 2>/dev/null || \
            log_warning "SSL certificate generation failed"
        
        chmod 600 "${SSL_DIR}/persistenceos.key" 2>/dev/null || true
        chmod 644 "${SSL_DIR}/persistenceos.crt" 2>/dev/null || true
    fi
    
    # Create the API configuration JSON file
    local PRIMARY_IP="127.0.0.1"
    local ip_candidates=($(ip -4 addr show 2>/dev/null | grep -oP 'inet \K[\d.]+' | grep -v '127.0.0.1' | head -1))
    if [ ${#ip_candidates[@]} -gt 0 ]; then
        PRIMARY_IP="${ip_candidates[0]}"
    fi
    
    cat > "${PERSISTENCE_ROOT}/web-ui/api-config.json" << EOF
{
  "host": "${PRIMARY_IP}",
  "http_port": 8080,
  "https_port": 8443,
    "api_base_url": "/api",
    "secure_api_base_url": "/api",
  "available_ips": ["${PRIMARY_IP}", "127.0.0.1"],
  "version": "6.1.0"
}
EOF

    # Set proper permissions on web-ui directory and files
    chmod -R 755 "${PERSISTENCE_ROOT}/web-ui" 2>/dev/null || true
    find "${PERSISTENCE_ROOT}/web-ui" -type f -exec chmod 644 {} \; 2>/dev/null || true
    chmod 644 "${PERSISTENCE_ROOT}/web-ui/api-config.json" 2>/dev/null || true
    
    # Ensure proper permissions for the API directory
    chmod 755 "${PERSISTENCE_ROOT}/api" 2>/dev/null || true
    
    log_success "Standalone FastAPI configured (no Nginx required)"
}

# Main function
main() {
    log_info "Starting PersistenceOS configuration"

    # Phase 1: Initialization
    ensure_directory "${PERSISTENCE_ROOT}"
    ensure_directory "${PERSISTENCE_SCRIPTS}"
    ensure_directory "${PERSISTENCE_SERVICES}"
    ensure_directory "${PERSISTENCE_BIN}"
    ensure_directory "${PERSISTENCE_TOOLS}"
    ensure_directory "${PERSISTENCE_VAR}"
    ensure_directory "${PERSISTENCE_LOG}"
    ensure_directory "${PERSISTENCE_CONFIG}"
    ensure_directory "${PERSISTENCE_ROOT}/web-ui"
    ensure_directory "${PERSISTENCE_ROOT}/api"

    # Phase 2: Core Configuration
    set_permissions
    
    # Configure network interfaces first
    configure_network
    
    # Get the web IP for both MOTD and web UI
    local web_ip=$(get_network_ips)
    log_info "Detected web interface IP: ${web_ip}"
    
    # Update web UI configuration with detected IP
    update_web_ui_config "$web_ip"
    
    # Configure minimal API for web UI
    configure_minimal_api
    
    # Create web UI configuration files
    create_web_ui_config_files
    
    # Ensure auth.js exists for authentication
    ensure_auth_js
    
    # Ensure web UI assets are available (CSS, JS, HTML files)
    ensure_web_ui_assets
    
    # Configure and start Nginx
    configure_nginx
    configure_services
    
    # Final check to ensure nginx is working properly
    if ! systemctl is-active --quiet nginx; then
        log_warning "Nginx still not running, attempting final fix..."
        fix_nginx_service
    fi
    
    # Update MOTD with the same IP
    update_motd

    # Configure network interfaces to ensure they are up
    configure_network

    # Configure standalone FastAPI (no Nginx required)
    configure_fastapi

    # Configure services
    configure_services

    # Final message
    log_info "PersistenceOS configuration complete"
    echo "==============================================="
    echo "The system has been configured successfully."
    echo "You can access the web UI at:"
    echo "   http://$(get_network_ips "true"):8080"
    echo "==============================================="
}

# Run the main function
main "$@"
exit 0