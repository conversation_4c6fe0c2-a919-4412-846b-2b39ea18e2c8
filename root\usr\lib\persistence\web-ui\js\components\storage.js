// Storage Component
app.component('Storage', {
    props: {
        storagePools: Array
    },
    emits: ['refresh-data'],
    data() {
        return {
            isLoading: false
        };
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');
            
            // Reset loading state after animation
            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        
        managePool(poolName) {
            console.log(`Managing pool: ${poolName}`);
            // In a real implementation, this would navigate to pool details or open a modal
        },
        
        snapshotPool(poolName) {
            console.log(`Creating snapshot for pool: ${poolName}`);
            // In a real implementation, this would call an API
        },
        
        createPool() {
            console.log('Creating new storage pool');
            // In a real implementation, this would open a modal
        },
        
        importPool() {
            console.log('Importing storage pool');
            // In a real implementation, this would open a modal
        }
    },
    template: `
        <div id="storage-section" class="content-section">
            <div class="card">
                <div class="card-header">
                    <h3>Storage Pools</h3>
                    <div class="card-actions">
                        <button class="refresh-btn" @click="refreshData"
                            :class="{'spinning': isLoading}">
                            <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="action-bar">
                        <button class="btn btn-primary" @click="createPool">Create Pool</button>
                        <button class="btn btn-primary" @click="importPool">Import Pool</button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Type</th>
                                <th>Size</th>
                                <th>Used</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="pool in storagePools" :key="pool.name">
                                <td>{{ pool.name }}</td>
                                <td>
                                    <span class="status-badge" :class="{ 'running': pool.status === 'healthy' }">
                                        {{ pool.status.charAt(0).toUpperCase() + pool.status.slice(1) }}
                                    </span>
                                </td>
                                <td>{{ pool.type }}</td>
                                <td>{{ pool.size }}</td>
                                <td>{{ pool.used }} ({{ pool.usedPercent }}%)</td>
                                <td>
                                    <button class="btn btn-primary" @click="managePool(pool.name)">Manage</button>
                                    <button class="btn btn-primary" @click="snapshotPool(pool.name)">Snapshot</button>
                                </td>
                            </tr>
                            
                            <!-- Show message if no pools exist -->
                            <tr v-if="storagePools.length === 0">
                                <td colspan="6" class="no-data">
                                    No storage pools available. Click 'Create Pool' to add one.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `
}); 