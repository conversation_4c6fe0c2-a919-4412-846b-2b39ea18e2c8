[Unit]
Description=PersistenceOS Web UI and API Service
After=network.target persistenceos-core-services.service
Wants=network-online.target persistenceos-core-services.service
Requires=network-online.target
# Ensure core services have completed before starting API
# Note: JavaScript files may be installed by RPM or by config.sh during first boot

[Service]
Type=simple
# Verify JavaScript files and web-ui directory before starting API
ExecStartPre=/bin/bash -c 'echo "Verifying web-ui setup before starting API..." >> /var/log/persistenceos-api.log'
ExecStartPre=/bin/bash -c 'mkdir -p /usr/lib/persistence/web-ui/js /usr/lib/persistence/web-ui/css /usr/lib/persistence/web-ui/img'
ExecStartPre=/bin/bash -c 'for js_file in app.js auth.js vue.js login.js; do if [ -f "/usr/lib/persistence/web-ui/js/$js_file" ]; then echo "✅ $js_file found: $(stat -c%s /usr/lib/persistence/web-ui/js/$js_file) bytes" >> /var/log/persistenceos-api.log; else echo "⚠️ $js_file not found - will use fallback or config.sh will handle" >> /var/log/persistenceos-api.log; fi; done'
ExecStart=/usr/lib/persistence/api/run_api.sh
WorkingDirectory=/usr/lib/persistence/api
Environment=WEB_ROOT=/usr/lib/persistence/web-ui
Environment=API_PORT=8080
Environment=DEBUG_MODE=false
Environment=VAR_WEB_ROOT=/var/lib/persistence/web-ui
Environment=API_CONFIG_PATH=/usr/lib/persistence/web-ui/api-config.json
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
# Make sure we have enough file descriptors to handle multiple connections
LimitNOFILE=10000

[Install]
WantedBy=multi-user.target