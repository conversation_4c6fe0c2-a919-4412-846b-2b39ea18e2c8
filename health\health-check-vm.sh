#!/bin/bash
#================
# FILE          : health-check-vm.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Virtual machine health monitoring script
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Source common functions
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
if [ -f "${SCRIPT_DIR}/../scripts/util-common.sh" ]; then
    source "${SCRIPT_DIR}/../scripts/util-common.sh"
else
    echo "Error: util-common.sh not found"
    exit 1
fi

# Define log file
LOG_FILE="${PERSISTENCE_LOG}/health-check-vm.log"
export COMMON_LOG_FILE="${LOG_FILE}"

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help, -h            Show this help message"
    echo "  --version, -v         Show version information"
    echo "  --verbose             Show detailed output"
    echo "  --output FORMAT       Output format (json, text)"
    echo ""
    echo "Examples:"
    echo "  $0                    Run VM health check"
    echo "  $0 --output json      Run VM health check and output in JSON format"
}

# Check libvirt service health
check_libvirt_service() {
    log_info "Checking libvirt service health"
    
    # Check if libvirtd is running
    if ! systemctl is-active --quiet libvirtd; then
        # Create result
        local result=$(cat << EOF
{
    "status": "critical",
    "message": "libvirtd service is not running",
    "service": "stopped"
}
EOF
)
        
        log_error "libvirtd service is not running"
        echo "${result}"
        return 1
    fi
    
    # Check if libvirtd is enabled
    local is_enabled=$(systemctl is-enabled --quiet libvirtd && echo "true" || echo "false")
    
    # Create result
    local result=$(cat << EOF
{
    "status": "healthy",
    "message": "libvirtd service is running",
    "service": "running",
    "enabled": ${is_enabled}
}
EOF
)
    
    log_success "libvirtd service is running"
    echo "${result}"
    return 0
}

# Check VM network health
check_vm_network() {
    log_info "Checking VM network health"
    
    # Check if virsh is available
    if ! command -v virsh &> /dev/null; then
        # Create result
        local result=$(cat << EOF
{
    "status": "warning",
    "message": "virsh command not found",
    "networks": []
}
EOF
)
        
        log_error "virsh command not found"
        echo "${result}"
        return 1
    fi
    
    # Get VM networks
    local networks_output
    if ! networks_output=$(virsh net-list --all 2>&1); then
        # Create result
        local result=$(cat << EOF
{
    "status": "warning",
    "message": "Failed to list VM networks",
    "networks": []
}
EOF
)
        
        log_error "Failed to list VM networks: ${networks_output}"
        echo "${result}"
        return 1
    fi
    
    # Parse networks
    local active_networks=()
    local inactive_networks=()
    local network_details=()
    
    # Skip header line
    while read -r line; do
        # Skip empty lines and header
        if [[ -z "${line}" || "${line}" == *"Name"*"State"*"Autostart"* ]]; then
            continue
        fi
        
        # Parse network information
        local name=$(echo "${line}" | awk '{print $1}')
        local state=$(echo "${line}" | awk '{print $2}')
        local autostart=$(echo "${line}" | awk '{print $3}')
        
        # Add to appropriate array
        if [[ "${state}" == "active" ]]; then
            active_networks+=("${name}")
        else
            inactive_networks+=("${name}")
        fi
        
        # Add to network details
        network_details+=("{\"name\": \"${name}\", \"state\": \"${state}\", \"autostart\": \"${autostart}\"}")
    done <<< "${networks_output}"
    
    # Determine status
    local status="healthy"
    local message="All VM networks are active"
    
    if [ ${#active_networks[@]} -eq 0 ]; then
        status="critical"
        message="No active VM networks found"
    elif [ ${#inactive_networks[@]} -gt 0 ]; then
        status="warning"
        message="Some VM networks are inactive"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${status}",
    "message": "${message}",
    "active": [$(printf '"%s",' "${active_networks[@]}" | sed 's/,$//')],
    "inactive": [$(printf '"%s",' "${inactive_networks[@]}" | sed 's/,$//')],
    "networks": [$(printf '%s,' "${network_details[@]}" | sed 's/,$//')]
}
EOF
)
    
    log_success "VM network check completed with status: ${status}"
    echo "${result}"
    return 0
}

# Check VM storage health
check_vm_storage() {
    log_info "Checking VM storage health"
    
    # Check if virsh is available
    if ! command -v virsh &> /dev/null; then
        # Create result
        local result=$(cat << EOF
{
    "status": "warning",
    "message": "virsh command not found",
    "pools": []
}
EOF
)
        
        log_error "virsh command not found"
        echo "${result}"
        return 1
    fi
    
    # Get VM storage pools
    local pools_output
    if ! pools_output=$(virsh pool-list --all 2>&1); then
        # Create result
        local result=$(cat << EOF
{
    "status": "warning",
    "message": "Failed to list VM storage pools",
    "pools": []
}
EOF
)
        
        log_error "Failed to list VM storage pools: ${pools_output}"
        echo "${result}"
        return 1
    fi
    
    # Parse pools
    local active_pools=()
    local inactive_pools=()
    local pool_details=()
    
    # Skip header line
    while read -r line; do
        # Skip empty lines and header
        if [[ -z "${line}" || "${line}" == *"Name"*"State"*"Autostart"* ]]; then
            continue
        fi
        
        # Parse pool information
        local name=$(echo "${line}" | awk '{print $1}')
        local state=$(echo "${line}" | awk '{print $2}')
        local autostart=$(echo "${line}" | awk '{print $3}')
        
        # Add to appropriate array
        if [[ "${state}" == "active" ]]; then
            active_pools+=("${name}")
        else
            inactive_pools+=("${name}")
        fi
        
        # Add to pool details
        pool_details+=("{\"name\": \"${name}\", \"state\": \"${state}\", \"autostart\": \"${autostart}\"}")
    done <<< "${pools_output}"
    
    # Determine status
    local status="healthy"
    local message="All VM storage pools are active"
    
    if [ ${#active_pools[@]} -eq 0 ]; then
        status="critical"
        message="No active VM storage pools found"
    elif [ ${#inactive_pools[@]} -gt 0 ]; then
        status="warning"
        message="Some VM storage pools are inactive"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${status}",
    "message": "${message}",
    "active": [$(printf '"%s",' "${active_pools[@]}" | sed 's/,$//')],
    "inactive": [$(printf '"%s",' "${inactive_pools[@]}" | sed 's/,$//')],
    "pools": [$(printf '%s,' "${pool_details[@]}" | sed 's/,$//')]
}
EOF
)
    
    log_success "VM storage check completed with status: ${status}"
    echo "${result}"
    return 0
}

# Check VM status
check_vm_status() {
    log_info "Checking VM status"
    
    # Check if virsh is available
    if ! command -v virsh &> /dev/null; then
        # Create result
        local result=$(cat << EOF
{
    "status": "warning",
    "message": "virsh command not found",
    "vms": []
}
EOF
)
        
        log_error "virsh command not found"
        echo "${result}"
        return 1
    fi
    
    # Get VM list
    local vms_output
    if ! vms_output=$(virsh list --all 2>&1); then
        # Create result
        local result=$(cat << EOF
{
    "status": "warning",
    "message": "Failed to list VMs",
    "vms": []
}
EOF
)
        
        log_error "Failed to list VMs: ${vms_output}"
        echo "${result}"
        return 1
    fi
    
    # Parse VMs
    local running_vms=()
    local stopped_vms=()
    local vm_details=()
    
    # Skip header line
    while read -r line; do
        # Skip empty lines and header
        if [[ -z "${line}" || "${line}" == *"Id"*"Name"*"State"* ]]; then
            continue
        fi
        
        # Parse VM information
        local id=$(echo "${line}" | awk '{print $1}')
        local name=$(echo "${line}" | awk '{print $2}')
        local state=$(echo "${line}" | awk '{print $3 " " $4}' | sed 's/^ *//' | sed 's/ *$//')
        
        # Add to appropriate array
        if [[ "${state}" == "running" ]]; then
            running_vms+=("${name}")
        else
            stopped_vms+=("${name}")
        fi
        
        # Add to VM details
        vm_details+=("{\"name\": \"${name}\", \"id\": \"${id}\", \"state\": \"${state}\"}")
    done <<< "${vms_output}"
    
    # Determine status
    local status="healthy"
    local message="All VMs are in expected state"
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${status}",
    "message": "${message}",
    "running": [$(printf '"%s",' "${running_vms[@]}" | sed 's/,$//')],
    "stopped": [$(printf '"%s",' "${stopped_vms[@]}" | sed 's/,$//')],
    "vms": [$(printf '%s,' "${vm_details[@]}" | sed 's/,$//')]
}
EOF
)
    
    log_success "VM status check completed with status: ${status}"
    echo "${result}"
    return 0
}

# Check VM health
check_vm_health() {
    log_info "Checking VM health"
    
    # Check components
    local service_health=$(check_libvirt_service)
    local network_health=$(check_vm_network)
    local storage_health=$(check_vm_storage)
    local vm_status=$(check_vm_status)
    
    # Determine overall status
    local service_status=$(echo "${service_health}" | grep -o '"status": *"[^"]*"' | head -n 1 | cut -d'"' -f4)
    local network_status=$(echo "${network_health}" | grep -o '"status": *"[^"]*"' | head -n 1 | cut -d'"' -f4)
    local storage_status=$(echo "${storage_health}" | grep -o '"status": *"[^"]*"' | head -n 1 | cut -d'"' -f4)
    local vm_status_status=$(echo "${vm_status}" | grep -o '"status": *"[^"]*"' | head -n 1 | cut -d'"' -f4)
    
    local overall_status="healthy"
    
    if [ "${service_status}" = "critical" ] || [ "${network_status}" = "critical" ] || [ "${storage_status}" = "critical" ] || [ "${vm_status_status}" = "critical" ]; then
        overall_status="critical"
    elif [ "${service_status}" = "warning" ] || [ "${network_status}" = "warning" ] || [ "${storage_status}" = "warning" ] || [ "${vm_status_status}" = "warning" ]; then
        overall_status="warning"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${overall_status}",
    "details": {
        "service": ${service_health},
        "network": ${network_health},
        "storage": ${storage_health},
        "vms": ${vm_status}
    }
}
EOF
)
    
    log_success "VM health check completed with status: ${overall_status}"
    
    # Output health status
    if [ "${OUTPUT_FORMAT}" = "json" ]; then
        echo "${result}"
    else
        # Default to text format
        echo "VM Health Status: ${overall_status}"
        echo ""
        echo "Service Status: ${service_status}"
        echo "Network Status: ${network_status}"
        echo "Storage Status: ${storage_status}"
        echo "VM Status: ${vm_status_status}"
        echo ""
        
        # Show inactive networks
        local inactive_networks=$(echo "${network_health}" | grep -o '"inactive": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        if [ -n "${inactive_networks}" ]; then
            echo "Inactive Networks: ${inactive_networks}"
        fi
        
        # Show inactive pools
        local inactive_pools=$(echo "${storage_health}" | grep -o '"inactive": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        if [ -n "${inactive_pools}" ]; then
            echo "Inactive Storage Pools: ${inactive_pools}"
        fi
        
        # Show running VMs
        local running_vms=$(echo "${vm_status}" | grep -o '"running": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        if [ -n "${running_vms}" ]; then
            echo "Running VMs: ${running_vms}"
        fi
        
        # Show stopped VMs
        local stopped_vms=$(echo "${vm_status}" | grep -o '"stopped": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        if [ -n "${stopped_vms}" ]; then
            echo "Stopped VMs: ${stopped_vms}"
        fi
    fi
    
    return 0
}

# Main function
main() {
    # Default values
    VERBOSE="false"
    OUTPUT_FORMAT="text"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --help|-h)
                show_usage
                exit 0
                ;;
            --version|-v)
                echo "health-check-vm.sh version ${VERSION}"
                echo "Part of PersistenceOS ${PERSISTENCE_VERSION}"
                exit 0
                ;;
            --verbose)
                VERBOSE="true"
                shift
                ;;
            --output)
                OUTPUT_FORMAT="$2"
                shift 2
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log_info "Starting VM health check (version ${VERSION})"
    
    # Run VM health check
    check_vm_health
    
    log_success "VM health check completed successfully"
    return 0
}

# Run main function
main "$@"
exit $?
