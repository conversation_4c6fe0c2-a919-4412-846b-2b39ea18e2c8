// Settings Component
app.component('Settings', {
    props: {
        systemInfo: Object
    },
    emits: ['refresh-data'],
    data() {
        return {
            isLoading: false,
            activeTab: 'system',
            terminalHistory: [],
            terminalInput: '',
            terminalCommandHistory: [],
            terminalHistoryIndex: -1
        };
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');
            
            // Reset loading state after animation
            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        
        setActiveTab(tab) {
            this.activeTab = tab;
        },
        
        editHostname() {
            console.log('Editing hostname');
            // In a real implementation, this would open a modal
        },
        
        checkForUpdates() {
            console.log('Checking for updates');
            // In a real implementation, this would call an API
        },
        
        serviceAction(service, action) {
            console.log(`${action} service: ${service}`);
            // In a real implementation, this would call an API
        },
        
        changePassword(username) {
            console.log(`Changing password for: ${username}`);
            // In a real implementation, this would open a modal
        },
        
        processTerminalCommand() {
            if (!this.terminalInput.trim()) return;
            
            // Add command to history
            this.terminalCommandHistory.push(this.terminalInput);
            this.terminalHistoryIndex = this.terminalCommandHistory.length;
            
            // Add command to terminal display
            this.terminalHistory.push({
                type: 'command',
                content: this.terminalInput
            });
            
            // Process command (mock responses)
            const command = this.terminalInput.trim().toLowerCase();
            let response = '';
            
            if (command === 'help') {
                response = `Available commands:
  help                 - Display this help message
  ls [directory]       - List directory contents
  cd [directory]       - Change directory
  cat [file]           - Display file contents
  mkdir [directory]    - Create a directory
  rm [file/directory]  - Remove a file or directory
  df                   - Show disk usage
  free                 - Show memory usage
  top                  - Show running processes
  systemctl [command]  - Control system services
  reboot               - Reboot the system
  shutdown             - Shutdown the system
  clear                - Clear the terminal screen`;
            } else if (command === 'ls') {
                response = 'bin  boot  dev  etc  home  lib  lib64  media  mnt  opt  proc  root  run  sbin  srv  sys  tmp  usr  var';
            } else if (command === 'clear') {
                this.terminalHistory = [];
                return;
            } else if (command === 'df') {
                response = `Filesystem      Size  Used Avail Use% Mounted on
/dev/sda1       500G  150G  350G  30% /
/dev/sda2       2.0T  800G  1.2T  40% /mnt/vm-storage`;
            } else if (command === 'free') {
                response = `              total        used        free      shared  buff/cache   available
Mem:        16384M       6553M       4915M        512M       4915M       9830M
Swap:        8192M          0M       8192M`;
            } else if (command === 'hostname') {
                response = this.systemInfo.hostname;
            } else if (command === 'uname -a') {
                response = `Linux ${this.systemInfo.hostname} ${this.systemInfo.kernel} #1 SMP PREEMPT_DYNAMIC Thu Jun 15 10:15:32 UTC 2023 (a54e539) x86_64 x86_64 x86_64 GNU/Linux`;
            } else if (command === 'reboot' || command === 'shutdown') {
                response = 'This command cannot be executed from the web terminal for security reasons.';
            } else {
                response = `Command not found: ${command}. Type 'help' for available commands.`;
            }
            
            // Add response to terminal display
            if (response) {
                this.terminalHistory.push({
                    type: 'response',
                    content: response
                });
            }
            
            // Clear input
            this.terminalInput = '';
            
            // Scroll to bottom (in next tick after DOM update)
            this.$nextTick(() => {
                const terminal = document.getElementById('terminal-output');
                if (terminal) {
                    terminal.scrollTop = terminal.scrollHeight;
                }
            });
        },
        
        handleTerminalKeydown(event) {
            if (event.key === 'Enter') {
                this.processTerminalCommand();
            } else if (event.key === 'ArrowUp') {
                // Navigate command history (up)
                event.preventDefault();
                if (this.terminalHistoryIndex > 0) {
                    this.terminalHistoryIndex--;
                    this.terminalInput = this.terminalCommandHistory[this.terminalHistoryIndex];
                }
            } else if (event.key === 'ArrowDown') {
                // Navigate command history (down)
                event.preventDefault();
                if (this.terminalHistoryIndex < this.terminalCommandHistory.length - 1) {
                    this.terminalHistoryIndex++;
                    this.terminalInput = this.terminalCommandHistory[this.terminalHistoryIndex];
                } else {
                    this.terminalHistoryIndex = this.terminalCommandHistory.length;
                    this.terminalInput = '';
                }
            }
        },
        
        clearTerminal() {
            this.terminalHistory = [];
        },
        
        copyTerminal() {
            const text = this.terminalHistory.map(item => {
                if (item.type === 'command') {
                    return `root@${this.systemInfo.hostname}:~# ${item.content}`;
                } else {
                    return item.content;
                }
            }).join('\n');
            
            navigator.clipboard.writeText(text)
                .then(() => {
                    this.terminalHistory.push({
                        type: 'response',
                        content: 'Terminal output copied to clipboard.'
                    });
                    
                    // Remove message after 3 seconds
                    setTimeout(() => {
                        if (this.terminalHistory.length > 0 && 
                            this.terminalHistory[this.terminalHistory.length - 1].content === 'Terminal output copied to clipboard.') {
                            this.terminalHistory.pop();
                        }
                    }, 3000);
                })
                .catch(err => {
                    console.error('Failed to copy terminal output:', err);
                });
        }
    },
    mounted() {
        // Add welcome message to terminal
        this.terminalHistory.push({
            type: 'response',
            content: `Welcome to PersistenceOS Terminal
Version 6.1
Type 'help' for available commands.`
        });
    },
    template: `
        <div id="settings-section" class="content-section">
            <div class="section-header">
                <h2>Settings</h2>
                <div class="section-actions">
                    <button class="btn btn-secondary" id="refresh-settings" @click="refreshData"
                        :class="{'spinning': isLoading}">
                        <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i> Refresh
                    </button>
                </div>
            </div>

            <div class="tabs-container">
                <div class="tabs">
                    <button class="tab-btn" :class="{ 'active': activeTab === 'system' }" 
                        @click="setActiveTab('system')">System</button>
                    <button class="tab-btn" :class="{ 'active': activeTab === 'updates' }" 
                        @click="setActiveTab('updates')">Updates</button>
                    <button class="tab-btn" :class="{ 'active': activeTab === 'services' }" 
                        @click="setActiveTab('services')">Services</button>
                    <button class="tab-btn" :class="{ 'active': activeTab === 'users' }" 
                        @click="setActiveTab('users')">Users</button>
                    <button class="tab-btn" :class="{ 'active': activeTab === 'terminal' }" 
                        @click="setActiveTab('terminal')">Terminal</button>
                </div>

                <!-- System Tab -->
                <div class="tab-content" :class="{ 'active': activeTab === 'system' }" id="system-tab">
                    <div class="card">
                        <div class="card-header">
                            <h3>System Information</h3>
                            <div class="card-actions">
                                <button class="refresh-btn" @click="refreshData"
                                    :class="{'spinning': isLoading}">
                                    <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="system-stats">
                                <div class="stat-item">
                                    <div class="stat-label">Version</div>
                                    <div class="stat-value">{{ systemInfo.version }}</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">Kernel</div>
                                    <div class="stat-value">{{ systemInfo.kernel }}</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">Hostname</div>
                                    <div class="stat-value">
                                        {{ systemInfo.hostname }}
                                        <button class="btn btn-sm btn-secondary" @click="editHostname">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">Uptime</div>
                                    <div class="stat-value" id="settings-uptime">{{ systemInfo.uptime }}</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">CPU</div>
                                    <div class="stat-value">{{ systemInfo.cpu }}</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">Memory</div>
                                    <div class="stat-value">{{ systemInfo.memory }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Updates Tab -->
                <div class="tab-content" :class="{ 'active': activeTab === 'updates' }" id="updates-tab">
                    <div class="card">
                        <div class="card-header">
                            <h3>System Updates</h3>
                            <div class="card-actions">
                                <button class="refresh-btn" @click="refreshData"
                                    :class="{'spinning': isLoading}">
                                    <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="update-status">
                                <div class="status-message">
                                    <i class="fas fa-check-circle"></i>
                                    <span>System is up to date</span>
                                </div>
                                <div class="last-checked">
                                    Last checked: Today at 08:15 AM
                                </div>
                                <div class="update-actions">
                                    <button class="btn btn-primary" id="check-updates-btn" @click="checkForUpdates">
                                        <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i> Snapshot & Check for Updates
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Services Tab -->
                <div class="tab-content" :class="{ 'active': activeTab === 'services' }" id="services-tab">
                    <div class="card">
                        <div class="card-header">
                            <h3>System Services</h3>
                            <div class="card-actions">
                                <button class="refresh-btn" @click="refreshData"
                                    :class="{'spinning': isLoading}">
                                    <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="service-list">
                                <div class="service-item">
                                    <div class="service-name">nginx</div>
                                    <div class="service-status running">
                                        <div class="status-indicator healthy"></div>
                                        Running
                                    </div>
                                    <div class="service-actions">
                                        <button class="btn btn-sm btn-secondary" @click="serviceAction('nginx', 'stop')">
                                            <i class="fas fa-stop"></i> Stop
                                        </button>
                                        <button class="btn btn-sm btn-secondary" @click="serviceAction('nginx', 'restart')">
                                            <i class="fas fa-redo"></i> Restart
                                        </button>
                                    </div>
                                </div>
                                <div class="service-item">
                                    <div class="service-name">libvirtd</div>
                                    <div class="service-status running">
                                        <div class="status-indicator healthy"></div>
                                        Running
                                    </div>
                                    <div class="service-actions">
                                        <button class="btn btn-sm btn-secondary" @click="serviceAction('libvirtd', 'stop')">
                                            <i class="fas fa-stop"></i> Stop
                                        </button>
                                        <button class="btn btn-sm btn-secondary" @click="serviceAction('libvirtd', 'restart')">
                                            <i class="fas fa-redo"></i> Restart
                                        </button>
                                    </div>
                                </div>
                                <div class="service-item">
                                    <div class="service-name">sshd</div>
                                    <div class="service-status running">
                                        <div class="status-indicator healthy"></div>
                                        Running
                                    </div>
                                    <div class="service-actions">
                                        <button class="btn btn-sm btn-secondary" @click="serviceAction('sshd', 'stop')">
                                            <i class="fas fa-stop"></i> Stop
                                        </button>
                                        <button class="btn btn-sm btn-secondary" @click="serviceAction('sshd', 'restart')">
                                            <i class="fas fa-redo"></i> Restart
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Tab -->
                <div class="tab-content" :class="{ 'active': activeTab === 'users' }" id="users-tab">
                    <div class="card">
                        <div class="card-header">
                            <h3>User Management</h3>
                            <div class="card-actions">
                                <button class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> Add User
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="user-list">
                                <div class="user-item">
                                    <div class="user-avatar">
                                        <span class="avatar-text">R</span>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name">root</div>
                                        <div class="user-role">Administrator</div>
                                    </div>
                                    <div class="user-actions">
                                        <button class="btn btn-sm btn-secondary" @click="changePassword('root')">
                                            <i class="fas fa-key"></i> Change Password
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Terminal Tab -->
                <div class="tab-content" :class="{ 'active': activeTab === 'terminal' }" id="terminal-tab">
                    <div class="card">
                        <div class="card-header">
                            <h3>Terminal</h3>
                            <div class="terminal-controls">
                                <button id="clear-terminal" class="btn btn-sm btn-primary" @click="clearTerminal">
                                    <i class="fas fa-eraser"></i> Clear
                                </button>
                                <button id="copy-terminal" class="btn btn-sm btn-primary" @click="copyTerminal">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="terminal-container" @click="$refs.terminalInput.focus()">
                                <div id="terminal-output" class="terminal-output">
                                    <div v-for="(item, index) in terminalHistory" :key="index" 
                                        :class="['terminal-message', { 'terminal-response': item.type === 'response' }]">
                                        <template v-if="item.type === 'command'">
                                            <span class="terminal-prompt">root@{{ systemInfo.hostname }}:~#</span>
                                            <span class="terminal-command">{{ item.content }}</span>
                                        </template>
                                        <template v-else>
                                            {{ item.content }}
                                        </template>
                                    </div>
                                </div>
                                <div class="terminal-input-container">
                                    <span class="terminal-prompt">root@{{ systemInfo.hostname }}:~#</span>
                                    <input type="text" ref="terminalInput" v-model="terminalInput" 
                                        class="terminal-input" placeholder="Enter command..." 
                                        autocomplete="off" @keydown="handleTerminalKeydown">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
}); 