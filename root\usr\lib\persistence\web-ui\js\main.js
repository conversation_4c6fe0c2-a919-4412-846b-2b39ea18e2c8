// PersistenceOS Vue App
const { createApp, ref, onMounted, watch } = Vue;

// Root component
const app = createApp({
    setup() {
        // State
        const activeSection = ref('dashboard');
        const sidebarCollapsed = ref(false);
        
        // System data
        const systemStats = ref({
            uptime: '10 days, 4 hours',
            version: 'PersistenceOS 6.1',
            hostname: 'persistence-server',
            cpuUsage: 45,
            memoryUsage: 60,
            storageUsage: 30
        });
        
        const virtualMachines = ref([
            {
                id: 1,
                name: 'Ubuntu Server',
                status: 'running',
                specs: {
                    cpu: 2,
                    memory: 4,
                    storage: 40
                },
                metrics: {
                    cpu: 45,
                    memory: 60
                }
            },
            {
                id: 2,
                name: 'Windows 10',
                status: 'running',
                specs: {
                    cpu: 4,
                    memory: 8,
                    storage: 120
                },
                metrics: {
                    cpu: 65,
                    memory: 75
                }
            },
            {
                id: 3,
                name: 'Debian 11',
                status: 'stopped',
                specs: {
                    cpu: 1,
                    memory: 2,
                    storage: 20
                },
                metrics: {
                    cpu: 0,
                    memory: 0
                }
            }
        ]);
        
        const storagePools = ref([
            {
                name: 'system',
                status: 'healthy',
                type: 'btrfs',
                size: '500 GB',
                used: '150 GB',
                usedPercent: 30
            },
            {
                name: 'vm-storage',
                status: 'healthy',
                type: 'xfs',
                size: '2 TB',
                used: '800 GB',
                usedPercent: 40
            }
        ]);
        
        const networkInterfaces = ref([
            {
                name: 'eth0',
                status: 'up',
                ipAddress: '*************',
                macAddress: '00:1A:2B:3C:4D:5E',
                download: '2.5 MB/s',
                upload: '0.8 MB/s'
            },
            {
                name: 'virbr0',
                status: 'up',
                ipAddress: '*************',
                macAddress: '52:54:00:12:34:56',
                download: '0.2 MB/s',
                upload: '0.1 MB/s'
            }
        ]);
        
        const systemSnapshots = ref([
            {
                name: 'system-2023-06-15',
                type: 'System',
                date: '2023-06-15 10:30',
                size: '2.5 GB'
            },
            {
                name: 'vm-ubuntu-2023-06-14',
                type: 'VM',
                date: '2023-06-14 14:45',
                size: '4.2 GB'
            }
        ]);
        
        const systemInfo = ref({
            version: 'PersistenceOS 6.1',
            kernel: '5.14.21-150400.24.76-default',
            hostname: 'persistence-server',
            uptime: '10 days, 4 hours',
            cpu: 'Intel Xeon E5-2680 v4 @ 2.40GHz',
            memory: '16 GB'
        });
        
        // Methods
        const setActiveSection = (section) => {
            activeSection.value = section;
            window.location.hash = '#' + section;
        };
        
        const toggleSidebar = () => {
            sidebarCollapsed.value = !sidebarCollapsed.value;
        };
        
        const logout = () => {
            sessionStorage.removeItem('persistenceos_token');
            localStorage.removeItem('persistenceos_token');
            window.location.href = 'login.html';
        };
        
        const fetchSystemStats = () => {
            // In a real implementation, this would be an API call
            console.log('Fetching system stats');
            // Simulate API call
            setTimeout(() => {
                systemStats.value = {
                    ...systemStats.value,
                    uptime: '10 days, ' + Math.floor(Math.random() * 24) + ' hours',
                    cpuUsage: Math.floor(Math.random() * 100),
                    memoryUsage: Math.floor(Math.random() * 100),
                    storageUsage: Math.floor(Math.random() * 100)
                };
            }, 500);
        };
        
        const fetchVirtualMachines = () => {
            console.log('Fetching virtual machines');
            // Simulate API call
            setTimeout(() => {
                // Update only metrics for demo
                virtualMachines.value = virtualMachines.value.map(vm => {
                    if (vm.status === 'running') {
                        return {
                            ...vm,
                            metrics: {
                                cpu: Math.floor(Math.random() * 100),
                                memory: Math.floor(Math.random() * 100)
                            }
                        };
                    }
                    return vm;
                });
            }, 500);
        };
        
        const fetchStoragePools = () => {
            console.log('Fetching storage pools');
        };
        
        const fetchSnapshots = () => {
            console.log('Fetching snapshots');
        };
        
        const fetchNetworkInterfaces = () => {
            console.log('Fetching network interfaces');
        };
        
        const fetchSystemInfo = () => {
            console.log('Fetching system info');
        };
        
        // Lifecycle hooks
        onMounted(() => {
            // Initial data fetch
            fetchSystemStats();
            fetchVirtualMachines();
            
            // Get initial section from hash
            const hash = window.location.hash.substring(1);
            if (hash) {
                activeSection.value = hash;
            }
            
            // Listen for hash changes
            window.addEventListener('hashchange', () => {
                const newHash = window.location.hash.substring(1);
                if (newHash) {
                    activeSection.value = newHash;
                }
            });
            
            // Set up periodic refresh
            setInterval(fetchSystemStats, 30000);
            setInterval(fetchVirtualMachines, 30000);
        });
        
        return {
            activeSection,
            sidebarCollapsed,
            systemStats,
            virtualMachines,
            storagePools,
            networkInterfaces,
            systemSnapshots,
            systemInfo,
            setActiveSection,
            toggleSidebar,
            logout,
            fetchSystemStats,
            fetchVirtualMachines,
            fetchStoragePools,
            fetchSnapshots,
            fetchNetworkInterfaces,
            fetchSystemInfo
        };
    },
    template: `
        <div class="app-container" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
            <Sidebar 
                :active-section="activeSection"
                :collapsed="sidebarCollapsed"
                @update-section="setActiveSection"
                @toggle-sidebar="toggleSidebar"
                @logout="logout"
            />
            
            <div class="main-content">
                <div class="header">
                    <h2 id="page-title" class="bold-heading">
                        {{ activeSection.charAt(0).toUpperCase() + activeSection.slice(1) }}
                    </h2>
                </div>
                
                <div class="content-container">
                    <Dashboard 
                        v-if="activeSection === 'dashboard'"
                        :system-stats="systemStats"
                        :virtual-machines="virtualMachines"
                        :storage-pools="storagePools"
                        :network-interfaces="networkInterfaces"
                        @refresh-data="fetchSystemStats"
                    />
                    
                    <Virtualization 
                        v-if="activeSection === 'vms'"
                        :vms="virtualMachines"
                        @refresh-data="fetchVirtualMachines"
                    />
                    
                    <Storage
                        v-if="activeSection === 'storage'"
                        :storage-pools="storagePools"
                        @refresh-data="fetchStoragePools"
                    />
                    
                    <Snapshots
                        v-if="activeSection === 'snapshots'"
                        :snapshots="systemSnapshots"
                        @refresh-data="fetchSnapshots"
                    />
                    
                    <Network
                        v-if="activeSection === 'network'"
                        :interfaces="networkInterfaces"
                        @refresh-data="fetchNetworkInterfaces"
                    />
                    
                    <Settings
                        v-if="activeSection === 'settings'"
                        :system-info="systemInfo"
                        @refresh-data="fetchSystemInfo"
                    />
                </div>
            </div>
        </div>
    `
});

// Mount the app
app.mount('#app'); 