/**
 * FILE          : auth.js
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : Authentication and token management
 */

/**
 * Authentication module for PersistenceOS
 * Handles user authentication, token management, and session validation
 */
const Auth = (function() {
    // Private variables - STANDARDIZED to match login.js
    const TOKEN_KEY = 'persistenceos_token';
    const USER_KEY = 'persistenceos_user';
    const TOKEN_EXPIRY_KEY = 'persistenceos_token_expiry';
    const AUTH_FLAG_KEY = 'authenticated';

    // Token refresh settings
    const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes in milliseconds
    let refreshTokenInterval = null;

    /**
     * Initializes the authentication module
     * Sets up token refresh interval if user is logged in
     */
    function init() {
        console.log('🔐 Initializing authentication module...');

        // Make auth status available globally for Vue.js FIRST
        window.authStatus = {
            isAuthenticated: isAuthenticated,
            getUser: getUser,
            logout: logout
        };

        // For app.html, perform authentication check
        if (window.location.pathname.endsWith('app.html')) {
            console.log('📱 On app.html - performing authentication check');

            // Check if user just came from login
            const urlParams = new URLSearchParams(window.location.search);
            const fromLogin = urlParams.get('from_login') === 'true';

            if (fromLogin) {
                console.log('✅ User just logged in, skipping auth check and cleaning URL');
                // Remove the parameter from URL
                window.history.replaceState({}, document.title, window.location.pathname);
                return;
            }

            // Regular authentication check
            if (!isAuthenticated()) {
                console.log('❌ Not authenticated, redirecting to login');
                window.location.href = 'login.html';
                return;
            } else {
                console.log('✅ Authentication verified, proceeding with app initialization');
            }
        }

        // Set up authentication features if authenticated
        if (isAuthenticated()) {
            setupTokenRefresh();
            updateUserInfo();
        }

        // Add event listener for page visibility changes
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Wait for server config to be loaded
        window.addEventListener('server-config-loaded', function(event) {
            console.log('Auth module received server configuration');
        });

        console.log('🔐 Authentication module initialized successfully');
    }

    /**
     * Handles page visibility changes to manage token refresh
     * Pauses token refresh when page is hidden, resumes when visible
     */
    function handleVisibilityChange() {
        if (document.visibilityState === 'hidden') {
            // Clear interval when page is not visible
            if (refreshTokenInterval) {
                clearInterval(refreshTokenInterval);
                refreshTokenInterval = null;
            }
        } else if (document.visibilityState === 'visible' && isAuthenticated()) {
            // Resume token refresh when page becomes visible again
            setupTokenRefresh();
        }
    }

    /**
     * Sets up the token refresh interval
     * Checks token expiry and refreshes when needed
     */
    function setupTokenRefresh() {
        // Clear any existing interval
        if (refreshTokenInterval) {
            clearInterval(refreshTokenInterval);
        }

        // Check token immediately
        checkAndRefreshToken();

        // Set up interval to check token regularly
        refreshTokenInterval = setInterval(checkAndRefreshToken, 60000); // Check every minute
    }

    /**
     * Checks if token needs refreshing and refreshes if necessary
     */
    async function checkAndRefreshToken() {
        const expiryTime = getTokenExpiry();

        if (!expiryTime) {
            console.warn('No token expiry found, cannot refresh token');
            return;
        }

        const currentTime = new Date().getTime();
        const timeUntilExpiry = expiryTime - currentTime;

        console.log(`Token check: ${Math.floor(timeUntilExpiry / 1000)} seconds until expiry`);

        // If token is already expired, log out immediately
        if (timeUntilExpiry <= 0) {
            console.warn('Token already expired, logging out');
            logout();
            window.location.href = 'login.html?session_expired=true';
            return;
        }

        // If token is about to expire, refresh it
        if (timeUntilExpiry < TOKEN_REFRESH_THRESHOLD) {
            console.log('Token nearing expiry, attempting refresh');
            try {
                const refreshResult = await refreshToken();
                console.log('Token refreshed successfully, new expiry:', new Date(refreshResult.expires_at).toISOString());
            } catch (error) {
                console.error('Failed to refresh token:', error);
                // If refresh fails and token is very close to expiry (less than 30 seconds), log out
                if (timeUntilExpiry < 30000) {
                    console.warn('Token refresh failed and expiry imminent, logging out');
                    logout();
                    window.location.href = 'login.html?session_expired=true';
                }
            }
        }
    }

    /**
     * Attempts to log in a user with the provided credentials
     * @param {string} username - The username
     * @param {string} password - The password
     * @param {boolean} rememberMe - Whether to remember the user
     * @returns {Promise<Object>} - The user object if login is successful
     */
    async function login(username, password, rememberMe = false) {
        try {
            // Get API URL from server config or use default
            const apiUrl = getApiUrl('/api/auth/token');

            // Try FastAPI authentication
            try {
                // Using FormData as FastAPI's OAuth2PasswordRequestForm expects
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);

                // Make the request
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const data = await response.json();

                    console.log('Login successful:', data);

                    // Store authentication data
                    setToken(data.access_token);
                    setUser(data.user);
                    setTokenExpiry(data.expires_at);

                    // Set up token refresh
                    setupTokenRefresh();

                    return data.user;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || 'Invalid username or password');
                }
            } catch (error) {
                console.warn('API login failed:', error.message);

                // If API login fails, try fallback
                if (username === 'root' && password === 'linux') {
                    console.log('Using fallback authentication');

                    // Create mock user and token
                    const mockUser = {
                        username: username,
                        display_name: 'Administrator',
                        roles: ['admin']
                    };

                    const mockToken = 'dev-token-' + Math.random().toString(36).substring(2);
                    const expiryTime = new Date();
                    expiryTime.setHours(expiryTime.getHours() + 1); // Token valid for 1 hour

                    // Store authentication data
                    setToken(mockToken);
                    setUser(mockUser);
                    setTokenExpiry(expiryTime.getTime());

                    return mockUser;
                }

                throw new Error('Invalid username or password');
            }
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    /**
     * Gets the appropriate API URL based on server configuration
     * @param {string} endpoint - API endpoint to access
     * @returns {string} - Full API URL
     */
    function getApiUrl(endpoint) {
        // Get server configuration or use defaults
        const config = window.serverConfig || {};
        const baseUrl = config.apiBaseUrl || `/api`;

        // If endpoint already starts with baseUrl, return it as is
        if (endpoint.startsWith(baseUrl)) {
            return endpoint;
        }

        // If endpoint already starts with /api, use it directly
        if (endpoint.startsWith('/api')) {
            return endpoint;
        }

        // Otherwise, join baseUrl and endpoint
        return baseUrl + endpoint;
    }

    /**
     * Logs out the current user
     * Clears all authentication data and redirects to login page
     */
    function logout() {
        // Clear token refresh interval
        if (refreshTokenInterval) {
            clearInterval(refreshTokenInterval);
            refreshTokenInterval = null;
        }

        // Clear authentication data - COMPREHENSIVE CLEANUP
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(USER_KEY);
        localStorage.removeItem(TOKEN_EXPIRY_KEY);
        localStorage.removeItem(AUTH_FLAG_KEY);
        localStorage.removeItem('username');
        sessionStorage.removeItem(TOKEN_KEY);
        sessionStorage.removeItem(USER_KEY);
        sessionStorage.removeItem(TOKEN_EXPIRY_KEY);
        sessionStorage.removeItem(AUTH_FLAG_KEY);
        sessionStorage.removeItem('username');

        // Redirect to login page
        window.location.href = 'login.html';
    }

    /**
     * Refreshes the authentication token
     * @returns {Promise<Object>} - New token data
     */
    async function refreshToken() {
        try {
            const token = getToken();

            if (!token) {
                throw new Error('No token available');
            }

            // Skip actual refresh for development tokens
            if (token.startsWith('dev-token-')) {
                // Just extend expiry for development tokens
                const expiryTime = new Date();
                expiryTime.setHours(expiryTime.getHours() + 1); // Token valid for 1 hour
                setTokenExpiry(expiryTime.getTime());
                return { access_token: token, expires_at: expiryTime.getTime() };
            }

            // Get API URL from server config or use default
            const apiUrl = getApiUrl('/api/auth/refresh');

            // Make the request
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            if (response.ok) {
                const data = await response.json();

                // Update token and expiry
                setToken(data.access_token);
                setTokenExpiry(data.expires_at);

                return data;
            } else {
                throw new Error('Failed to refresh token');
            }
        } catch (error) {
            console.error('Token refresh error:', error);
            throw error;
        }
    }

    /**
     * Checks if the user is authenticated
     * @returns {boolean} - Whether the user is authenticated
     */
    function isAuthenticated() {
        // First check the authenticated flag (primary indicator from login.js)
        const authFlag = localStorage.getItem(AUTH_FLAG_KEY) === 'true' ||
                        sessionStorage.getItem(AUTH_FLAG_KEY) === 'true';

        if (!authFlag) {
            return false;
        }

        // Check for token in both localStorage and sessionStorage
        const token = getToken();

        if (!token) {
            return false;
        }

        // Check expiry if available (but don't fail if missing for compatibility)
        const expiry = getTokenExpiry();
        if (expiry) {
            const currentTime = new Date().getTime();
            if (currentTime > expiry) {
                console.log('🔐 Token expired, clearing authentication');
                return false;
            }
        }

        return true;
    }

    /**
     * Gets the authentication token
     * @returns {string|null} - The token or null if not found
     */
    function getToken() {
        // Check sessionStorage first (higher priority)
        const sessionToken = sessionStorage.getItem(TOKEN_KEY);
        if (sessionToken) {
            return sessionToken;
        }

        // Fall back to localStorage
        const localToken = localStorage.getItem(TOKEN_KEY);
        if (localToken) {
            return localToken;
        }

        // Additional compatibility check: look for tokens stored by login.js
        // Check if login.js stored tokens with 'authenticated' flag
        if (sessionStorage.getItem('authenticated') === 'true') {
            const loginToken = sessionStorage.getItem('persistenceos_token');
            if (loginToken) {
                return loginToken;
            }
        }

        if (localStorage.getItem('authenticated') === 'true') {
            const loginToken = localStorage.getItem('persistenceos_token');
            if (loginToken) {
                return loginToken;
            }
        }

        return null;
    }

    /**
     * Sets the authentication token
     * @param {string} token - The token to set
     */
    function setToken(token) {
        // Store in both storages to ensure compatibility
        // The getToken() function will find it in either location
        if (sessionStorage.getItem(AUTH_FLAG_KEY) === 'true') {
            sessionStorage.setItem(TOKEN_KEY, token);
        }
        if (localStorage.getItem(AUTH_FLAG_KEY) === 'true') {
            localStorage.setItem(TOKEN_KEY, token);
        }

        // If neither storage has 'authenticated', default to localStorage
        if (!sessionStorage.getItem(AUTH_FLAG_KEY) && !localStorage.getItem(AUTH_FLAG_KEY)) {
            localStorage.setItem(TOKEN_KEY, token);
            localStorage.setItem(AUTH_FLAG_KEY, 'true'); // Ensure auth flag is set
        }
    }

    /**
     * Gets the user object
     * @returns {Object|null} - The user object or null if not found
     */
    function getUser() {
        // Check sessionStorage first (higher priority)
        const sessionUser = sessionStorage.getItem(USER_KEY);
        if (sessionUser) {
            try {
                return JSON.parse(sessionUser);
            } catch (e) {
                console.error('Failed to parse user from sessionStorage:', e);
            }
        }

        // Fall back to localStorage
        const localUser = localStorage.getItem(USER_KEY);
        if (localUser) {
            try {
                return JSON.parse(localUser);
            } catch (e) {
                console.error('Failed to parse user from localStorage:', e);
            }
        }

        // Additional compatibility check: look for user stored by login.js
        // Check if login.js stored user with 'authenticated' flag
        if (sessionStorage.getItem('authenticated') === 'true') {
            const loginUser = sessionStorage.getItem('persistenceos_user');
            if (loginUser) {
                try {
                    return JSON.parse(loginUser);
                } catch (e) {
                    console.error('Failed to parse login user from sessionStorage:', e);
                }
            }
            // If no user object, create a basic one from username
            const username = sessionStorage.getItem('username');
            if (username) {
                return {
                    username: username,
                    display_name: username === 'root' ? 'Administrator' : username,
                    roles: username === 'root' ? ['admin'] : ['user']
                };
            }
        }

        if (localStorage.getItem('authenticated') === 'true') {
            const loginUser = localStorage.getItem('persistenceos_user');
            if (loginUser) {
                try {
                    return JSON.parse(loginUser);
                } catch (e) {
                    console.error('Failed to parse login user from localStorage:', e);
                }
            }
            // If no user object, create a basic one from username
            const username = localStorage.getItem('username');
            if (username) {
                return {
                    username: username,
                    display_name: username === 'root' ? 'Administrator' : username,
                    roles: username === 'root' ? ['admin'] : ['user']
                };
            }
        }

        return null;
    }

    /**
     * Sets the user object
     * @param {Object} user - The user object to set
     */
    function setUser(user) {
        // Store in both storages to ensure compatibility
        const userJson = JSON.stringify(user);
        if (sessionStorage.getItem(AUTH_FLAG_KEY) === 'true') {
            sessionStorage.setItem(USER_KEY, userJson);
        }
        if (localStorage.getItem(AUTH_FLAG_KEY) === 'true') {
            localStorage.setItem(USER_KEY, userJson);
        }

        // If neither storage has 'authenticated', default to localStorage
        if (!sessionStorage.getItem(AUTH_FLAG_KEY) && !localStorage.getItem(AUTH_FLAG_KEY)) {
            localStorage.setItem(USER_KEY, userJson);
            localStorage.setItem(AUTH_FLAG_KEY, 'true'); // Ensure auth flag is set
        }
    }

    /**
     * Gets the token expiry time
     * @returns {number|null} - The expiry time in milliseconds or null if not found
     */
    function getTokenExpiry() {
        // Check sessionStorage first (higher priority)
        const sessionExpiry = sessionStorage.getItem(TOKEN_EXPIRY_KEY);
        if (sessionExpiry) {
            return parseInt(sessionExpiry, 10);
        }

        // Fall back to localStorage
        const localExpiry = localStorage.getItem(TOKEN_EXPIRY_KEY);
        if (localExpiry) {
            return parseInt(localExpiry, 10);
        }

        // Additional compatibility check: look for expiry stored by login.js
        // Check if login.js stored expiry with 'authenticated' flag
        if (sessionStorage.getItem('authenticated') === 'true') {
            const loginExpiry = sessionStorage.getItem('persistenceos_token_expiry');
            if (loginExpiry) {
                // Convert ISO string to timestamp if needed
                const expiryTime = new Date(loginExpiry).getTime();
                if (!isNaN(expiryTime)) {
                    return expiryTime;
                }
                // Try parsing as number if it's already a timestamp
                const numericExpiry = parseInt(loginExpiry, 10);
                if (!isNaN(numericExpiry)) {
                    return numericExpiry;
                }
            }
        }

        if (localStorage.getItem('authenticated') === 'true') {
            const loginExpiry = localStorage.getItem('persistenceos_token_expiry');
            if (loginExpiry) {
                // Convert ISO string to timestamp if needed
                const expiryTime = new Date(loginExpiry).getTime();
                if (!isNaN(expiryTime)) {
                    return expiryTime;
                }
                // Try parsing as number if it's already a timestamp
                const numericExpiry = parseInt(loginExpiry, 10);
                if (!isNaN(numericExpiry)) {
                    return numericExpiry;
                }
            }
        }

        // If no expiry found but we have a token, create a default expiry (1 hour from now)
        const token = getToken();
        if (token) {
            console.log('No expiry found for existing token, creating default expiry');
            const defaultExpiry = new Date().getTime() + (60 * 60 * 1000); // 1 hour
            setTokenExpiry(defaultExpiry);
            return defaultExpiry;
        }

        return null;
    }

    /**
     * Sets the token expiry time
     * @param {number} expiryTime - The expiry time in milliseconds
     */
    function setTokenExpiry(expiryTime) {
        // Store in both storages to ensure compatibility
        const expiryString = expiryTime.toString();
        if (sessionStorage.getItem(AUTH_FLAG_KEY) === 'true') {
            sessionStorage.setItem(TOKEN_EXPIRY_KEY, expiryString);
        }
        if (localStorage.getItem(AUTH_FLAG_KEY) === 'true') {
            localStorage.setItem(TOKEN_EXPIRY_KEY, expiryString);
        }

        // If neither storage has 'authenticated', default to localStorage
        if (!sessionStorage.getItem(AUTH_FLAG_KEY) && !localStorage.getItem(AUTH_FLAG_KEY)) {
            localStorage.setItem(TOKEN_EXPIRY_KEY, expiryString);
            localStorage.setItem(AUTH_FLAG_KEY, 'true'); // Ensure auth flag is set
        }
    }

    /**
     * Checks if the user has a specific role
     * @param {string} role - The role to check
     * @returns {boolean} - Whether the user has the role
     */
    function hasRole(role) {
        const user = getUser();

        if (!user || !user.roles) {
            return false;
        }

        return user.roles.includes(role);
    }

    /**
     * Checks if the user is an admin
     * @returns {boolean} - Whether the user is an admin
     */
    function isAdmin() {
        return hasRole('admin');
    }

    // Return public API
    return {
        init,
        login,
        logout,
        isAuthenticated,
        getToken,
        getUser,
        hasRole,
        isAdmin,
        refreshToken
    };
})();

/**
 * Handles the login form submission
 */
function handleLogin() {
    const loginForm = document.getElementById('login-form');
    const loginButton = document.getElementById('login-button');
    const errorElement = document.getElementById('login-error');

    if (!loginForm || !loginButton || !errorElement) {
        console.warn('Login form elements not found');
        return;
    }

    // Login button click handler
    loginButton.addEventListener('click', async function() {
        // Hide any previous errors
        errorElement.classList.add('hidden');

        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('remember').checked;

        if (!username || !password) {
            errorElement.textContent = 'Username and password are required';
            errorElement.classList.remove('hidden');
            return;
        }

        try {
            // Attempt to log in
            await Auth.login(username, password, rememberMe);

            // If successful, store authentication flag
            if (rememberMe) {
                localStorage.setItem('authenticated', 'true');
                localStorage.setItem('username', username);
            } else {
                sessionStorage.setItem('authenticated', 'true');
                sessionStorage.setItem('username', username);
            }

            // Redirect to dashboard with from_login parameter
            console.log('Login successful, redirecting to app.html...');
            window.location.href = '/app.html?from_login=true';
        } catch (error) {
            console.error('Login error:', error);
            errorElement.textContent = error.message || 'Invalid username or password';
            errorElement.classList.remove('hidden');
        }
    });

    // Also allow form submission with Enter key
    loginForm.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loginButton.click();
        }
    });
}

/**
 * Sets up the logout button handler
 */
function setupLogout() {
    // Find logout button/link
    const logoutButton = document.querySelector('a[onclick="logout()"]');

    if (logoutButton) {
        // Replace the inline onclick handler with a proper event listener
        logoutButton.removeAttribute('onclick');
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();
            Auth.logout();
        });
    }
}

/**
 * Updates user information in the UI
 */
function updateUserInfo() {
    // Get user info
    const user = Auth.getUser();

    if (!user) {
        return;
    }

    // Update user name in the sidebar
    const userNameElement = document.getElementById('user-name');
    if (userNameElement) {
        userNameElement.textContent = user.display_name || user.username || 'User';
    }

    // Update avatar text if present
    const avatarTextElement = document.querySelector('.avatar-text');
    if (avatarTextElement && user.username) {
        avatarTextElement.textContent = user.username.charAt(0).toUpperCase();
    }
}

/**
 * Loads API configuration from the server
 */
async function loadApiConfig() {
    try {
        const response = await fetch('/api/config');

        if (response.ok) {
            const config = await response.json();

            // Store config globally
            window.serverConfig = config;

            // Dispatch event to notify other modules
            window.dispatchEvent(new CustomEvent('server-config-loaded', { detail: config }));
        } else {
            console.error('Failed to load API config:', response.status);
        }
    } catch (error) {
        console.error('Error loading API config:', error);
    }
}

// Export logout function for global access
window.logout = function() {
    Auth.logout();
};

// Initialize the authentication module when the script loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔐 Auth.js DOM loaded, initializing...');
    Auth.init();

    // Set up additional features if we're on the right page
    if (window.location.pathname.endsWith('login.html')) {
        handleLogin();
    }

    // Set up logout button handler
    setupLogout();

    // Update user info in the UI
    updateUserInfo();

    // Load API configuration
    loadApiConfig();
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading, wait for DOMContentLoaded
    console.log('⏳ DOM still loading, waiting...');
} else {
    // DOM is already loaded
    console.log('🔐 DOM already loaded, initializing auth immediately...');
    Auth.init();

    // Set up additional features if we're on the right page
    if (window.location.pathname.endsWith('login.html')) {
        handleLogin();
    }

    // Set up logout button handler
    setupLogout();

    // Update user info in the UI
    updateUserInfo();

    // Load API configuration
    loadApiConfig();
}
