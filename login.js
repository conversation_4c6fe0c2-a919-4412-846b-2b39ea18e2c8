/**
 * PersistenceOS Standalone Login Script
 *
 * This file provides complete login functionality for PersistenceOS
 * and can work independently of config.sh integration.
 *
 * Features:
 * - FastAPI OAuth2 authentication
 * - Session management (localStorage/sessionStorage)
 * - Automatic redirect to app.html
 * - Fallback for API failures
 * - Comprehensive error handling
 * - Debug logging
 */

// Global configuration
const LOGIN_CONFIG = {
    API_BASE_URL: '/api',
    REDIRECT_URL: '/app.html',
    DEFAULT_USERNAME: 'root',
    DEFAULT_PASSWORD: 'linux',
    DEBUG: true
};

// Utility functions
const Logger = {
    log: (message, ...args) => {
        if (LOGIN_CONFIG.DEBUG) {
            console.log(`🔐 [Login] ${message}`, ...args);
        }
    },
    error: (message, ...args) => {
        console.error(`❌ [Login] ${message}`, ...args);
    },
    success: (message, ...args) => {
        if (LOGIN_CONFIG.DEBUG) {
            console.log(`✅ [Login] ${message}`, ...args);
        }
    }
};

// Authentication utilities
const Auth = {
    /**
     * Store authentication data
     */
    storeAuth: (data, rememberMe = false) => {
        const storage = rememberMe ? localStorage : sessionStorage;

        storage.setItem('persistenceos_token', data.access_token);
        storage.setItem('persistenceos_user', JSON.stringify(data.user || {}));
        storage.setItem('persistenceos_token_expiry', data.expires_at || '');
        storage.setItem('authenticated', 'true');
        storage.setItem('username', data.username || '');

        Logger.success('Authentication data stored', { rememberMe });
    },

    /**
     * Clear authentication data
     */
    clearAuth: () => {
        ['localStorage', 'sessionStorage'].forEach(storageType => {
            const storage = window[storageType];
            [
                'persistenceos_token',
                'persistenceos_user',
                'persistenceos_token_expiry',
                'authenticated',
                'username'
            ].forEach(key => storage.removeItem(key));
        });

        Logger.log('Authentication data cleared');
    },

    /**
     * Check if user is already authenticated
     */
    isAuthenticated: () => {
        const localAuth = localStorage.getItem('authenticated') === 'true';
        const sessionAuth = sessionStorage.getItem('authenticated') === 'true';
        return localAuth || sessionAuth;
    }
};

// UI utilities
const UI = {
    /**
     * Show error message
     */
    showError: (message) => {
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }
        Logger.error('UI Error:', message);
    },

    /**
     * Hide error message
     */
    hideError: () => {
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.classList.add('hidden');
        }
    },

    /**
     * Show status message
     */
    showStatus: (message) => {
        const statusElement = document.getElementById('login-status');
        if (statusElement) {
            statusElement.textContent = message;
            statusElement.classList.remove('hidden');
        }
        Logger.log('Status:', message);
    },

    /**
     * Hide status message
     */
    hideStatus: () => {
        const statusElement = document.getElementById('login-status');
        if (statusElement) {
            statusElement.classList.add('hidden');
        }
    },

    /**
     * Set button loading state
     */
    setButtonLoading: (loading = true) => {
        const button = document.getElementById('login-button');
        if (button) {
            button.disabled = loading;
            button.textContent = loading ? 'Logging in...' : 'Log In';
        }
    }
};

// Main login functionality
const LoginManager = {
    /**
     * Attempt authentication with API
     */
    async authenticateWithAPI(username, password) {
        Logger.log('Attempting API authentication...');

        const response = await fetch(`${LOGIN_CONFIG.API_BASE_URL}/auth/token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: new URLSearchParams({
                'username': username,
                'password': password,
                'grant_type': 'password'
            })
        });

        Logger.log(`API response status: ${response.status}`);

        if (response.ok) {
            const data = await response.json();
            data.username = username; // Add username to response data
            return { success: true, data };
        } else {
            const errorData = await response.json().catch(() => ({}));
            return {
                success: false,
                error: errorData.detail || 'Invalid username or password'
            };
        }
    },

    /**
     * Fallback authentication (for testing when API is unavailable)
     */
    authenticateWithFallback(username, password) {
        Logger.log('Using fallback authentication...');

        // Simple fallback for default credentials
        if (username === LOGIN_CONFIG.DEFAULT_USERNAME && password === LOGIN_CONFIG.DEFAULT_PASSWORD) {
            return {
                success: true,
                data: {
                    access_token: 'fallback_token',
                    username: username,
                    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
                }
            };
        }

        return {
            success: false,
            error: 'Invalid credentials'
        };
    },

    /**
     * Redirect to application
     */
    redirectToApp() {
        Logger.success('Redirecting to application...');
        UI.showStatus('Login successful! Redirecting...');

        setTimeout(() => {
            window.location.href = LOGIN_CONFIG.REDIRECT_URL;
        }, 1000);
    },

    /**
     * Handle login process
     */
    async handleLogin(username, password, rememberMe = false) {
        Logger.log('Starting login process...', { username, rememberMe });

        // Reset UI state
        UI.hideError();
        UI.showStatus('Logging in...');
        UI.setButtonLoading(true);

        try {
            // Try API authentication first
            let result = await this.authenticateWithAPI(username, password);

            // If API fails, try fallback
            if (!result.success) {
                Logger.log('API authentication failed, trying fallback...');
                result = this.authenticateWithFallback(username, password);
            }

            if (result.success) {
                // Store authentication data
                Auth.storeAuth(result.data, rememberMe);

                // Redirect to app
                this.redirectToApp();
            } else {
                // Show error
                UI.showError(result.error);
                UI.hideStatus();
            }

        } catch (error) {
            Logger.error('Login process failed:', error);

            // Try fallback on network error
            const fallbackResult = this.authenticateWithFallback(username, password);
            if (fallbackResult.success) {
                Logger.log('Network error, but fallback succeeded');
                Auth.storeAuth(fallbackResult.data, rememberMe);
                UI.showStatus('Network error, using offline mode...');
                setTimeout(() => this.redirectToApp(), 2000);
            } else {
                UI.showError('Connection error. Please try again.');
                UI.hideStatus();
            }
        } finally {
            UI.setButtonLoading(false);
        }
    }
};

// Initialize login page
document.addEventListener('DOMContentLoaded', function() {
    Logger.log('PersistenceOS login page initialized');

    // Check if already authenticated
    if (Auth.isAuthenticated()) {
        Logger.log('User already authenticated, redirecting...');
        LoginManager.redirectToApp();
        return;
    }

    // Get form elements
    const loginForm = document.getElementById('login-form');
    const loginButton = document.getElementById('login-button');
    const usernameField = document.getElementById('username');
    const passwordField = document.getElementById('password');
    const rememberField = document.getElementById('remember');

    // Validate required elements exist
    if (!loginButton || !usernameField || !passwordField) {
        Logger.error('Required form elements not found');
        UI.showError('Login form not properly initialized');
        return;
    }

    // Set default username if empty
    if (!usernameField.value) {
        usernameField.value = LOGIN_CONFIG.DEFAULT_USERNAME;
    }

    // Login button click handler
    loginButton.addEventListener('click', async function(e) {
        e.preventDefault();

        const username = usernameField.value.trim();
        const password = passwordField.value;
        const rememberMe = rememberField ? rememberField.checked : false;

        // Basic validation
        if (!username || !password) {
            UI.showError('Username and password are required');
            return;
        }

        // Handle login
        await LoginManager.handleLogin(username, password, rememberMe);
    });

    // Form submission handler
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            loginButton.click();
        });
    }

    // Enter key handler
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            loginButton.click();
        }
    });

    // Focus on password field (username is pre-filled)
    if (usernameField.value) {
        passwordField.focus();
    } else {
        usernameField.focus();
    }

    Logger.success('Login page ready');
});

// Export for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LoginManager, Auth, UI, Logger };
}