// Snapshots Component
app.component('Snapshots', {
    props: {
        snapshots: Array
    },
    emits: ['refresh-data'],
    data() {
        return {
            isLoading: false
        };
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');
            
            // Reset loading state after animation
            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        
        createSnapshot() {
            console.log('Creating new snapshot');
            // In a real implementation, this would open a modal
        },
        
        restoreSnapshot(name) {
            console.log(`Restoring snapshot: ${name}`);
            // In a real implementation, this would open a confirmation dialog
        },
        
        deleteSnapshot(name) {
            console.log(`Deleting snapshot: ${name}`);
            // In a real implementation, this would open a confirmation dialog
        }
    },
    template: `
        <div id="snapshots-section" class="content-section">
            <div class="card">
                <div class="card-header">
                    <h3>Snapshots</h3>
                    <div class="card-actions">
                        <button class="refresh-btn" @click="refreshData"
                            :class="{'spinning': isLoading}">
                            <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="action-bar">
                        <button class="btn btn-primary" @click="createSnapshot">
                            <i class="fas fa-plus"></i> Create Snapshot
                        </button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Date</th>
                                <th>Size</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="snapshot in snapshots" :key="snapshot.name">
                                <td>{{ snapshot.name }}</td>
                                <td>{{ snapshot.type }}</td>
                                <td>{{ snapshot.date }}</td>
                                <td>{{ snapshot.size }}</td>
                                <td>
                                    <button class="btn btn-primary" @click="restoreSnapshot(snapshot.name)">Restore</button>
                                    <button class="btn btn-primary" @click="deleteSnapshot(snapshot.name)">Delete</button>
                                </td>
                            </tr>
                            
                            <!-- Show message if no snapshots exist -->
                            <tr v-if="snapshots.length === 0">
                                <td colspan="5" class="no-data">
                                    No snapshots available. Click 'Create Snapshot' to add one.
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `
}); 