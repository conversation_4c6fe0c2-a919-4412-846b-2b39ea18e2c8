/**
 * FILE          : app.js
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : Consolidated Vue.js application
 */

// Extract Vue methods we'll use
const { createApp, ref, onMounted, watch } = Vue;

/**
 * PersistenceOS Vue Application
 * Consolidates all components into a single file
 */

// ========== COMPONENT DEFINITIONS ==========

// Sidebar Component
const SidebarComponent = {
    props: {
        activeSection: String,
        collapsed: Boolean
    },
    emits: ['update-section', 'toggle-sidebar', 'logout'],
    template: `
        <div class="sidebar" :class="{ 'collapsed': collapsed }">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="hamburger-menu" @click="$emit('toggle-sidebar')">
                        <i class="fas fa-bars"></i>
                    </div>
                    <h1>PersistenceOS</h1>
                </div>
                <div class="logo-icon">
                    <i class="fas fa-server"></i>
                </div>
            </div>

            <!-- Navigation -->
            <div class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item" :class="{ 'active': activeSection === 'dashboard' }"
                        @click="$emit('update-section', 'dashboard')">
                        <a href="#dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span class="nav-label">Overview</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'vms' }"
                        @click="$emit('update-section', 'vms')">
                        <a href="#vms">
                            <i class="fas fa-server"></i>
                            <span class="nav-label">Virtualization</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'storage' }"
                        @click="$emit('update-section', 'storage')">
                        <a href="#storage">
                            <i class="fas fa-hdd"></i>
                            <span class="nav-label">Storage</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'snapshots' }"
                        @click="$emit('update-section', 'snapshots')">
                        <a href="#snapshots">
                            <i class="fas fa-camera"></i>
                            <span class="nav-label">Snapshots</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'network' }"
                        @click="$emit('update-section', 'network')">
                        <a href="#network">
                            <i class="fas fa-network-wired"></i>
                            <span class="nav-label">Network</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'settings' }"
                        @click="$emit('update-section', 'settings')">
                        <a href="#settings">
                            <i class="fas fa-cog"></i>
                            <span class="nav-label">Settings</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- User Info at bottom -->
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <span class="avatar-text">R</span>
                    </div>
                    <div class="user-details">
                        <span id="user-name">root</span>
                        <a href="#" @click.prevent="$emit('logout')" class="btn-link">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
                <div class="system-status">
                    <div class="status-indicator healthy"></div>
                    <span>System Healthy</span>
                </div>
            </div>
        </div>
    `
};

// Dashboard Component
const DashboardComponent = {
    props: {
        systemStats: Object,
        virtualMachines: Array,
        storagePools: Array,
        networkInterfaces: Array
    },
    emits: ['refresh-data'],
    data() {
        return {
            isLoading: false
        };
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');

            // Reset loading state after animation
            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        }
    },
    template: `
        <div id="dashboard-section" class="content-section active">
            <div class="section-header">
                <div class="section-title">
                    <!-- Empty space where Dashboard heading was -->
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" id="refresh-dashboard" @click="refreshData"
                        :class="{'spinning': isLoading}">
                        <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- System Overview Card -->
            <div class="card system-overview-card">
                <div class="card-header">
                    <h3>System Status</h3>
                    <div class="card-actions">
                        <button class="refresh-btn" @click="refreshData"
                            :class="{'spinning': isLoading}">
                            <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="system-stats">
                        <div class="stat-item">
                            <div class="stat-label">Uptime</div>
                            <div class="stat-value" id="system-uptime">{{ systemStats.uptime }}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Version</div>
                            <div class="stat-value">{{ systemStats.version }}</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Hostname</div>
                            <div class="stat-value" id="system-hostname">{{ systemStats.hostname }}</div>
                        </div>
                    </div>
                    <div class="system-metrics">
                        <div class="metric">
                            <span class="metric-label">CPU Usage</span>
                            <div class="progress-bar">
                                <div class="progress" :style="{ width: systemStats.cpuUsage + '%' }"></div>
                            </div>
                            <span class="metric-value">{{ systemStats.cpuUsage }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Memory Usage</span>
                            <div class="progress-bar">
                                <div class="progress" :style="{ width: systemStats.memoryUsage + '%' }"></div>
                            </div>
                            <span class="metric-value">{{ systemStats.memoryUsage }}%</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Storage Usage</span>
                            <div class="progress-bar">
                                <div class="progress" :style="{ width: systemStats.storageUsage + '%' }"></div>
                            </div>
                            <span class="metric-value">{{ systemStats.storageUsage }}%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Grid with cards for VMs, Storage, Network, Updates -->
            <div class="dashboard-grid">
                <!-- Virtual Machines Card -->
                <div class="card">
                    <div class="card-header">
                        <h3>Virtual Machines</h3>
                        <div class="card-actions">
                            <button class="refresh-btn" @click="refreshData"
                                :class="{'spinning': isLoading}">
                                <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="status-counters">
                            <div class="counter running">
                                <span class="counter-value">{{ virtualMachines.filter(vm => vm.status === 'running').length }}</span>
                                <span class="counter-label">Running</span>
                            </div>
                            <div class="counter stopped">
                                <span class="counter-value">{{ virtualMachines.filter(vm => vm.status === 'stopped').length }}</span>
                                <span class="counter-label">Stopped</span>
                            </div>
                            <div class="counter total">
                                <span class="counter-value">{{ virtualMachines.length }}</span>
                                <span class="counter-label">Total</span>
                            </div>
                        </div>
                        <div class="vm-preview">
                            <div v-if="virtualMachines.length > 0" class="vm-card" :class="virtualMachines[0].status">
                                <div class="vm-header">
                                    <div class="vm-name">{{ virtualMachines[0].name }}</div>
                                    <div class="vm-status">
                                        <div class="status-indicator" :class="{ 'healthy': virtualMachines[0].status === 'running' }"></div>
                                        {{ virtualMachines[0].status.charAt(0).toUpperCase() + virtualMachines[0].status.slice(1) }}
                                    </div>
                                </div>
                                <div class="vm-body">
                                    <div class="vm-specs">
                                        <div class="spec-item">
                                            <i class="fas fa-microchip"></i>
                                            <span>{{ virtualMachines[0].specs.cpu }} vCPUs</span>
                                        </div>
                                        <div class="spec-item">
                                            <i class="fas fa-memory"></i>
                                            <span>{{ virtualMachines[0].specs.memory }} GB</span>
                                        </div>
                                        <div class="spec-item">
                                            <i class="fas fa-hdd"></i>
                                            <span>{{ virtualMachines[0].specs.storage }} GB</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <a href="#vms" class="btn btn-primary">
                                <i class="fas fa-server"></i> Manage VMs
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Storage Pools Card -->
                <div class="card">
                    <div class="card-header">
                        <h3>Storage Pools</h3>
                        <div class="card-actions">
                            <button class="refresh-btn" @click="refreshData"
                                :class="{'spinning': isLoading}">
                                <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="status-counters">
                            <div class="counter healthy">
                                <span class="counter-value">{{ storagePools.filter(pool => pool.status === 'healthy').length }}</span>
                                <span class="counter-label">Healthy</span>
                            </div>
                            <div class="counter degraded">
                                <span class="counter-value">{{ storagePools.filter(pool => pool.status === 'degraded').length }}</span>
                                <span class="counter-label">Degraded</span>
                            </div>
                            <div class="counter total">
                                <span class="counter-value">{{ storagePools.length }}</span>
                                <span class="counter-label">Total</span>
                            </div>
                        </div>
                        <div class="storage-preview">
                            <div v-for="pool in storagePools" :key="pool.name" class="metric">
                                <span class="metric-label">{{ pool.name }}</span>
                                <div class="progress-bar">
                                    <div class="progress" :style="{ width: pool.usedPercent + '%' }"></div>
                                </div>
                                <span class="metric-value">{{ pool.usedPercent }}%</span>
                            </div>
                        </div>
                        <div class="card-actions">
                            <a href="#storage" class="btn btn-primary">
                                <i class="fas fa-hdd"></i> Manage Storage
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Network Activity Card -->
                <div class="card">
                    <div class="card-header">
                        <h3>Network Activity</h3>
                        <div class="card-actions">
                            <button class="refresh-btn" @click="refreshData"
                                :class="{'spinning': isLoading}">
                                <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="network-interfaces">
                            <div v-for="iface in networkInterfaces" :key="iface.name" class="interface-item">
                                <div class="interface-name">{{ iface.name }}</div>
                                <div class="interface-details">
                                    <span class="detail"><i class="fas fa-arrow-down"></i> {{ iface.download }}</span>
                                    <span class="detail"><i class="fas fa-arrow-up"></i> {{ iface.upload }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <a href="#network" class="btn btn-primary">
                                <i class="fas fa-network-wired"></i> Manage Network
                            </a>
                        </div>
                    </div>
                </div>

                <!-- System Updates Card -->
                <div class="card">
                    <div class="card-header">
                        <h3>System Updates</h3>
                        <div class="card-actions">
                            <button class="refresh-btn" @click="refreshData"
                                :class="{'spinning': isLoading}">
                                <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="update-status">
                            <div class="update-status-row">
                                <div class="status-message">
                                    <i class="fas fa-check-circle"></i>
                                    <span id="update-status-text">System is up to date</span>
                                </div>
                                <div class="last-checked">
                                    Last checked: Today at 08:15 AM
                                </div>
                            </div>
                        </div>
                        <div class="card-actions">
                            <a href="#settings" class="btn btn-primary">
                                <i class="fas fa-cog"></i> Manage Updates
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};

// Virtual Machines Component
const VirtualMachineComponent = {
    props: {
        virtualMachines: Array
    },
    emits: ['refresh-data', 'start-vm', 'stop-vm', 'restart-vm', 'delete-vm', 'create-vm'],
    data() {
        return {
            isLoading: false,
            selectedVM: null,
            newVM: {
                name: '',
                os: 'linux',
                specs: {
                    cpu: 2,
                    memory: 4,
                    storage: 20
                }
            },
            showNewVMForm: false
        };
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');

            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        startVM(vm) {
            this.$emit('start-vm', vm);
        },
        stopVM(vm) {
            this.$emit('stop-vm', vm);
        },
        restartVM(vm) {
            this.$emit('restart-vm', vm);
        },
        deleteVM(vm) {
            if (confirm(`Are you sure you want to delete ${vm.name}?`)) {
                this.$emit('delete-vm', vm);
            }
        },
        showCreateVM() {
            this.showNewVMForm = true;
        },
        createVM() {
            this.$emit('create-vm', { ...this.newVM });

            // Reset form and hide it
            this.newVM = {
                name: '',
                os: 'linux',
                specs: {
                    cpu: 2,
                    memory: 4,
                    storage: 20
                }
            };
            this.showNewVMForm = false;
        },
        cancelCreateVM() {
            this.showNewVMForm = false;
        },
        selectVM(vm) {
            this.selectedVM = vm;
        }
    },
    template: `
        <div id="vms-section" class="content-section">
            <div class="section-header">
                <div class="section-title">
                    <h2>Virtual Machines</h2>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" @click="refreshData"
                        :class="{'spinning': isLoading}">
                        <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i> Refresh
                    </button>
                    <button class="btn btn-primary" @click="showCreateVM">
                        <i class="fas fa-plus"></i> New VM
                    </button>
                </div>
            </div>

            <!-- Virtual Machines List -->
            <div class="card vms-card">
                <div class="card-header">
                    <h3>Available Virtual Machines</h3>
                    <div class="card-actions">
                        <button class="refresh-btn" @click="refreshData"
                            :class="{'spinning': isLoading}">
                            <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <table class="data-table vm-table">
                        <thead>
                            <tr>
                                <th class="status-th">Status</th>
                                <th>Name</th>
                                <th>OS</th>
                                <th>CPU</th>
                                <th>Memory</th>
                                <th>Storage</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="vm in virtualMachines" :key="vm.id" @click="selectVM(vm)">
                                <td>
                                    <div class="status-indicator" :class="{ 'healthy': vm.status === 'running' }"></div>
                                    {{ vm.status.charAt(0).toUpperCase() + vm.status.slice(1) }}
                                </td>
                                <td>{{ vm.name }}</td>
                                <td>{{ vm.os }}</td>
                                <td>{{ vm.specs.cpu }} vCPUs</td>
                                <td>{{ vm.specs.memory }} GB</td>
                                <td>{{ vm.specs.storage }} GB</td>
                                <td class="actions-cell">
                                    <button v-if="vm.status === 'stopped'"
                                        class="btn btn-sm btn-success"
                                        @click.stop="startVM(vm)">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button v-if="vm.status === 'running'"
                                        class="btn btn-sm btn-warning"
                                        @click.stop="stopVM(vm)">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button v-if="vm.status === 'running'"
                                        class="btn btn-sm btn-info"
                                        @click.stop="restartVM(vm)">
                                        <i class="fas fa-sync"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger"
                                        @click.stop="deleteVM(vm)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="virtualMachines.length === 0">
                                <td colspan="7" class="text-center">No virtual machines found</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- New VM Form (shown when showNewVMForm is true) -->
            <div class="modal" v-if="showNewVMForm">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Create New Virtual Machine</h3>
                        <button class="close-btn" @click="cancelCreateVM">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="vm-name">Name</label>
                            <input type="text" id="vm-name" v-model="newVM.name" required>
                        </div>
                        <div class="form-group">
                            <label for="vm-os">Operating System</label>
                            <select id="vm-os" v-model="newVM.os">
                                <option value="linux">Linux</option>
                                <option value="windows">Windows</option>
                                <option value="freebsd">FreeBSD</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="vm-cpu">CPU (cores)</label>
                            <input type="number" id="vm-cpu" v-model="newVM.specs.cpu" min="1" max="32">
                        </div>
                        <div class="form-group">
                            <label for="vm-memory">Memory (GB)</label>
                            <input type="number" id="vm-memory" v-model="newVM.specs.memory" min="1" max="128">
                        </div>
                        <div class="form-group">
                            <label for="vm-storage">Storage (GB)</label>
                            <input type="number" id="vm-storage" v-model="newVM.specs.storage" min="5" max="2000">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" @click="cancelCreateVM">Cancel</button>
                        <button class="btn btn-primary" @click="createVM" :disabled="!newVM.name">Create</button>
                    </div>
                </div>
            </div>
        </div>
    `
};

// Storage Component
const StorageComponent = {
    props: {
        storagePools: Array,
        storageDisks: Array
    },
    emits: ['refresh-data', 'create-pool', 'delete-pool', 'add-disk-to-pool'],
    data() {
        return {
            isLoading: false,
            selectedPool: null,
            newPool: {
                name: '',
                type: 'btrfs',
                disks: []
            },
            showNewPoolForm: false
        };
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');

            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        showCreatePool() {
            this.showNewPoolForm = true;
        },
        createPool() {
            this.$emit('create-pool', { ...this.newPool });

            // Reset form and hide it
            this.newPool = {
                name: '',
                type: 'btrfs',
                disks: []
            };
            this.showNewPoolForm = false;
        },
        cancelCreatePool() {
            this.showNewPoolForm = false;
        },
        selectPool(pool) {
            this.selectedPool = pool;
        },
        deletePool(pool) {
            if (confirm(`Are you sure you want to delete the ${pool.name} pool?`)) {
                this.$emit('delete-pool', pool);
            }
        },
        addDiskToPool(disk, pool) {
            this.$emit('add-disk-to-pool', { disk, pool });
        },
        toggleDiskSelection(diskId) {
            const index = this.newPool.disks.indexOf(diskId);
            if (index === -1) {
                this.newPool.disks.push(diskId);
            } else {
                this.newPool.disks.splice(index, 1);
            }
        }
    },
    template: `
        <div id="storage-section" class="content-section">
            <div class="section-header">
                <div class="section-title">
                    <h2>Storage Management</h2>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" @click="refreshData"
                        :class="{'spinning': isLoading}">
                        <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i> Refresh
                    </button>
                    <button class="btn btn-primary" @click="showCreatePool">
                        <i class="fas fa-plus"></i> New Pool
                    </button>
                </div>
            </div>

            <!-- Storage Pools Card -->
            <div class="card storage-card">
                <div class="card-header">
                    <h3>Storage Pools</h3>
                    <div class="card-actions">
                        <button class="refresh-btn" @click="refreshData"
                            :class="{'spinning': isLoading}">
                            <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <table class="data-table pool-table">
                        <thead>
                            <tr>
                                <th class="status-th">Status</th>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Capacity</th>
                                <th>Used</th>
                                <th>Usage</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="pool in storagePools" :key="pool.id" @click="selectPool(pool)">
                                <td>
                                    <div class="status-indicator" :class="{ 'healthy': pool.status === 'healthy' }"></div>
                                    {{ pool.status.charAt(0).toUpperCase() + pool.status.slice(1) }}
                                </td>
                                <td>{{ pool.name }}</td>
                                <td>{{ pool.type }}</td>
                                <td>{{ pool.capacity }}</td>
                                <td>{{ pool.used }}</td>
                                <td>
                                    <div class="progress-bar small">
                                        <div class="progress" :style="{ width: pool.usedPercent + '%' }"></div>
                                    </div>
                                    {{ pool.usedPercent }}%
                                </td>
                                <td class="actions-cell">
                                    <button class="btn btn-sm btn-danger"
                                        @click.stop="deletePool(pool)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="storagePools.length === 0">
                                <td colspan="7" class="text-center">No storage pools found</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Available Disks Card -->
            <div class="card disks-card">
                <div class="card-header">
                    <h3>Available Disks</h3>
                </div>
                <div class="card-body">
                    <table class="data-table disk-table">
                        <thead>
                            <tr>
                                <th>Device</th>
                                <th>Model</th>
                                <th>Size</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="disk in storageDisks" :key="disk.id">
                                <td>{{ disk.device }}</td>
                                <td>{{ disk.model }}</td>
                                <td>{{ disk.size }}</td>
                                <td>{{ disk.type }}</td>
                                <td>
                                    <div class="status-indicator" :class="{ 'healthy': disk.status === 'healthy' }"></div>
                                    {{ disk.status.charAt(0).toUpperCase() + disk.status.slice(1) }}
                                </td>
                                <td class="actions-cell">
                                    <button v-if="disk.status === 'available'"
                                        class="btn btn-sm btn-primary"
                                        @click="addDiskToPool(disk, selectedPool)"
                                        :disabled="!selectedPool">
                                        <i class="fas fa-plus"></i> Add to Pool
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="storageDisks.length === 0">
                                <td colspan="6" class="text-center">No disks found</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- New Pool Form -->
            <div class="modal" v-if="showNewPoolForm">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Create New Storage Pool</h3>
                        <button class="close-btn" @click="cancelCreatePool">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="pool-name">Name</label>
                            <input type="text" id="pool-name" v-model="newPool.name" required>
                        </div>
                        <div class="form-group">
                            <label for="pool-type">Type</label>
                            <select id="pool-type" v-model="newPool.type">
                                <option value="btrfs">btrfs</option>
                                <option value="xfs">XFS</option>
                                <option value="zfs">ZFS</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Select Disks</label>
                            <div class="disk-selection">
                                <div v-for="disk in storageDisks.filter(d => d.status === 'available')"
                                    :key="disk.id"
                                    class="disk-option">
                                    <input type="checkbox"
                                        :id="'disk-' + disk.id"
                                        :value="disk.id"
                                        @change="toggleDiskSelection(disk.id)"
                                        :checked="newPool.disks.includes(disk.id)">
                                    <label :for="'disk-' + disk.id">
                                        {{ disk.device }} ({{ disk.size }}, {{ disk.model }})
                                    </label>
                                </div>
                                <div v-if="storageDisks.filter(d => d.status === 'available').length === 0"
                                    class="text-center p-3">
                                    No available disks found
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" @click="cancelCreatePool">Cancel</button>
                        <button class="btn btn-primary"
                            @click="createPool"
                            :disabled="!newPool.name || newPool.disks.length === 0">
                            Create
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `
};

// Network Component
const NetworkComponent = {
    props: {
        networkInterfaces: Array,
        networkSettings: Object
    },
    emits: ['refresh-data', 'update-interface'],
    data() {
        return {
            isLoading: false,
            selectedInterface: null,
            editingInterface: null,
            interfaceConfig: {
                method: 'dhcp',
                ipAddress: '',
                netmask: '',
                gateway: '',
                dns1: '',
                dns2: ''
            }
        };
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');

            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        selectInterface(iface) {
            this.selectedInterface = iface;
        },
        editInterface(iface) {
            this.editingInterface = iface;
            this.interfaceConfig = {
                method: iface.method || 'dhcp',
                ipAddress: iface.ipAddress || '',
                netmask: iface.netmask || '',
                gateway: iface.gateway || '',
                dns1: iface.dns1 || '',
                dns2: iface.dns2 || ''
            };
        },
        cancelEdit() {
            this.editingInterface = null;
        },
        saveInterface() {
            this.$emit('update-interface', {
                name: this.editingInterface.name,
                config: { ...this.interfaceConfig }
            });
            this.editingInterface = null;
        }
    },
    template: `
        <div id="network-section" class="content-section">
            <div class="section-header">
                <div class="section-title">
                    <h2>Network Configuration</h2>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" @click="refreshData"
                        :class="{'spinning': isLoading}">
                        <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Network Interfaces Card -->
            <div class="card network-card">
                <div class="card-header">
                    <h3>Network Interfaces</h3>
                    <div class="card-actions">
                        <button class="refresh-btn" @click="refreshData"
                            :class="{'spinning': isLoading}">
                            <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <table class="data-table interface-table">
                        <thead>
                            <tr>
                                <th class="status-th">Status</th>
                                <th>Name</th>
                                <th>MAC Address</th>
                                <th>IP Address</th>
                                <th>Method</th>
                                <th>Traffic</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="iface in networkInterfaces" :key="iface.name" @click="selectInterface(iface)">
                                <td>
                                    <div class="status-indicator" :class="{ 'healthy': iface.status === 'up' }"></div>
                                    {{ iface.status === 'up' ? 'Up' : 'Down' }}
                                </td>
                                <td>{{ iface.name }}</td>
                                <td>{{ iface.mac }}</td>
                                <td>{{ iface.ipAddress || 'Not configured' }}</td>
                                <td>{{ iface.method || 'Not configured' }}</td>
                                <td>
                                    <span class="network-traffic">
                                        <i class="fas fa-arrow-down"></i> {{ iface.download }}
                                        <i class="fas fa-arrow-up"></i> {{ iface.upload }}
                                    </span>
                                </td>
                                <td class="actions-cell">
                                    <button class="btn btn-sm btn-primary"
                                        @click.stop="editInterface(iface)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="networkInterfaces.length === 0">
                                <td colspan="7" class="text-center">No network interfaces found</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Network Statistics Card -->
            <div class="card network-stats-card">
                <div class="card-header">
                    <h3>Network Statistics</h3>
                </div>
                <div class="card-body">
                    <div v-if="selectedInterface" class="network-stats">
                        <div class="stat-row">
                            <div class="stat-label">Interface</div>
                            <div class="stat-value">{{ selectedInterface.name }}</div>
                        </div>
                        <div class="stat-row">
                            <div class="stat-label">Status</div>
                            <div class="stat-value">
                                <div class="status-indicator" :class="{ 'healthy': selectedInterface.status === 'up' }"></div>
                                {{ selectedInterface.status === 'up' ? 'Up' : 'Down' }}
                            </div>
                        </div>
                        <div class="stat-row">
                            <div class="stat-label">MAC Address</div>
                            <div class="stat-value">{{ selectedInterface.mac }}</div>
                        </div>
                        <div class="stat-row">
                            <div class="stat-label">IP Address</div>
                            <div class="stat-value">{{ selectedInterface.ipAddress || 'Not configured' }}</div>
                        </div>
                        <div class="stat-row">
                            <div class="stat-label">Netmask</div>
                            <div class="stat-value">{{ selectedInterface.netmask || 'Not configured' }}</div>
                        </div>
                        <div class="stat-row">
                            <div class="stat-label">Gateway</div>
                            <div class="stat-value">{{ selectedInterface.gateway || 'Not configured' }}</div>
                        </div>
                        <div class="stat-row">
                            <div class="stat-label">DNS Servers</div>
                            <div class="stat-value">
                                {{ selectedInterface.dns1 || 'Not configured' }}
                                <span v-if="selectedInterface.dns2">, {{ selectedInterface.dns2 }}</span>
                            </div>
                        </div>
                        <div class="stat-row">
                            <div class="stat-label">Downloaded</div>
                            <div class="stat-value">{{ selectedInterface.totalDownload || '0 KB' }}</div>
                        </div>
                        <div class="stat-row">
                            <div class="stat-label">Uploaded</div>
                            <div class="stat-value">{{ selectedInterface.totalUpload || '0 KB' }}</div>
                        </div>
                    </div>
                    <div v-else class="text-center p-3">
                        Select a network interface to view statistics
                    </div>
                </div>
            </div>

            <!-- Edit Interface Form -->
            <div class="modal" v-if="editingInterface">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Configure {{ editingInterface.name }}</h3>
                        <button class="close-btn" @click="cancelEdit">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="interface-method">Connection Method</label>
                            <select id="interface-method" v-model="interfaceConfig.method">
                                <option value="dhcp">DHCP</option>
                                <option value="static">Static IP</option>
                            </select>
                        </div>
                        <div v-if="interfaceConfig.method === 'static'">
                            <div class="form-group">
                                <label for="interface-ip">IP Address</label>
                                <input type="text" id="interface-ip" v-model="interfaceConfig.ipAddress" placeholder="*************">
                            </div>
                            <div class="form-group">
                                <label for="interface-netmask">Netmask</label>
                                <input type="text" id="interface-netmask" v-model="interfaceConfig.netmask" placeholder="*************">
                            </div>
                            <div class="form-group">
                                <label for="interface-gateway">Gateway</label>
                                <input type="text" id="interface-gateway" v-model="interfaceConfig.gateway" placeholder="***********">
                            </div>
                            <div class="form-group">
                                <label for="interface-dns1">DNS Server 1</label>
                                <input type="text" id="interface-dns1" v-model="interfaceConfig.dns1" placeholder="*******">
                            </div>
                            <div class="form-group">
                                <label for="interface-dns2">DNS Server 2 (Optional)</label>
                                <input type="text" id="interface-dns2" v-model="interfaceConfig.dns2" placeholder="*******">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" @click="cancelEdit">Cancel</button>
                        <button class="btn btn-primary" @click="saveInterface">Save</button>
                    </div>
                </div>
            </div>
        </div>
    `
};

// Snapshots Component
const SnapshotComponent = {
    props: {
        snapshots: Array,
        virtualMachines: Array,
        storagePools: Array
    },
    emits: ['refresh-data', 'create-snapshot', 'restore-snapshot', 'delete-snapshot'],
    data() {
        return {
            isLoading: false,
            selectedSnapshot: null,
            newSnapshot: {
                name: '',
                type: 'system',
                target: '',
                description: ''
            },
            showNewSnapshotForm: false,
            snapshotFilters: {
                type: 'all',
                target: 'all'
            }
        };
    },
    computed: {
        filteredSnapshots() {
            let result = this.snapshots;

            if (this.snapshotFilters.type !== 'all') {
                result = result.filter(snapshot => snapshot.type === this.snapshotFilters.type);
            }

            if (this.snapshotFilters.target !== 'all') {
                result = result.filter(snapshot => snapshot.target === this.snapshotFilters.target);
            }

            return result;
        }
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');

            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        selectSnapshot(snapshot) {
            this.selectedSnapshot = snapshot;
        },
        showCreateSnapshot() {
            this.showNewSnapshotForm = true;
        },
        createSnapshot() {
            this.$emit('create-snapshot', { ...this.newSnapshot });

            // Reset form and hide it
            this.newSnapshot = {
                name: '',
                type: 'system',
                target: '',
                description: ''
            };
            this.showNewSnapshotForm = false;
        },
        cancelCreateSnapshot() {
            this.showNewSnapshotForm = false;
        },
        restoreSnapshot(snapshot) {
            if (confirm(`Are you sure you want to restore snapshot "${snapshot.name}"? This action cannot be undone.`)) {
                this.$emit('restore-snapshot', snapshot);
            }
        },
        deleteSnapshot(snapshot) {
            if (confirm(`Are you sure you want to delete snapshot "${snapshot.name}"?`)) {
                this.$emit('delete-snapshot', snapshot);
            }
        }
    },
    template: `
        <div id="snapshots-section" class="content-section">
            <div class="section-header">
                <div class="section-title">
                    <h2>Snapshots</h2>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" @click="refreshData"
                        :class="{'spinning': isLoading}">
                        <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i> Refresh
                    </button>
                    <button class="btn btn-primary" @click="showCreateSnapshot">
                        <i class="fas fa-plus"></i> New Snapshot
                    </button>
                </div>
            </div>

            <!-- Snapshots Filter Card -->
            <div class="card filter-card">
                <div class="card-header">
                    <h3>Filters</h3>
                </div>
                <div class="card-body">
                    <div class="filter-controls">
                        <div class="filter-group">
                            <label for="filter-type">Type</label>
                            <select id="filter-type" v-model="snapshotFilters.type">
                                <option value="all">All Types</option>
                                <option value="system">System</option>
                                <option value="vm">Virtual Machine</option>
                                <option value="storage">Storage</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="filter-target">Target</label>
                            <select id="filter-target" v-model="snapshotFilters.target">
                                <option value="all">All Targets</option>
                                <option v-if="snapshotFilters.type === 'all' || snapshotFilters.type === 'system'" value="system">
                                    System
                                </option>
                                <option v-if="snapshotFilters.type === 'all' || snapshotFilters.type === 'vm'"
                                    v-for="vm in virtualMachines" :key="'vm-' + vm.id" :value="vm.id">
                                    VM: {{ vm.name }}
                                </option>
                                <option v-if="snapshotFilters.type === 'all' || snapshotFilters.type === 'storage'"
                                    v-for="pool in storagePools" :key="'pool-' + pool.id" :value="pool.id">
                                    Pool: {{ pool.name }}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Snapshots List Card -->
            <div class="card snapshots-card">
                <div class="card-header">
                    <h3>Available Snapshots</h3>
                    <div class="card-actions">
                        <button class="refresh-btn" @click="refreshData"
                            :class="{'spinning': isLoading}">
                            <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <table class="data-table snapshot-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Type</th>
                                <th>Target</th>
                                <th>Created</th>
                                <th>Size</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="snapshot in filteredSnapshots" :key="snapshot.id" @click="selectSnapshot(snapshot)">
                                <td>{{ snapshot.name }}</td>
                                <td>{{ snapshot.type.charAt(0).toUpperCase() + snapshot.type.slice(1) }}</td>
                                <td>{{ snapshot.targetName }}</td>
                                <td>{{ snapshot.created }}</td>
                                <td>{{ snapshot.size }}</td>
                                <td class="actions-cell">
                                    <button class="btn btn-sm btn-success"
                                        @click.stop="restoreSnapshot(snapshot)">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger"
                                        @click.stop="deleteSnapshot(snapshot)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="filteredSnapshots.length === 0">
                                <td colspan="6" class="text-center">No snapshots found</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Snapshot Details Card -->
            <div class="card snapshot-details-card" v-if="selectedSnapshot">
                <div class="card-header">
                    <h3>Snapshot Details: {{ selectedSnapshot.name }}</h3>
                </div>
                <div class="card-body">
                    <div class="snapshot-details">
                        <div class="detail-row">
                            <div class="detail-label">ID</div>
                            <div class="detail-value">{{ selectedSnapshot.id }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Name</div>
                            <div class="detail-value">{{ selectedSnapshot.name }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Type</div>
                            <div class="detail-value">{{ selectedSnapshot.type.charAt(0).toUpperCase() + selectedSnapshot.type.slice(1) }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Target</div>
                            <div class="detail-value">{{ selectedSnapshot.targetName }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Created</div>
                            <div class="detail-value">{{ selectedSnapshot.created }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Size</div>
                            <div class="detail-value">{{ selectedSnapshot.size }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">Description</div>
                            <div class="detail-value">{{ selectedSnapshot.description || 'No description provided' }}</div>
                        </div>
                    </div>
                    <div class="button-group mt-4">
                        <button class="btn btn-success" @click="restoreSnapshot(selectedSnapshot)">
                            <i class="fas fa-redo"></i> Restore
                        </button>
                        <button class="btn btn-danger" @click="deleteSnapshot(selectedSnapshot)">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>

            <!-- New Snapshot Form -->
            <div class="modal" v-if="showNewSnapshotForm">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Create New Snapshot</h3>
                        <button class="close-btn" @click="cancelCreateSnapshot">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="snapshot-name">Name</label>
                            <input type="text" id="snapshot-name" v-model="newSnapshot.name" required>
                        </div>
                        <div class="form-group">
                            <label for="snapshot-type">Type</label>
                            <select id="snapshot-type" v-model="newSnapshot.type">
                                <option value="system">System</option>
                                <option value="vm">Virtual Machine</option>
                                <option value="storage">Storage Pool</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="snapshot-target">Target</label>
                            <select id="snapshot-target" v-model="newSnapshot.target">
                                <option v-if="newSnapshot.type === 'system'" value="system">System</option>
                                <option v-if="newSnapshot.type === 'vm'"
                                    v-for="vm in virtualMachines" :key="'vm-' + vm.id" :value="vm.id">
                                    {{ vm.name }}
                                </option>
                                <option v-if="newSnapshot.type === 'storage'"
                                    v-for="pool in storagePools" :key="'pool-' + pool.id" :value="pool.id">
                                    {{ pool.name }}
                                </option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="snapshot-description">Description (Optional)</label>
                            <textarea id="snapshot-description" v-model="newSnapshot.description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" @click="cancelCreateSnapshot">Cancel</button>
                        <button class="btn btn-primary"
                            @click="createSnapshot"
                            :disabled="!newSnapshot.name || !newSnapshot.target">
                            Create
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `
};

// Settings Component
const SettingsComponent = {
    props: {
        systemSettings: Object
    },
    emits: ['refresh-data', 'update-settings'],
    data() {
        return {
            isLoading: false,
            settings: {
                hostname: '',
                timezone: '',
                allowUpdates: false,
                enableRemoteAccess: false,
                sshEnabled: false,
                httpEnabled: true,
                httpsEnabled: false
            }
        };
    },
    watch: {
        systemSettings: {
            handler(newVal) {
                if (newVal) {
                    this.settings = { ...newVal };
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        refreshData() {
            this.isLoading = true;
            this.$emit('refresh-data');

            setTimeout(() => {
                this.isLoading = false;
            }, 1000);
        },
        saveSettings() {
            this.$emit('update-settings', { ...this.settings });
        }
    },
    template: `
        <div id="settings-section" class="content-section">
            <div class="section-header">
                <div class="section-title">
                    <h2>System Settings</h2>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" @click="refreshData"
                        :class="{'spinning': isLoading}">
                        <i class="fas fa-sync" :class="{'fa-spin': isLoading}"></i> Refresh
                    </button>
                    <button class="btn btn-primary" @click="saveSettings">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>
            </div>

            <!-- General Settings Card -->
            <div class="card settings-card">
                <div class="card-header">
                    <h3>General Settings</h3>
                </div>
                <div class="card-body">
                    <div class="settings-form">
                        <div class="form-group">
                            <label for="setting-hostname">Hostname</label>
                            <input type="text" id="setting-hostname" v-model="settings.hostname">
                        </div>
                        <div class="form-group">
                            <label for="setting-timezone">Timezone</label>
                            <select id="setting-timezone" v-model="settings.timezone">
                                <option value="UTC">UTC</option>
                                <option value="America/New_York">America/New_York</option>
                                <option value="America/Chicago">America/Chicago</option>
                                <option value="America/Denver">America/Denver</option>
                                <option value="America/Los_Angeles">America/Los_Angeles</option>
                                <option value="Europe/London">Europe/London</option>
                                <option value="Europe/Berlin">Europe/Berlin</option>
                                <option value="Asia/Tokyo">Asia/Tokyo</option>
                                <option value="Australia/Sydney">Australia/Sydney</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Settings Card -->
            <div class="card settings-card">
                <div class="card-header">
                    <h3>Security Settings</h3>
                </div>
                <div class="card-body">
                    <div class="settings-form">
                        <div class="form-group toggle-group">
                            <div class="toggle-label">Enable SSH Access</div>
                            <label class="toggle-switch">
                                <input type="checkbox" v-model="settings.sshEnabled">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="form-group toggle-group">
                            <div class="toggle-label">Enable Remote Access</div>
                            <label class="toggle-switch">
                                <input type="checkbox" v-model="settings.enableRemoteAccess">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="form-group toggle-group">
                            <div class="toggle-label">HTTP Access</div>
                            <label class="toggle-switch">
                                <input type="checkbox" v-model="settings.httpEnabled">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="form-group toggle-group">
                            <div class="toggle-label">HTTPS Access</div>
                            <label class="toggle-switch">
                                <input type="checkbox" v-model="settings.httpsEnabled">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Update Settings Card -->
            <div class="card settings-card">
                <div class="card-header">
                    <h3>Update Settings</h3>
                </div>
                <div class="card-body">
                    <div class="settings-form">
                        <div class="form-group toggle-group">
                            <div class="toggle-label">Automatic Updates</div>
                            <label class="toggle-switch">
                                <input type="checkbox" v-model="settings.allowUpdates">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-secondary">
                                <i class="fas fa-search"></i> Check for Updates
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary">
                                <i class="fas fa-cloud-download-alt"></i> Install Updates
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};

// Main App Component
const App = {
    components: {
        SidebarComponent,
        DashboardComponent,
        VirtualMachineComponent,
        StorageComponent,
        NetworkComponent,
        SnapshotComponent,
        SettingsComponent
    },
    data() {
        return {
            activeSection: 'dashboard',
            sidebarCollapsed: false,
            isLoading: false,

            // Mock data for demo
            systemStats: {
                uptime: '3 days, 5 hours',
                version: 'PersistenceOS 6.1.0',
                hostname: 'persistenceos',
                cpuUsage: 15,
                memoryUsage: 35,
                storageUsage: 42
            },
            virtualMachines: [
                {
                    id: 'vm1',
                    name: 'Ubuntu Server',
                    os: 'linux',
                    status: 'running',
                    specs: {
                        cpu: 2,
                        memory: 4,
                        storage: 50
                    }
                },
                {
                    id: 'vm2',
                    name: 'Windows Server',
                    os: 'windows',
                    status: 'stopped',
                    specs: {
                        cpu: 4,
                        memory: 8,
                        storage: 100
                    }
                }
            ],
            storagePools: [
                {
                    id: 'pool1',
                    name: 'System Pool',
                    type: 'btrfs',
                    status: 'healthy',
                    capacity: '500 GB',
                    used: '150 GB',
                    usedPercent: 30
                },
                {
                    id: 'pool2',
                    name: 'Data Pool',
                    type: 'xfs',
                    status: 'healthy',
                    capacity: '2 TB',
                    used: '800 GB',
                    usedPercent: 40
                }
            ],
            storageDisks: [
                {
                    id: 'disk1',
                    device: '/dev/sda',
                    model: 'Samsung SSD 860 EVO',
                    size: '500 GB',
                    type: 'ssd',
                    status: 'in-use'
                },
                {
                    id: 'disk2',
                    device: '/dev/sdb',
                    model: 'WD Blue 2TB',
                    size: '2 TB',
                    type: 'hdd',
                    status: 'in-use'
                },
                {
                    id: 'disk3',
                    device: '/dev/sdc',
                    model: 'Seagate Barracuda 4TB',
                    size: '4 TB',
                    type: 'hdd',
                    status: 'available'
                }
            ],
            networkInterfaces: [
                {
                    name: 'eth0',
                    mac: '00:1B:44:11:3A:B7',
                    ipAddress: '*************',
                    netmask: '*************',
                    gateway: '***********',
                    dns1: '*******',
                    dns2: '*******',
                    status: 'up',
                    method: 'dhcp',
                    download: '2.5 MB/s',
                    upload: '1.2 MB/s',
                    totalDownload: '15.2 GB',
                    totalUpload: '5.4 GB'
                },
                {
                    name: 'eth1',
                    mac: '00:1B:44:11:3A:B8',
                    status: 'down',
                    download: '0 B/s',
                    upload: '0 B/s'
                }
            ],
            snapshots: [
                {
                    id: 'snap1',
                    name: 'Pre-update Snapshot',
                    type: 'system',
                    target: 'system',
                    targetName: 'System',
                    created: '2024-01-15 14:30',
                    size: '15 GB',
                    description: 'Snapshot taken before system update'
                },
                {
                    id: 'snap2',
                    name: 'Ubuntu Server Backup',
                    type: 'vm',
                    target: 'vm1',
                    targetName: 'Ubuntu Server',
                    created: '2024-01-16 09:45',
                    size: '25 GB',
                    description: 'Weekly backup of Ubuntu Server'
                }
            ],
            systemSettings: {
                hostname: 'persistenceos',
                timezone: 'UTC',
                allowUpdates: true,
                enableRemoteAccess: false,
                sshEnabled: true,
                httpEnabled: true,
                httpsEnabled: false
            },
            networkSettings: {
                domain: 'local',
                dnsDomain: 'local',
                dnsServers: ['*******', '*******']
            }
        };
    },
    methods: {
        toggleSidebar() {
            this.sidebarCollapsed = !this.sidebarCollapsed;
        },
        updateSection(section) {
            this.activeSection = section;
        },
        refreshData() {
            this.isLoading = true;

            // Simulate API call delay
            setTimeout(() => {
                this.isLoading = false;
                console.log('Data refreshed');
            }, 1000);
        },
        logout() {
            // Clear authentication
            localStorage.removeItem('persistenceos_token');
            localStorage.removeItem('persistenceos_user');
            localStorage.removeItem('authenticated');

            // Redirect to login page
            window.location.href = 'login.html';
        },

        // VM Actions
        startVM(vm) {
            console.log('Starting VM:', vm.name);
            const index = this.virtualMachines.findIndex(v => v.id === vm.id);
            if (index !== -1) {
                this.virtualMachines[index].status = 'running';
            }
        },
        stopVM(vm) {
            console.log('Stopping VM:', vm.name);
            const index = this.virtualMachines.findIndex(v => v.id === vm.id);
            if (index !== -1) {
                this.virtualMachines[index].status = 'stopped';
            }
        },
        restartVM(vm) {
            console.log('Restarting VM:', vm.name);
            // Simulate restart by briefly setting to 'stopped'
            const index = this.virtualMachines.findIndex(v => v.id === vm.id);
            if (index !== -1) {
                this.virtualMachines[index].status = 'stopped';
                setTimeout(() => {
                    this.virtualMachines[index].status = 'running';
                }, 2000);
            }
        },
        deleteVM(vm) {
            console.log('Deleting VM:', vm.name);
            this.virtualMachines = this.virtualMachines.filter(v => v.id !== vm.id);
        },
        createVM(vmData) {
            console.log('Creating VM:', vmData);
            const newVM = {
                id: 'vm' + (this.virtualMachines.length + 1),
                name: vmData.name,
                os: vmData.os,
                status: 'stopped',
                specs: { ...vmData.specs }
            };
            this.virtualMachines.push(newVM);
        },

        // Storage Actions
        createPool(poolData) {
            console.log('Creating storage pool:', poolData);
            const newPool = {
                id: 'pool' + (this.storagePools.length + 1),
                name: poolData.name,
                type: poolData.type,
                status: 'healthy',
                capacity: '4 TB',
                used: '0 GB',
                usedPercent: 0
            };
            this.storagePools.push(newPool);

            // Update disks status
            poolData.disks.forEach(diskId => {
                const index = this.storageDisks.findIndex(d => d.id === diskId);
                if (index !== -1) {
                    this.storageDisks[index].status = 'in-use';
                }
            });
        },
        deletePool(pool) {
            console.log('Deleting storage pool:', pool.name);
            this.storagePools = this.storagePools.filter(p => p.id !== pool.id);
        },
        addDiskToPool({ disk, pool }) {
            console.log('Adding disk to pool:', disk.device, 'to', pool.name);
            const diskIndex = this.storageDisks.findIndex(d => d.id === disk.id);
            if (diskIndex !== -1) {
                this.storageDisks[diskIndex].status = 'in-use';
            }
        },

        // Network Actions
        updateInterface({ name, config }) {
            console.log('Updating network interface:', name, config);
            const index = this.networkInterfaces.findIndex(i => i.name === name);
            if (index !== -1) {
                this.networkInterfaces[index] = {
                    ...this.networkInterfaces[index],
                    ...config
                };
            }
        },

        // Snapshot Actions
        createSnapshot(snapshotData) {
            console.log('Creating snapshot:', snapshotData);
            const targetName = snapshotData.type === 'system'
                ? 'System'
                : snapshotData.type === 'vm'
                    ? this.virtualMachines.find(vm => vm.id === snapshotData.target)?.name || 'Unknown'
                    : this.storagePools.find(pool => pool.id === snapshotData.target)?.name || 'Unknown';

            const newSnapshot = {
                id: 'snap' + (this.snapshots.length + 1),
                name: snapshotData.name,
                type: snapshotData.type,
                target: snapshotData.target,
                targetName,
                created: new Date().toLocaleString(),
                size: '10 GB',
                description: snapshotData.description
            };
            this.snapshots.push(newSnapshot);
        },
        restoreSnapshot(snapshot) {
            console.log('Restoring snapshot:', snapshot.name);
            // In a real app, this would trigger a snapshot restore operation
        },
        deleteSnapshot(snapshot) {
            console.log('Deleting snapshot:', snapshot.name);
            this.snapshots = this.snapshots.filter(s => s.id !== snapshot.id);
        },

        // Settings Actions
        updateSettings(settings) {
            console.log('Updating system settings:', settings);
            this.systemSettings = { ...settings };
        }
    },
    mounted() {
        // Authentication is handled by auth.js module
        console.log('Vue.js app mounted successfully');

        // Handle navigation via hash
        const handleHash = () => {
            const hash = window.location.hash.substring(1);
            if (hash && ['dashboard', 'vms', 'storage', 'snapshots', 'network', 'settings'].includes(hash)) {
                this.activeSection = hash;
            }
        };

        // Initial hash check
        handleHash();

        // Listen for hash changes
        window.addEventListener('hashchange', handleHash);

        // Initialize data refresh
        this.refreshData();
    },
    template: `
        <div class="app-container" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
            <sidebar-component
                :active-section="activeSection"
                :collapsed="sidebarCollapsed"
                @update-section="updateSection"
                @toggle-sidebar="toggleSidebar"
                @logout="logout"
            ></sidebar-component>

            <div class="main-content">
                <dashboard-component
                    v-show="activeSection === 'dashboard'"
                    :system-stats="systemStats"
                    :virtual-machines="virtualMachines"
                    :storage-pools="storagePools"
                    :network-interfaces="networkInterfaces"
                    @refresh-data="refreshData"
                ></dashboard-component>

                <virtual-machine-component
                    v-show="activeSection === 'vms'"
                    :virtual-machines="virtualMachines"
                    @refresh-data="refreshData"
                    @start-vm="startVM"
                    @stop-vm="stopVM"
                    @restart-vm="restartVM"
                    @delete-vm="deleteVM"
                    @create-vm="createVM"
                ></virtual-machine-component>

                <storage-component
                    v-show="activeSection === 'storage'"
                    :storage-pools="storagePools"
                    :storage-disks="storageDisks"
                    @refresh-data="refreshData"
                    @create-pool="createPool"
                    @delete-pool="deletePool"
                    @add-disk-to-pool="addDiskToPool"
                ></storage-component>

                <network-component
                    v-show="activeSection === 'network'"
                    :network-interfaces="networkInterfaces"
                    :network-settings="networkSettings"
                    @refresh-data="refreshData"
                    @update-interface="updateInterface"
                ></network-component>

                <snapshot-component
                    v-show="activeSection === 'snapshots'"
                    :snapshots="snapshots"
                    :virtual-machines="virtualMachines"
                    :storage-pools="storagePools"
                    @refresh-data="refreshData"
                    @create-snapshot="createSnapshot"
                    @restore-snapshot="restoreSnapshot"
                    @delete-snapshot="deleteSnapshot"
                ></snapshot-component>

                <settings-component
                    v-show="activeSection === 'settings'"
                    :system-settings="systemSettings"
                    @refresh-data="refreshData"
                    @update-settings="updateSettings"
                ></settings-component>
            </div>
        </div>
    `
};

// Initialize the Vue application
function initializeVueApp() {
    if (typeof Vue !== 'undefined' && Vue.createApp) {
        console.log('✅ Vue.js loaded successfully, initializing application...');

        try {
            const app = Vue.createApp(App);
            app.mount('#app');
            console.log('✅ Vue.js application mounted successfully');

            // Notify auth.js that Vue is ready
            window.dispatchEvent(new CustomEvent('vue-app-ready'));
        } catch (error) {
            console.error('❌ Error mounting Vue.js application:', error);
            document.getElementById('app').innerHTML = `
                <div style="text-align: center; padding: 50px; color: red;">
                    <h2>Application Error</h2>
                    <p>Failed to initialize the dashboard. Please refresh the page.</p>
                    <button onclick="window.location.reload()" style="padding: 10px 20px; margin-top: 10px;">Refresh Page</button>
                </div>
            `;
        }
    } else if (typeof createApp !== 'undefined') {
        console.log('✅ Vue.js createApp available, initializing application...');

        try {
            const app = createApp(App);
            app.mount('#app');
            console.log('✅ Vue.js application mounted successfully');

            // Notify auth.js that Vue is ready
            window.dispatchEvent(new CustomEvent('vue-app-ready'));
        } catch (error) {
            console.error('❌ Error mounting Vue.js application:', error);
            document.getElementById('app').innerHTML = `
                <div style="text-align: center; padding: 50px; color: red;">
                    <h2>Application Error</h2>
                    <p>Failed to initialize the dashboard. Please refresh the page.</p>
                    <button onclick="window.location.reload()" style="padding: 10px 20px; margin-top: 10px;">Refresh Page</button>
                </div>
            `;
        }
    } else {
        console.warn('⚠️ Vue.js not loaded yet, retrying in 500ms...');
        setTimeout(initializeVueApp, 500);
    }
}

// Wait for both DOM and authentication to be ready
function waitForReadiness() {
    console.log('🔄 Checking readiness for Vue.js initialization...');

    // Check if we're on app.html
    if (!window.location.pathname.endsWith('app.html')) {
        console.log('ℹ️ Not on app.html, skipping Vue initialization');
        return;
    }

    // Check if auth.js is available
    if (typeof window.authStatus === 'undefined') {
        console.log('⏳ Waiting for auth.js to initialize...');
        setTimeout(waitForReadiness, 100);
        return;
    }

    console.log('✅ Auth system ready, initializing Vue.js...');
    initializeVueApp();
}

document.addEventListener('DOMContentLoaded', () => {
    console.log('📄 DOM loaded, starting initialization sequence...');

    // Give auth.js a moment to initialize, then start Vue
    setTimeout(waitForReadiness, 200);
});
