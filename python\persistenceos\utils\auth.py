'''
FILE          : auth.py
PROJECT       : PersistenceOS
COPYRIGHT     : (c) 2024 PersistenceOS Team
AUTHOR        : PersistenceOS Team
PACKAGE       : PersistenceOS
LICENSE       : MIT
PURPOSE       : Authentication utility functions
'''

import os
import subprocess
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# In-memory storage for tokens (in production, use a proper database)
active_tokens = {}


def get_system_user(username: str) -> Optional[Dict[str, Any]]:
    """
    Get user information from system
    
    Args:
        username: The username to check
        
    Returns:
        Dict with user information or None if user doesn't exist
    """
    try:
        # Check if user exists using getent
        result = subprocess.run(
            ["getent", "passwd", username],
            capture_output=True, text=True, check=False
        )
        
        if result.returncode != 0:
            return None
            
        # User exists, create a user object
        # Format is usually: username:x:uid:gid:gecos:home:shell
        user_data = result.stdout.strip().split(':')
        if len(user_data) < 7:
            logger.warning(f"Invalid user data format for user {username}")
            return None
            
        return {
            "username": user_data[0],
            "uid": int(user_data[2]),
            "gid": int(user_data[3]),
            "full_name": user_data[4],
            "home_dir": user_data[5],
            "shell": user_data[6],
            # Determine if user is admin (root or in wheel/sudo group)
            "is_admin": user_data[0] == "root" or is_user_in_admin_group(username)
        }
    except Exception as e:
        logger.error(f"Error retrieving system user {username}: {e}")
        return None


def is_user_in_admin_group(username: str) -> bool:
    """
    Check if user is in an admin group (wheel or sudo)
    
    Args:
        username: The username to check
        
    Returns:
        True if user is in admin group, False otherwise
    """
    try:
        # Check if user is in wheel or sudo group
        for group in ["wheel", "sudo"]:
            result = subprocess.run(
                ["getent", "group", group],
                capture_output=True, text=True, check=False
            )
            
            if result.returncode == 0:
                # Group exists, check if user is a member
                group_data = result.stdout.strip().split(':')
                if len(group_data) >= 4:
                    members = group_data[3].split(',')
                    if username in members:
                        return True
        
        return False
    except Exception as e:
        logger.error(f"Error checking admin group membership for {username}: {e}")
        return False


def authenticate_user(username: str, password: str) -> Optional[Dict[str, Any]]:
    """
    Authenticate a user against the system
    
    Args:
        username: The username to authenticate
        password: The password to authenticate with
        
    Returns:
        Dict with user information if authentication successful, None otherwise
    """
    # For development/testing only - allow root/linux in all environments
    if username == "root" and password == "linux":
        return {
            "username": "root",
            "display_name": "Administrator",
            "roles": ["admin"],
            "is_admin": True
        }
        
    # In production, we would use PAM for authentication
    # This is a placeholder for real authentication
    try:
        user = get_system_user(username)
        if not user:
            return None
            
        # TODO: In production implementation, use PAM to validate password
        # For now, we're not doing real password validation
            
        # Set up roles based on admin status
        roles = ["admin"] if user.get("is_admin", False) else ["user"]
        
        return {
            "username": user["username"],
            "display_name": user.get("full_name", user["username"]),
            "roles": roles,
            "is_admin": user.get("is_admin", False)
        }
    except Exception as e:
        logger.error(f"Authentication error for user {username}: {e}")
        return None


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> Dict[str, Any]:
    """
    Create an access token for authentication
    
    Args:
        data: The data to encode in the token
        expires_delta: Optional expiration time delta
        
    Returns:
        Dict with token details
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=1)
        
    to_encode.update({"exp": expire})
    
    # In a real implementation, this would use JWT or similar
    # For our simplified version, we're using a random string
    token = f"token-{os.urandom(16).hex()}"
    expires_at = int(expire.timestamp() * 1000)  # milliseconds
    
    # Store token in memory (in production, this would be in a database)
    active_tokens[token] = {
        "data": to_encode,
        "expires_at": expires_at
    }
    
    return {
        "access_token": token,
        "token_type": "bearer",
        "expires_at": expires_at
    }


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify an access token
    
    Args:
        token: The token to verify
        
    Returns:
        Dict with token data if valid, None otherwise
    """
    if token not in active_tokens:
        return None
        
    token_data = active_tokens[token]
    
    # Check if token is expired
    now = datetime.utcnow()
    if now.timestamp() * 1000 > token_data["expires_at"]:
        # Remove expired token
        del active_tokens[token]
        return None
        
    return token_data["data"]


def revoke_token(token: str) -> bool:
    """
    Revoke an access token
    
    Args:
        token: The token to revoke
        
    Returns:
        True if token was revoked, False otherwise
    """
    if token in active_tokens:
        del active_tokens[token]
        return True
    return False 