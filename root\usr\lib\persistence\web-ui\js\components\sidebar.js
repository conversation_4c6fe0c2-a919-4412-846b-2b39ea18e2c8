// Sidebar Component
app.component('Sidebar', {
    props: {
        activeSection: String,
        collapsed: Boolean
    },
    emits: ['update-section', 'toggle-sidebar', 'logout'],
    template: `
        <div class="sidebar" :class="{ 'collapsed': collapsed }">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="hamburger-menu" @click="$emit('toggle-sidebar')">
                        <i class="fas fa-bars"></i>
                    </div>
                    <h1>PersistenceOS</h1>
                </div>
                <div class="logo-icon">
                    <i class="fas fa-server"></i>
                </div>
            </div>

            <!-- Navigation -->
            <div class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item" :class="{ 'active': activeSection === 'dashboard' }" 
                        @click="$emit('update-section', 'dashboard')">
                        <a href="#dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            <span class="nav-label">Overview</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'vms' }"
                        @click="$emit('update-section', 'vms')">
                        <a href="#vms">
                            <i class="fas fa-server"></i>
                            <span class="nav-label">Virtualization</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'storage' }"
                        @click="$emit('update-section', 'storage')">
                        <a href="#storage">
                            <i class="fas fa-hdd"></i>
                            <span class="nav-label">Storage</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'snapshots' }"
                        @click="$emit('update-section', 'snapshots')">
                        <a href="#snapshots">
                            <i class="fas fa-camera"></i>
                            <span class="nav-label">Snapshots</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'network' }"
                        @click="$emit('update-section', 'network')">
                        <a href="#network">
                            <i class="fas fa-network-wired"></i>
                            <span class="nav-label">Network</span>
                        </a>
                    </li>
                    <li class="nav-item" :class="{ 'active': activeSection === 'settings' }"
                        @click="$emit('update-section', 'settings')">
                        <a href="#settings">
                            <i class="fas fa-cog"></i>
                            <span class="nav-label">Settings</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- User Info at bottom -->
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <span class="avatar-text">R</span>
                    </div>
                    <div class="user-details">
                        <span id="user-name">root</span>
                        <a href="#" @click.prevent="$emit('logout')" class="btn-link">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                </div>
                <div class="system-status">
                    <div class="status-indicator healthy"></div>
                    <span>System Healthy</span>
                </div>
            </div>
        </div>
    `
}); 