#!/bin/bash
set -e
set -o pipefail

# --- 0. <PERSON><PERSON><PERSON> Handling and Logging ---
log_info() {
    echo "[INFO] $1"
}

log_error() {
    echo "[ERROR] $1" >&2
    exit 1
}

# --- 1. Define Paths ---
PERSISTENCE_ROOT="/usr/lib/persistence"
BIN="${PERSISTENCE_ROOT}/bin"
SERVICES="${PERSISTENCE_ROOT}/services"
WEBUI="${PERSISTENCE_ROOT}/web-ui"
LOGDIR="/var/log/persistence"

# --- FUNCTION DEFINITIONS (must be defined before use) ---

# --- Self-contained login.js deployment function ---
deploy_self_contained_login() {
    log_info "Deploying self-contained login.js (no HTML dependencies)..."

    # The self-contained login.js will be handled by setup_static_directory()
    # This function is now a placeholder for any login-specific setup

    # Create minimal login endpoint file for web server compatibility
    mkdir -p "/var/lib/persistence/web-ui" "/usr/lib/persistence/web-ui"

    # Create a minimal login.html that just loads the self-contained login.js
    cat > "/var/lib/persistence/web-ui/login.html" <<'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login</title>
    <link rel="icon" href="img/favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- Self-contained login.js will create the entire page -->
    <script src="js/login.js"></script>
</body>
</html>
EOF

    # Copy to usr location as well
    cp "/var/lib/persistence/web-ui/login.html" "/usr/lib/persistence/web-ui/login.html"

    # Set permissions
    chmod 644 "/var/lib/persistence/web-ui/login.html" "/usr/lib/persistence/web-ui/login.html"

    log_info "✅ Self-contained login deployment complete"
}

# --- Function to create minimal fallback app.js for debugging ---
create_minimal_fallback_app_js() {
    log_info "Creating minimal fallback app.js for debugging file detection issues..."

    cat > "$WEBUI/js/app.js" <<'EOF'
/**
 * FALLBACK app.js - File Detection Issue
 * This is a minimal fallback created because the original app.js was not found.
 * This indicates a build process issue that needs to be resolved.
 */

console.error('🚨 FALLBACK app.js loaded - Original app.js file not found during build');
console.log('📁 Expected app.js locations that were checked:');
console.log('  - app.js (OBS root)');
console.log('  - /usr/src/packages/SOURCES/app.js');
console.log('  - /usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/app.js');
console.log('  - usr/lib/persistence/web-ui/js/app.js');

// Create a simple diagnostic interface
document.addEventListener('DOMContentLoaded', function() {
    const appElement = document.getElementById('app');
    if (appElement) {
        appElement.innerHTML = `
            <div style="padding: 20px; font-family: Arial, sans-serif;">
                <h1 style="color: #d32f2f;">⚠️ PersistenceOS - File Detection Issue</h1>
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h3>🔧 Build Process Issue Detected</h3>
                    <p>The original <code>app.js</code> file was not found during the build process.</p>
                    <p>This fallback interface was created to prevent build failure.</p>
                </div>

                <div style="background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>📋 Troubleshooting Steps:</h4>
                    <ol>
                        <li>Verify <code>app.js</code> is uploaded to OBS source files</li>
                        <li>Check OBS build logs for file placement details</li>
                        <li>Ensure file permissions allow reading during build</li>
                        <li>Verify KIWI working directory during build process</li>
                        <li>Check if files are in expected locations:
                            <ul>
                                <li><code>app.js</code> (OBS root)</li>
                                <li><code>usr/lib/persistence/web-ui/js/app.js</code> (nested)</li>
                            </ul>
                        </li>
                    </ol>
                </div>

                <div style="background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4>🎯 Next Steps:</h4>
                    <p>1. <strong>Fix the file detection issue</strong> rather than using embedded code</p>
                    <p>2. <strong>Rebuild the image</strong> after ensuring proper file placement</p>
                    <p>3. <strong>Verify the full dashboard loads</strong> with proper Vue.js components</p>
                </div>

                <button onclick="window.location.reload()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                    🔄 Reload Page
                </button>

                <button onclick="window.location.href='/login.html'" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
                    🔙 Back to Login
                </button>
            </div>
        `;
    }
});
EOF

    log_info "✅ Created minimal fallback app.js for debugging file detection issues"

    # Verify the file was created successfully
    if [ -f "$WEBUI/js/app.js" ]; then
        local file_size=$(stat -c%s "$WEBUI/js/app.js" 2>/dev/null || echo "unknown")
        log_info "✅ Fallback app.js created successfully: $WEBUI/js/app.js ($file_size bytes)"
    else
        log_info "❌ Failed to create fallback app.js file"
    fi
}

# --- Function to setup static directory structure properly ---
setup_static_directory() {
    log_info "Setting up static directory structure for web UI with Vue.js support"

    # Create static directory if it doesn't exist
    mkdir -p "$WEBUI/static" || log_info "Warning: Failed to create static directory"
    mkdir -p "$WEBUI/static/css" "$WEBUI/static/js" "$WEBUI/static/img" || log_info "Warning: Failed to create static subdirectories"

    # Create components directory for Vue.js
    mkdir -p "$WEBUI/js/components" || log_info "Warning: Failed to create components directory"

    # Note: Vue.js installation is now handled by the comprehensive search logic below
    # This section has been consolidated with the enhanced JavaScript file detection

    # Install app.js from source
    log_info "Installing app.js Vue application..."

    # Enhanced debugging for OBS file placement with additional search locations
    log_info "=== ENHANCED DEBUGGING FOR OBS FILE PLACEMENT ==="
    log_info "Current working directory: $(pwd)"
    log_info "Current directory contents:"
    ls -la | head -20 | while read line; do log_info "  $line"; done

    # Check for app.js specifically in current directory
    if [ -f "app.js" ]; then
        log_info "✅ app.js found in current directory: $(pwd)/app.js"
        log_info "   File size: $(stat -c%s "app.js" 2>/dev/null || echo "unknown") bytes"
        log_info "   File permissions: $(stat -c%A "app.js" 2>/dev/null || echo "unknown")"
    else
        log_info "❌ app.js NOT found in current directory: $(pwd)/app.js"
    fi

    # Check SOURCES directory
    if [ -d "/usr/src/packages/SOURCES" ]; then
        log_info "SOURCES directory exists: /usr/src/packages/SOURCES"
        log_info "SOURCES directory contents:"
        ls -la /usr/src/packages/SOURCES | head -20 | while read line; do log_info "  $line"; done

        # Check for specific JavaScript files in SOURCES
        for js_file in "app.js" "auth.js" "vue.js" "login.js"; do
            if [ -f "/usr/src/packages/SOURCES/$js_file" ]; then
                file_size=$(stat -c%s "/usr/src/packages/SOURCES/$js_file" 2>/dev/null || echo "unknown")
                file_perms=$(stat -c%A "/usr/src/packages/SOURCES/$js_file" 2>/dev/null || echo "unknown")
                log_info "✅ Found $js_file in SOURCES: $file_size bytes, permissions: $file_perms"
            else
                log_info "❌ Missing $js_file in SOURCES"
            fi
        done

        # Check for @ prefix files (OBS notation)
        for js_file in "@app.js" "@auth.js" "@vue.js" "@login.js"; do
            if [ -f "/usr/src/packages/SOURCES/$js_file" ]; then
                file_size=$(stat -c%s "/usr/src/packages/SOURCES/$js_file" 2>/dev/null || echo "unknown")
                log_info "✅ Found OBS @ prefix file: $js_file ($file_size bytes)"
            fi
        done

        # Check for nested structure in SOURCES
        if [ -d "/usr/src/packages/SOURCES/usr" ]; then
            log_info "Found nested usr directory in SOURCES"
            find /usr/src/packages/SOURCES/usr -name "*.js" -type f 2>/dev/null | while read jsfile; do
                log_info "  Found JS file: $jsfile ($(stat -c%s "$jsfile" 2>/dev/null || echo "unknown") bytes)"
            done
        fi
    else
        log_info "SOURCES directory does not exist: /usr/src/packages/SOURCES"
    fi

    # Check additional OBS-specific locations where files might be placed
    log_info "Checking additional OBS-specific locations:"
    for obs_dir in "/usr/src/packages/BUILD" "/usr/src/packages/BUILDROOT" "/usr/src/packages/RPMS" "/usr/src/packages/SRPMS" "/home/<USER>/rpmbuild/SOURCES"; do
        if [ -d "$obs_dir" ]; then
            log_info "Checking OBS directory: $obs_dir"
            find "$obs_dir" -name "*.js" -type f 2>/dev/null | head -5 | while read jsfile; do
                log_info "  Found JS file: $jsfile ($(stat -c%s "$jsfile" 2>/dev/null || echo "unknown") bytes)"
            done
        else
            log_info "OBS directory does not exist: $obs_dir"
        fi
    done

    # Check for any JavaScript files in the build environment
    log_info "Searching for all .js files in common build locations:"
    for search_dir in "$(pwd)" "/usr/src/packages" "/image" "/tmp" "/var/tmp" "/home/<USER>"; do
        if [ -d "$search_dir" ]; then
            log_info "Searching in: $search_dir"
            find "$search_dir" -name "*.js" -type f 2>/dev/null | head -10 | while read jsfile; do
                log_info "  Found: $jsfile ($(stat -c%s "$jsfile" 2>/dev/null || echo "unknown") bytes)"
            done
        fi
    done
    log_info "=== END ENHANCED DEBUGGING ==="

    # Debug: Check for app.js specifically in various locations including OBS source paths
    log_info "Checking for app.js specifically:"
    for location in "$(pwd)/app.js" "/usr/src/packages/SOURCES/app.js" "/image/app.js" "app.js" "/usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/app.js" "usr/lib/persistence/web-ui/js/app.js"; do
        if [ -f "$location" ]; then
            log_info "  ✅ FOUND: $location (size: $(stat -c%s "$location" 2>/dev/null || echo "unknown") bytes)"
        else
            log_info "  ❌ NOT FOUND: $location"
        fi
    done

    # Ensure target directory exists
    log_info "Ensuring target directory exists: $WEBUI/js/"
    mkdir -p "$WEBUI/js" || log_info "Warning: Failed to create target directory: $WEBUI/js"

    if [ -d "$WEBUI/js" ]; then
        log_info "✅ Target directory confirmed: $WEBUI/js"
        log_info "   Directory permissions: $(ls -ld "$WEBUI/js" 2>/dev/null || echo "unknown")"
    else
        log_info "❌ Target directory does not exist: $WEBUI/js"
    fi

    # Try to copy app.js from the first available location (including OBS source paths)
    APP_JS_COPIED=false

    # Define all possible source locations for app.js (expanded for OBS)
    APP_JS_LOCATIONS=(
        "/usr/src/packages/SOURCES/app.js"                        # Standard OBS SOURCES (priority)
        "app.js"                                                    # Current directory (OBS root)
        "$(pwd)/app.js"                                            # Explicit current directory
        "/usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/app.js"  # OBS nested path
        "usr/lib/persistence/web-ui/js/app.js"                    # Relative nested path
        "/image/app.js"                                            # Image directory
        "/usr/src/packages/BUILD/app.js"                          # OBS BUILD directory
        "/usr/src/packages/BUILDROOT/app.js"                      # OBS BUILDROOT directory
        "/home/<USER>/rpmbuild/SOURCES/app.js"                    # Alternative OBS SOURCES
        "/home/<USER>/app.js"                                      # abuild home directory
        "./usr/lib/persistence/web-ui/js/app.js"                  # Relative path with dot
        "usr/src/packages/SOURCES/app.js"                         # Local development SOURCES (corrected)
        "usr/src/pacakges/SOURCES/app.js"                         # Local development SOURCES (with typo)
        "/var/tmp/build-root/usr/src/packages/SOURCES/app.js"     # Alternative OBS build path
        "/usr/src/packages/SOURCES/@app.js"                       # OBS @ prefix notation
        "/usr/src/packages/SOURCES/@usr/lib/persistence/web-ui/js/app.js"  # OBS @ prefix nested
    )

    # Try each location in order
    for source_path in "${APP_JS_LOCATIONS[@]}"; do
        if [ -f "$source_path" ]; then
            log_info "✅ FOUND app.js at: $source_path"
            source_size=$(stat -c%s "$source_path" 2>/dev/null || echo "unknown")
            source_perms=$(stat -c%A "$source_path" 2>/dev/null || echo "unknown")
            log_info "   File details: $source_size bytes, permissions: $source_perms"
            log_info "Attempting to copy app.js from: $source_path"

            if cp "$source_path" "$WEBUI/js/app.js" 2>/dev/null; then
                target_size=$(stat -c%s "$WEBUI/js/app.js" 2>/dev/null || echo "unknown")
                log_info "✅ app.js installed successfully from: $source_path"
                log_info "   Source: $source_path ($source_size bytes)"
                log_info "   Target: $WEBUI/js/app.js ($target_size bytes)"

                # Verify the copy was successful
                if [ "$source_size" = "$target_size" ] && [ "$target_size" != "unknown" ]; then
                    log_info "✅ File copy verification: sizes match ($target_size bytes)"
                else
                    log_info "⚠️  File copy verification: size mismatch (source: $source_size, target: $target_size)"
                fi

                APP_JS_COPIED=true
                break
            else
                copy_error=$?
                log_info "❌ Failed to copy app.js from: $source_path (exit code: $copy_error)"
                log_info "   Check file permissions and target directory write access"
            fi
        else
            log_info "❌ NOT FOUND: $source_path"
        fi
    done

    # If no file was found, this is a critical error that should be addressed
    if [ "$APP_JS_COPIED" = "false" ]; then
        log_info "❌ CRITICAL: app.js not found in any expected location"
        log_info "Checked locations:"
        for location in "${APP_JS_LOCATIONS[@]}"; do
            log_info "  - $location"
        done
        log_info ""
        log_info "🔧 TROUBLESHOOTING STEPS:"
        log_info "1. Verify app.js is uploaded to OBS source files"
        log_info "2. Check OBS build logs for file placement"
        log_info "3. Ensure file permissions allow reading"
        log_info "4. Verify KIWI working directory during build"
        log_info ""
        log_info "⚠️  Creating minimal fallback app.js to prevent build failure..."

        # Create a minimal fallback that shows the error and debugging info
        create_minimal_fallback_app_js
        APP_JS_COPIED=true
    fi

    # Install auth.js from source if available (using comprehensive search)
    log_info "Installing auth.js authentication library..."
    AUTH_JS_LOCATIONS=(
        "/usr/src/packages/SOURCES/auth.js"                        # Standard OBS SOURCES (priority)
        "auth.js"                                                    # Current directory (OBS root)
        "$(pwd)/auth.js"                                            # Explicit current directory
        "/usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/auth.js"  # OBS nested path
        "usr/lib/persistence/web-ui/js/auth.js"                    # Relative nested path
        "/image/auth.js"                                            # Image directory
        "/usr/src/packages/BUILD/auth.js"                          # OBS BUILD directory
        "/usr/src/packages/BUILDROOT/auth.js"                      # OBS BUILDROOT directory
        "/home/<USER>/rpmbuild/SOURCES/auth.js"                    # Alternative OBS SOURCES
        "/home/<USER>/auth.js"                                      # abuild home directory
        "./usr/lib/persistence/web-ui/js/auth.js"                  # Relative path with dot
        "usr/src/packages/SOURCES/auth.js"                         # Local development SOURCES
        "/var/tmp/build-root/usr/src/packages/SOURCES/auth.js"     # Alternative OBS build path
        "/usr/src/packages/SOURCES/@auth.js"                       # OBS @ prefix notation
        "/usr/src/packages/SOURCES/@usr/lib/persistence/web-ui/js/auth.js"  # OBS @ prefix nested
    )

    AUTH_JS_COPIED=false
    for source_path in "${AUTH_JS_LOCATIONS[@]}"; do
        if [ -f "$source_path" ]; then
            log_info "Attempting to copy auth.js from: $source_path"
            if cp "$source_path" "$WEBUI/js/auth.js"; then
                log_info "✅ auth.js installed successfully from: $source_path"
                AUTH_JS_COPIED=true
                break
            else
                log_info "❌ Failed to copy auth.js from: $source_path"
            fi
        fi
    done

    if [ "$AUTH_JS_COPIED" = "false" ]; then
        log_info "⚠️  auth.js not found in any location, will be handled by app.js"
    fi

    # Install vue.js from source if available (using comprehensive search)
    log_info "Installing vue.js library..."
    VUE_JS_LOCATIONS=(
        "/usr/src/packages/SOURCES/vue.js"                        # Standard OBS SOURCES (priority)
        "vue.js"                                                    # Current directory (OBS root)
        "$(pwd)/vue.js"                                            # Explicit current directory
        "/usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/vue.js"  # OBS nested path
        "usr/lib/persistence/web-ui/js/vue.js"                    # Relative nested path
        "/image/vue.js"                                            # Image directory
        "/usr/src/packages/BUILD/vue.js"                          # OBS BUILD directory
        "/usr/src/packages/BUILDROOT/vue.js"                      # OBS BUILDROOT directory
        "/home/<USER>/rpmbuild/SOURCES/vue.js"                    # Alternative OBS SOURCES
        "/home/<USER>/vue.js"                                      # abuild home directory
        "./usr/lib/persistence/web-ui/js/vue.js"                  # Relative path with dot
        "usr/src/packages/SOURCES/vue.js"                         # Local development SOURCES
        "/var/tmp/build-root/usr/src/packages/SOURCES/vue.js"     # Alternative OBS build path
        "/usr/src/packages/SOURCES/@vue.js"                       # OBS @ prefix notation
        "/usr/src/packages/SOURCES/@usr/lib/persistence/web-ui/js/vue.js"  # OBS @ prefix nested
    )

    VUE_JS_COPIED=false
    for source_path in "${VUE_JS_LOCATIONS[@]}"; do
        if [ -f "$source_path" ]; then
            log_info "Attempting to copy vue.js from: $source_path"
            if cp "$source_path" "$WEBUI/js/vue.js"; then
                log_info "✅ vue.js installed successfully from: $source_path"
                VUE_JS_COPIED=true
                break
            else
                log_info "❌ Failed to copy vue.js from: $source_path"
            fi
        fi
    done

    if [ "$VUE_JS_COPIED" = "false" ]; then
        log_info "⚠️  vue.js not found in any location, will use CDN fallback"
    fi

    # Install login.js from source if available (using comprehensive search)
    log_info "Installing login.js authentication script..."
    LOGIN_JS_LOCATIONS=(
        "/usr/src/packages/SOURCES/login.js"                        # Standard OBS SOURCES (priority)
        "login.js"                                                    # Current directory (OBS root)
        "$(pwd)/login.js"                                            # Explicit current directory
        "/usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/login.js"  # OBS nested path
        "usr/lib/persistence/web-ui/js/login.js"                    # Relative nested path
        "/image/login.js"                                            # Image directory
        "/usr/src/packages/BUILD/login.js"                          # OBS BUILD directory
        "/usr/src/packages/BUILDROOT/login.js"                      # OBS BUILDROOT directory
        "/home/<USER>/rpmbuild/SOURCES/login.js"                    # Alternative OBS SOURCES
        "/home/<USER>/login.js"                                      # abuild home directory
        "./usr/lib/persistence/web-ui/js/login.js"                  # Relative path with dot
        "usr/src/packages/SOURCES/login.js"                         # Local development SOURCES
        "/var/tmp/build-root/usr/src/packages/SOURCES/login.js"     # Alternative OBS build path
        "/usr/src/packages/SOURCES/@login.js"                       # OBS @ prefix notation
        "/usr/src/packages/SOURCES/@usr/lib/persistence/web-ui/js/login.js"  # OBS @ prefix nested
    )

    LOGIN_JS_COPIED=false
    for source_path in "${LOGIN_JS_LOCATIONS[@]}"; do
        if [ -f "$source_path" ]; then
            log_info "Attempting to copy login.js from: $source_path"
            if cp "$source_path" "$WEBUI/js/login.js"; then
                log_info "✅ login.js installed successfully from: $source_path"
                log_info "   Source: $source_path ($(stat -c%s "$source_path" 2>/dev/null || echo "unknown") bytes)"
                log_info "   Target: $WEBUI/js/login.js ($(stat -c%s "$WEBUI/js/login.js" 2>/dev/null || echo "unknown") bytes)"
                LOGIN_JS_COPIED=true
                break
            else
                log_info "❌ Failed to copy login.js from: $source_path"
            fi
        fi
    done

    if [ "$LOGIN_JS_COPIED" = "false" ]; then
        log_info "⚠️  login.js not found in any location, creating self-contained fallback..."

        # Create a self-contained login.js fallback that includes HTML layout
        cat << 'EOF' > "$WEBUI/js/login.js"
/**
 * PersistenceOS Self-Contained Login Script - FALLBACK VERSION
 * This fallback is created when the main login.js file is not found during build.
 * It provides complete login functionality with embedded HTML layout.
 */

// Configuration
const LOGIN_CONFIG = {
    API_BASE_URL: '/api',
    REDIRECT_URL: '/app.html',
    DEFAULT_USERNAME: 'root',
    DEFAULT_PASSWORD: 'linux',
    DEBUG: true,
    STANDALONE_MODE: true
};

// HTML Layout Generator
const LoginHTML = {
    createFullPage() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login</title>
    <link rel="icon" href="img/favicon.ico" type="image/x-icon">
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0a4b78 0%, #003366 100%);
            height: 100vh; display: flex; justify-content: center; align-items: center; color: #333;
        }
        .login-container { width: 400px; max-width: 95%; }
        .login-card { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.2); }
        .login-header { background: #0066cc; color: white; padding: 30px 20px; text-align: center; }
        .login-header h1 { margin: 0 0 5px 0; font-size: 28px; }
        .login-header .version { margin: 5px 0 0 0; opacity: 0.8; font-size: 14px; }
        .logo { width: 60px; height: 60px; margin-bottom: 10px; }
        .login-form { padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; font-size: 14px; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .remember-me { display: flex; align-items: center; }
        .remember-me input { margin-right: 8px; }
        .btn { padding: 12px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; transition: background 0.3s; }
        .btn-primary { background: #0066cc; color: white; }
        .btn-primary:hover { background: #0055aa; }
        .login-footer { text-align: center; padding: 20px; border-top: 1px solid #eee; font-size: 12px; color: #777; }
        .copyright { margin-top: 10px; font-size: 11px; }
        .error-message { color: #e74c3c; margin-top: 10px; text-align: center; padding: 8px; border-radius: 4px; background-color: rgba(231, 76, 60, 0.1); }
        .hidden { display: none !important; }
    </style>
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div style="width: 60px; height: 60px; margin: 0 auto 10px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 24px; color: #0066cc; font-weight: bold;">+</div>
                <h1>PersistenceOS</h1>
                <p class="version">Version: 6.1.0</p>
            </div>
            <div class="login-form">
                <form id="login-form" action="javascript:void(0);">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" placeholder="root" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" placeholder="Enter password" required>
                    </div>
                    <div class="form-group">
                        <div class="remember-me">
                            <input type="checkbox" id="remember" name="remember">
                            <label for="remember">Remember me</label>
                        </div>
                    </div>
                    <div id="login-error" class="error-message hidden">Invalid username or password</div>
                    <div class="form-group">
                        <button type="button" id="login-button" class="btn btn-primary" style="width: 100%;">Log In</button>
                    </div>
                </form>
            </div>
            <div class="login-footer">
                <p>PersistenceOS is a specialized hypervisor and NAS operating system based on SUSE Micro Leap 6.1.</p>
                <p>Default login: <strong>root</strong> / <strong>linux</strong> (same as system login)</p>
                <p class="copyright">&copy; 2024 PersistenceOS Team</p>
            </div>
        </div>
    </div>
</body>
</html>`;
    },

    injectIntoCurrentPage() {
        document.open();
        document.write(this.createFullPage());
        document.close();
        setTimeout(() => this.initializeAfterInjection(), 100);
    },

    initializeAfterInjection() {
        if (typeof LoginManager !== 'undefined') {
            LoginManager.initializeLoginPage();
        }
    }
};

// Utility functions
const Logger = {
    log: (message, ...args) => { if (LOGIN_CONFIG.DEBUG) console.log(`🔐 [Fallback] ${message}`, ...args); },
    error: (message, ...args) => console.error(`❌ [Fallback] ${message}`, ...args),
    success: (message, ...args) => { if (LOGIN_CONFIG.DEBUG) console.log(`✅ [Fallback] ${message}`, ...args); }
};

// Authentication utilities
const Auth = {
    storeAuth: (data, rememberMe = false) => {
        const storage = rememberMe ? localStorage : sessionStorage;
        storage.setItem('persistenceos_token', data.access_token);
        storage.setItem('persistenceos_user', JSON.stringify(data.user || {}));
        storage.setItem('persistenceos_token_expiry', data.expires_at || '');
        storage.setItem('authenticated', 'true');
        storage.setItem('username', data.username || '');
        Logger.success('Authentication data stored', { rememberMe });
    },
    clearAuth: () => {
        ['localStorage', 'sessionStorage'].forEach(storageType => {
            const storage = window[storageType];
            ['persistenceos_token', 'persistenceos_user', 'persistenceos_token_expiry', 'authenticated', 'username'].forEach(key => storage.removeItem(key));
        });
        Logger.log('Authentication data cleared');
    },
    isAuthenticated: () => {
        const localAuth = localStorage.getItem('authenticated') === 'true';
        const sessionAuth = sessionStorage.getItem('authenticated') === 'true';
        return localAuth || sessionAuth;
    }
};

// UI utilities
const UI = {
    showError: (message) => {
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }
        Logger.error('UI Error:', message);
    },
    hideError: () => {
        const errorElement = document.getElementById('login-error');
        if (errorElement) errorElement.classList.add('hidden');
    },
    showStatus: (message) => {
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.color = '#28a745';
            errorElement.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
            errorElement.classList.remove('hidden');
        }
        Logger.log('Status:', message);
    },
    hideStatus: () => {
        const errorElement = document.getElementById('login-error');
        if (errorElement) {
            errorElement.classList.add('hidden');
            errorElement.style.color = '#e74c3c';
            errorElement.style.backgroundColor = 'rgba(231, 76, 60, 0.1)';
        }
    },
    setButtonLoading: (loading = true) => {
        const button = document.getElementById('login-button');
        if (button) {
            button.disabled = loading;
            button.textContent = loading ? 'Logging in...' : 'Log In';
        }
    }
};

// Main login functionality
const LoginManager = {
    async authenticateWithAPI(username, password) {
        Logger.log('Attempting API authentication...');
        const response = await fetch(`${LOGIN_CONFIG.API_BASE_URL}/auth/token`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: new URLSearchParams({ 'username': username, 'password': password, 'grant_type': 'password' })
        });
        Logger.log(`API response status: ${response.status}`);
        if (response.ok) {
            const data = await response.json();
            data.username = username;
            return { success: true, data };
        } else {
            const errorData = await response.json().catch(() => ({}));
            return { success: false, error: errorData.detail || 'Invalid username or password' };
        }
    },
    authenticateWithFallback(username, password) {
        Logger.log('Using fallback authentication...');
        if (username === LOGIN_CONFIG.DEFAULT_USERNAME && password === LOGIN_CONFIG.DEFAULT_PASSWORD) {
            return {
                success: true,
                data: {
                    access_token: 'fallback_token',
                    username: username,
                    expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
                }
            };
        }
        return { success: false, error: 'Invalid credentials' };
    },
    redirectToApp() {
        Logger.success('Redirecting to application...');
        UI.showStatus('Login successful! Redirecting...');
        setTimeout(() => { window.location.href = LOGIN_CONFIG.REDIRECT_URL; }, 1000);
    },
    async handleLogin(username, password, rememberMe = false) {
        Logger.log('Starting login process...', { username, rememberMe });
        UI.hideError(); UI.showStatus('Logging in...'); UI.setButtonLoading(true);
        try {
            let result = await this.authenticateWithAPI(username, password);
            if (!result.success) {
                Logger.log('API authentication failed, trying fallback...');
                result = this.authenticateWithFallback(username, password);
            }
            if (result.success) {
                Auth.storeAuth(result.data, rememberMe);
                this.redirectToApp();
            } else {
                UI.showError(result.error); UI.hideStatus();
            }
        } catch (error) {
            Logger.error('Login process failed:', error);
            const fallbackResult = this.authenticateWithFallback(username, password);
            if (fallbackResult.success) {
                Logger.log('Network error, but fallback succeeded');
                Auth.storeAuth(fallbackResult.data, rememberMe);
                UI.showStatus('Network error, using offline mode...');
                setTimeout(() => this.redirectToApp(), 2000);
            } else {
                UI.showError('Connection error. Please try again.'); UI.hideStatus();
            }
        } finally {
            UI.setButtonLoading(false);
        }
    },
    initializeLoginPage() {
        Logger.log('Initializing login page...');
        if (Auth.isAuthenticated()) {
            Logger.log('User already authenticated, redirecting...');
            this.redirectToApp(); return;
        }
        const loginForm = document.getElementById('login-form');
        const loginButton = document.getElementById('login-button');
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        const rememberField = document.getElementById('remember');
        if (!loginButton || !usernameField || !passwordField) {
            Logger.error('Required form elements not found');
            UI.showError('Login form not properly initialized'); return;
        }
        if (!usernameField.value) usernameField.value = LOGIN_CONFIG.DEFAULT_USERNAME;
        loginButton.addEventListener('click', async (e) => {
            e.preventDefault();
            const username = usernameField.value.trim();
            const password = passwordField.value;
            const rememberMe = rememberField ? rememberField.checked : false;
            if (!username || !password) {
                UI.showError('Username and password are required'); return;
            }
            await this.handleLogin(username, password, rememberMe);
        });
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => { e.preventDefault(); loginButton.click(); });
        }
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') { e.preventDefault(); loginButton.click(); }
        });
        if (usernameField.value) passwordField.focus(); else usernameField.focus();
        Logger.success('Login page ready');
    }
};

// Initialize based on mode
if (LOGIN_CONFIG.STANDALONE_MODE && document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => { LoginHTML.injectIntoCurrentPage(); });
} else if (document.readyState === 'complete' || document.readyState === 'interactive') {
    const loginForm = document.getElementById('login-form');
    if (!loginForm && LOGIN_CONFIG.STANDALONE_MODE) {
        LoginHTML.injectIntoCurrentPage();
    } else {
        LoginManager.initializeLoginPage();
    }
} else {
    document.addEventListener('DOMContentLoaded', () => {
        const loginForm = document.getElementById('login-form');
        if (!loginForm && LOGIN_CONFIG.STANDALONE_MODE) {
            LoginHTML.injectIntoCurrentPage();
        } else {
            LoginManager.initializeLoginPage();
        }
    });
}

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { LoginManager, Auth, UI, Logger, LoginHTML };
}
EOF
        log_info "✅ Created self-contained fallback login.js"
    fi

    log_info "Static directory structure setup complete"
}

# --- 2. Setup Repositories (DISABLED - packages should be pre-installed) ---
setup_repositories() {
    log_info "Skipping repository setup - using pre-installed packages from build environment"
    # Note: All required packages should be specified in the KIWI configuration
    # and installed during the package installation phase, not during config.sh
    return 0
}

# --- 3. Install Python Dependencies (DISABLED - packages should be pre-installed) ---
install_python_dependencies() {
    log_info "Skipping Python package installation - using pre-installed packages from build environment"
    # Note: All Python packages should be specified in the KIWI configuration
    # and installed during the package installation phase, not during config.sh
    return 0
}

# --- 4. Verify Dependencies ---
verify_dependencies() {
    log_info "Verifying dependencies..."

    # Check Python (warn but don't fail)
    if command -v python3.11 &>/dev/null; then
        log_info "Python 3.11 is available"
    elif command -v python3 &>/dev/null; then
        log_info "Python 3 is available (fallback)"
    else
        log_info "Python not found - API may not work properly"
    fi

    # Check NetworkManager (warn but don't fail)
    if command -v NetworkManager &>/dev/null; then
        log_info "NetworkManager is available"
    else
        log_info "NetworkManager not found - network management may be limited"
    fi

    # Check for core Python packages via RPM (warn but don't fail)
    for pkg in python311-fastapi python311-uvicorn; do
        if rpm -q "$pkg" &>/dev/null; then
            log_info "Core RPM package $pkg is installed"
        else
            log_info "Core RPM package $pkg is not installed - API may not work properly"
        fi
    done

    # Check for optional Python packages via RPM (warn but don't fail)
    for pkg in python311-psutil python311-netifaces python311-pydantic; do
        if rpm -q "$pkg" &>/dev/null; then
            log_info "Optional RPM package $pkg is installed"
        else
            log_info "Optional RPM package $pkg is not installed (will use fallback if needed)"
        fi
    done

    log_info "Dependency verification completed"
}

# --- MAIN EXECUTION STARTS HERE ---

# --- 3. Ensure Directories ---
mkdir -p "$BIN" "$SERVICES" "$WEBUI" "$LOGDIR" "$PERSISTENCE_ROOT/api" || log_info "Warning: Failed to create some directories"
mkdir -p "$WEBUI/js" "$WEBUI/css" "$WEBUI/img" "$WEBUI/config" || log_info "Warning: Failed to create some web-ui subdirectories"

# --- 3.1 Diagnostic Information ---
log_info "WEBUI path: $WEBUI"
log_info "Current directory: $(pwd)"

# --- 4. Set Permissions ---
chmod 755 "$BIN" "$SERVICES" "$WEBUI" "$LOGDIR" || log_info "Warning: Failed to set some permissions"
# Ensure web-ui files are readable by everyone
find "$WEBUI" -type f -exec chmod 644 {} \; 2>/dev/null || log_info "No files to set permissions for yet"
find "$WEBUI" -type d -exec chmod 755 {} \; 2>/dev/null || log_info "No directories to set permissions for yet"
chown -R root:root "$PERSISTENCE_ROOT" || log_info "Warning: Failed to set ownership"

# --- 5. Install/Enable NetworkManager ---
if ! systemctl enable NetworkManager; then
    log_info "Warning: Failed to enable NetworkManager"
fi

if ! systemctl start NetworkManager; then
    log_info "Warning: Failed to start NetworkManager (this is normal during build)"
fi

# --- 6. Minimal Network Bring-up ---
if command -v ip &>/dev/null; then
    for iface in $(ip -br link show | awk '{print $1}' | grep -v lo); do
        ip link set dev "$iface" up || log_info "Warning: Failed to bring up interface $iface (this is normal during build)"
    done
fi

# --- 7. Get Primary IP for Configuration ---
PRIMARY_IP=$(hostname -I | awk '{print $1}')
NM_STATUS=$(systemctl is-active NetworkManager 2>/dev/null || echo "unknown")
# Note: Using dynamic welcome message only (see step 15) instead of static MOTD

# --- 8. FastAPI main.py (Python) ---
cat > "$PERSISTENCE_ROOT/api/main.py" <<'EOF'
#!/usr/bin/env python3
"""
PersistenceOS FastAPI Backend
Main entry point for the PersistenceOS web interface and API
"""

import uvicorn
import sys
import os
import logging
from pathlib import Path

# Add the API directory to Python path
api_dir = Path(__file__).parent
sys.path.insert(0, str(api_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("persistenceos")

def main():
    """Main entry point for PersistenceOS API server"""

    # Check if we're in debug mode
    debug_mode = "--debug" in sys.argv

    if debug_mode:
        logger.info("🔧 Starting PersistenceOS API in DEBUG mode")
        log_level = "debug"
        reload = True
    else:
        logger.info("🚀 Starting PersistenceOS API in PRODUCTION mode")
        log_level = "info"
        reload = False

    # Import the FastAPI app
    try:
        from app import app
        logger.info("✅ FastAPI app imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import FastAPI app: {e}")
        sys.exit(1)

    # Get host and port from environment or use defaults
    host = os.getenv("PERSISTENCE_HOST", "0.0.0.0")
    port = int(os.getenv("PERSISTENCE_PORT", "8080"))

    logger.info(f"🌐 Server will bind to {host}:{port}")
    logger.info(f"📁 API directory: {api_dir}")
    logger.info(f"🔄 Auto-reload: {reload}")

    try:
        # Start the Uvicorn server
        uvicorn.run(
            "app:app",
            host=host,
            port=port,
            log_level=log_level,
            reload=reload,
            access_log=True,
            server_header=False,
            date_header=False
        )
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF

# --- 8.1. Create app.py (FastAPI application) ---
cat > "$PERSISTENCE_ROOT/api/app.py" <<'EOF'
from fastapi import FastAPI, HTTPException, Request, Depends, status
from fastapi.staticfiles import StaticFiles
from fastapi.responses import RedirectResponse, HTMLResponse, FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
import socket
import os
import logging
from datetime import datetime, timedelta
import json
from typing import Optional, Dict, Any, List

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("persistenceos")

# Define the web UI path
WEBUI_PATH = "/usr/lib/persistence/web-ui"

# Check if web UI exists
if not os.path.exists(WEBUI_PATH):
    logger.error(f"Web UI path does not exist: {WEBUI_PATH}")
elif not os.path.exists(os.path.join(WEBUI_PATH, "login.html")):
    logger.error(f"login.html not found in {WEBUI_PATH}")
else:
    logger.info(f"Web UI path exists: {WEBUI_PATH}")
    # List files for verification
    for item in os.listdir(WEBUI_PATH):
        item_path = os.path.join(WEBUI_PATH, item)
        if os.path.isfile(item_path):
            logger.info(f"Found file: {item} ({os.path.getsize(item_path)} bytes)")
        else:
            logger.info(f"Found directory: {item}")

# Create FastAPI app - do not set root_path here as it's handled by Uvicorn
app = FastAPI(
    title="PersistenceOS API",
    description="PersistenceOS Web UI and API",
    version="6.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Log request middleware for debugging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info(f"Request path: {request.url.path}")
    try:
        response = await call_next(request)
        logger.info(f"Response status: {response.status_code}")
        return response
    except Exception as e:
        logger.error(f"Request error: {str(e)}")
        raise

# OAuth2 password bearer token for auth
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/auth/token", auto_error=False)

# Simple in-memory token storage for demo purposes
# In production, use proper token management
active_tokens = {}

# Validate token function
async def validate_token(token: str = Depends(oauth2_scheme)) -> Optional[Dict[str, Any]]:
    if not token:
        return None

    # Check if token exists and is not expired
    if token in active_tokens:
        token_data = active_tokens[token]
        if datetime.now().timestamp() * 1000 < token_data["expires_at"]:
            return token_data["user"]

    return None

# Optional token validation (doesn't raise exceptions)
def validate_token_optional(token: str) -> Optional[Dict[str, Any]]:
    """Validate token without raising exceptions."""
    try:
        if not token:
            return None

        # Check if token exists and is not expired
        if token in active_tokens:
            token_data = active_tokens[token]
            if datetime.now().timestamp() * 1000 < token_data["expires_at"]:
                return token_data["user"]

        return None
    except Exception as e:
        logger.error(f"Error validating token: {e}")
        return None

# Authentication endpoint
@app.post("/api/auth/token")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    # Simple authentication for demo
    if form_data.username == "root" and form_data.password == "linux":
        # Create token with expiry time
        token = f"token_{form_data.username}_{int(datetime.now().timestamp())}"
        user_data = {
            "username": form_data.username,
            "display_name": "Administrator",
            "roles": ["admin"]
        }
        expiry = int((datetime.now() + timedelta(hours=1)).timestamp() * 1000)

        # Store token
        active_tokens[token] = {
            "user": user_data,
            "expires_at": expiry
        }

        return {
            "access_token": token,
            "token_type": "bearer",
            "user": user_data,
            "expires_at": expiry
        }

    # Authentication failed
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid username or password",
        headers={"WWW-Authenticate": "Bearer"},
    )

# Token refresh endpoint
@app.post("/api/auth/refresh")
async def refresh_token(current_user: Dict = Depends(validate_token)):
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create new token
    token = f"token_{current_user['username']}_{int(datetime.now().timestamp())}"
    expiry = int((datetime.now() + timedelta(hours=1)).timestamp() * 1000)

    # Store token
    active_tokens[token] = {
        "user": current_user,
        "expires_at": expiry
    }

    return {
        "access_token": token,
        "token_type": "bearer",
        "expires_at": expiry
    }

# System config endpoint
@app.get("/api/config")
async def get_server_config():
    host_ip = socket.gethostbyname(socket.gethostname())

    # Get local IP addresses
    available_ips = []
    try:
        # Primary IP
        if host_ip and host_ip != "127.0.0.1":
            available_ips.append(host_ip)

        # Explicitly add localhost
        if "127.0.0.1" not in available_ips:
            available_ips.append("127.0.0.1")

    except Exception as e:
        logger.error(f"Error getting IPs: {str(e)}")
        available_ips = ["127.0.0.1"]

    config = {
        "host": host_ip,
        "port": 8080,
        "securePort": 8443,
        "apiBaseUrl": f"http://{host_ip}:8080/api",
        "secureApiBaseUrl": f"https://{host_ip}:8443/api",
        "availableIPs": available_ips,
        "version": "6.1.0",
        "generated_at": datetime.now().isoformat()
    }

    # Also update the api-config.json file to match
    try:
        api_config_path = os.path.join(WEBUI_PATH, "api-config.json")
        with open(api_config_path, "w") as f:
            json.dump({
                "host": host_ip,
                "http_port": 8080,
                "https_port": 8443,
                "api_base_url": "/api",
                "secure_api_base_url": "/api",
                "available_ips": available_ips
            }, f, indent=2)
        logger.info(f"Updated api-config.json at {api_config_path}")
    except Exception as e:
        logger.error(f"Failed to update api-config.json: {str(e)}")

    return config

# Health check endpoint
@app.get("/api/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# Root path redirects to login.html
@app.get("/")
async def redirect_to_login():
    logger.info("Root path accessed, redirecting to login.html")
    return RedirectResponse(url="/login.html")

# Direct login.html handler with caching headers
@app.get("/login.html", response_class=HTMLResponse)
async def serve_login(request: Request):
    login_path = os.path.join(WEBUI_PATH, "login.html")
    if os.path.exists(login_path):
        logger.info(f"Serving login.html directly from {login_path}")
        return FileResponse(
            login_path,
            media_type="text/html",
            headers={
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0"
            }
        )
    else:
        logger.error(f"login.html not found at {login_path}")
        raise HTTPException(status_code=404, detail=f"login.html not found at {login_path}")

# App.html endpoint for Vue.js app
@app.get("/app.html", response_class=HTMLResponse)
async def serve_app_html(request: Request):
    """Serve Vue.js app HTML wrapper"""
    # Check authentication from cookies or localStorage (more flexible)
    authenticated = False
    current_user = None

    try:
        # Try to get token from cookie first
        access_token = request.cookies.get("access_token")
        if access_token and access_token.startswith("Bearer "):
            token = access_token.replace("Bearer ", "")
            current_user = validate_token_optional(token)
            if current_user:
                authenticated = True
                logger.info(f"User authenticated via cookie: {current_user['username']}")

        # If no cookie auth, try Authorization header
        if not authenticated:
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                token = auth_header.split(" ")[1]
                current_user = validate_token_optional(token)
                if current_user:
                    authenticated = True
                    logger.info(f"User authenticated via header: {current_user['username']}")

        # If still not authenticated, allow access but let frontend handle auth
        if not authenticated:
            logger.info("No authentication found for app.html, serving page for frontend auth check")
            current_user = {"username": "guest", "display_name": "Guest"}
    except Exception as e:
        logger.error(f"Error checking authentication for app.html: {e}")
        current_user = {"username": "guest", "display_name": "Guest"}

    logger.info(f"Serving app.html for user: {current_user['username']}")

    # This is a wrapper HTML that loads the Vue.js app
    app_html = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>PersistenceOS - Dashboard</title>
        <link rel="stylesheet" href="css/style.css">
        <link rel="icon" href="img/favicon.ico" type="image/x-icon">
        <!-- Font Awesome for icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <!-- Authentication library -->
        <script src="js/auth.js"></script>
        <!-- Vue.js (with CDN fallback) -->
        <script>
            // Try to load local Vue.js first, fallback to CDN
            const vueScript = document.createElement('script');
            vueScript.src = 'js/vue.js';
            vueScript.onerror = function() {
                console.log('Local Vue.js not found, loading from CDN...');
                const cdnScript = document.createElement('script');
                cdnScript.src = 'https://unpkg.com/vue@3/dist/vue.global.prod.js';
                cdnScript.onload = function() {
                    console.log('Vue.js loaded from CDN successfully');
                };
                cdnScript.onerror = function() {
                    console.error('Failed to load Vue.js from CDN');
                    document.getElementById('app').innerHTML = '<div style="text-align: center; padding: 50px; color: red;">Error: Could not load Vue.js framework</div>';
                };
                document.head.appendChild(cdnScript);
            };
            vueScript.onload = function() {
                console.log('Vue.js loaded locally successfully');
            };
            document.head.appendChild(vueScript);
        </script>
        <!-- Main app script -->
        <script src="js/app.js" defer></script>
    </head>
    <body>
        <div id="app">
            <!-- Vue.js app will be mounted here -->
            <div class="loading-indicator">Loading PersistenceOS Dashboard...</div>
        </div>
    </body>
    </html>
    """

    return HTMLResponse(content=app_html, headers={
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0"
    })

# Serve JavaScript and CSS files directly
@app.get("/js/app.js")
async def serve_app_js(request: Request):
    """Serve the Vue.js application script"""
    app_js_path = os.path.join(WEBUI_PATH, "js/app.js")
    if os.path.exists(app_js_path):
        logger.info(f"Serving app.js from {app_js_path}")
        return FileResponse(app_js_path, media_type="application/javascript")
    else:
        logger.error(f"app.js not found at {app_js_path}")
        raise HTTPException(status_code=404, detail="app.js not found")

# Mount static files for CSS, JS, and images
app.mount("/css", StaticFiles(directory=os.path.join(WEBUI_PATH, "css")), name="css")
app.mount("/js", StaticFiles(directory=os.path.join(WEBUI_PATH, "js")), name="js")
app.mount("/img", StaticFiles(directory=os.path.join(WEBUI_PATH, "img")), name="img")

# Mount static directory if it exists
static_dir = os.path.join(WEBUI_PATH, "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")
EOF

# --- 9. Create run_api.sh script ---
cat > "$BIN/run_api.sh" <<'EOF'
#!/bin/bash
# PersistenceOS API Runner Script

set -e

API_DIR="/usr/lib/persistence/api"
MAIN_PY="$API_DIR/main.py"
LOG_FILE="/var/log/persistence/api.log"

# Ensure log directory exists
mkdir -p "$(dirname "$LOG_FILE")"

# Check if main.py exists
if [ ! -f "$MAIN_PY" ]; then
    echo "ERROR: main.py not found at $MAIN_PY" >&2
    exit 1
fi

# Change to API directory
cd "$API_DIR"

# Check for debug flag
if [ "$1" = "--debug" ]; then
    echo "Starting PersistenceOS API in debug mode..."
    exec python3 main.py --debug 2>&1 | tee "$LOG_FILE"
else
    echo "Starting PersistenceOS API in production mode..."
    exec python3 main.py 2>&1 | tee "$LOG_FILE"
fi
EOF

chmod +x "$BIN/run_api.sh"

# --- 10. Create systemd service ---
cat > "$SERVICES/persistenceos-api.service" <<'EOF'
[Unit]
Description=PersistenceOS FastAPI Backend
After=network.target
Wants=network.target

[Service]
Type=exec
User=root
Group=root
WorkingDirectory=/usr/lib/persistence/api
ExecStart=/usr/lib/persistence/bin/run_api.sh
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
Environment=PYTHONPATH=/usr/lib/persistence/api
Environment=PERSISTENCE_HOST=0.0.0.0
Environment=PERSISTENCE_PORT=8080

[Install]
WantedBy=multi-user.target
EOF

# --- 11. Install and enable the service ---
cp "$SERVICES/persistenceos-api.service" /etc/systemd/system/
systemctl daemon-reload
systemctl enable persistenceos-api.service

# --- 12. Create welcome message script ---
cat > "$BIN/welcome.sh" <<'EOF'
#!/bin/bash
# PersistenceOS Welcome Message

PRIMARY_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "unknown")
NM_STATUS=$(systemctl is-active NetworkManager 2>/dev/null || echo "unknown")
API_STATUS=$(systemctl is-active persistenceos-api 2>/dev/null || echo "unknown")

echo "=============================================="
echo "       Welcome to PersistenceOS 6.1"
echo "=============================================="
echo ""
echo "System Status:"
echo "  Primary IP: $PRIMARY_IP"
echo "  NetworkManager: $NM_STATUS"
echo "  API Service: $API_STATUS"
echo ""
echo "Web Interface:"
if [ "$PRIMARY_IP" != "unknown" ] && [ "$PRIMARY_IP" != "" ]; then
    echo "  http://$PRIMARY_IP:8080"
    echo "  https://$PRIMARY_IP:8443 (if SSL configured)"
else
    echo "  http://localhost:8080"
    echo "  https://localhost:8443 (if SSL configured)"
fi
echo ""
echo "Default Login: root / linux"
echo "=============================================="
EOF

chmod +x "$BIN/welcome.sh"

# --- 13. Setup login message ---
cat > /etc/profile.d/persistenceos-welcome.sh <<'EOF'
#!/bin/bash
# Show PersistenceOS welcome message on login
if [ -f /usr/lib/persistence/bin/welcome.sh ]; then
    /usr/lib/persistence/bin/welcome.sh
fi
EOF

chmod +x /etc/profile.d/persistenceos-welcome.sh

# --- 14. Setup repositories and install dependencies (DISABLED) ---
# setup_repositories  # Disabled - packages should be pre-installed
# install_python_dependencies  # Disabled - packages should be pre-installed

# --- 15. Create API config file ---
cat > "$WEBUI/api-config.json" <<EOF
{
  "host": "${PRIMARY_IP:-localhost}",
  "http_port": 8080,
  "https_port": 8443,
  "api_base_url": "/api",
  "secure_api_base_url": "/api",
  "available_ips": ["${PRIMARY_IP:-127.0.0.1}", "127.0.0.1"]
}
EOF

# --- 16. Deploy Web UI Components ---
log_info "Deploying web UI components..."

# Deploy self-contained login (minimal HTML wrapper)
deploy_self_contained_login

# Setup static directory structure and copy JavaScript files (includes self-contained login.js)
setup_static_directory

# --- 17. Verify Setup ---
verify_dependencies

echo "[SUCCESS] Minimal PersistenceOS config complete."
