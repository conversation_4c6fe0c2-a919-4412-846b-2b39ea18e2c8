/**
 * FILE          : server.js
 * PROJECT       : PersistenceOS
 * COPYRIGHT     : (c) 2024 PersistenceOS Team
 * AUTHOR        : PersistenceOS Team
 * PACKAGE       : PersistenceOS
 * LICENSE       : MIT
 * PURPOSE       : Server configuration for UI components
 */

/**
 * Server configuration for PersistenceOS UI
 * This module attempts to load server configuration in multiple ways:
 * 1. Try to load from api-config.json
 * 2. Try to fetch from runtime API at /api/config
 * 3. Use default configuration if both methods fail
 */
(function() {
    // Default configuration - used if dynamic config fails
    const DEFAULT_CONFIG = {
        host: window.location.hostname || 'localhost',
        http_port: 8080,
        https_port: 8443,
        api_base_url: '/api',
        secure_api_base_url: '/api',
        available_ips: [window.location.hostname || 'localhost', '127.0.0.1'],
        version: '6.1.0'
    };
    
    // Normalize and set configuration
    function setServerConfig(config) {
        if (!config) config = DEFAULT_CONFIG;
        
        // Convert legacy properties to new format if needed
        const normalizedConfig = {
            host: config.host || DEFAULT_CONFIG.host,
            port: Number(config.http_port || config.port || DEFAULT_CONFIG.http_port),
            securePort: Number(config.https_port || config.securePort || DEFAULT_CONFIG.https_port),
            apiBaseUrl: config.api_base_url || config.apiBaseUrl || DEFAULT_CONFIG.api_base_url,
            secureApiBaseUrl: config.secure_api_base_url || config.secureApiBaseUrl || DEFAULT_CONFIG.secure_api_base_url,
            availableIPs: config.available_ips || config.availableIPs || DEFAULT_CONFIG.available_ips,
            version: config.version || DEFAULT_CONFIG.version
        };
        
        // Default to current hostname if not provided
        if (!normalizedConfig.host || normalizedConfig.host === 'localhost') {
            normalizedConfig.host = window.location.hostname || 'localhost';
        }
        
        // Construct full URLs if they don't include protocol
        if (!normalizedConfig.apiBaseUrl.includes('://')) {
            if (normalizedConfig.apiBaseUrl.startsWith('/')) {
                // Relative path, keep as is
            } else {
                normalizedConfig.apiBaseUrl = `http://${normalizedConfig.host}:${normalizedConfig.port}/${normalizedConfig.apiBaseUrl}`;
            }
        }
        
        if (!normalizedConfig.secureApiBaseUrl.includes('://')) {
            if (normalizedConfig.secureApiBaseUrl.startsWith('/')) {
                // Relative path, keep as is
            } else {
                normalizedConfig.secureApiBaseUrl = `https://${normalizedConfig.host}:${normalizedConfig.securePort}/${normalizedConfig.secureApiBaseUrl}`;
            }
        }
        
        // Store the configuration globally
        window.serverConfig = normalizedConfig;
        
        // Dispatch event to notify other components that config is loaded
        const event = new CustomEvent('server-config-loaded', { detail: normalizedConfig });
        window.dispatchEvent(event);
        
        console.log('Server configuration loaded:', normalizedConfig);
        return normalizedConfig;
    }
    
    // Try to load config from api-config.json
    async function loadStaticConfig() {
        try {
            const response = await fetch('api-config.json');
            if (response.ok) {
                const config = await response.json();
                return setServerConfig(config);
            }
            return null;
        } catch (error) {
            console.warn('Failed to load api-config.json:', error);
            return null;
        }
    }
    
    // Try to fetch config from API
    async function fetchRuntimeConfig() {
        try {
            const apiUrl = '/api/config';
            const response = await fetch(apiUrl);
            if (response.ok) {
                const config = await response.json();
                return setServerConfig(config);
            }
            return null;
        } catch (error) {
            console.warn('Failed to fetch runtime config:', error);
            return null;
        }
    }
    
    // Initialize server configuration
    async function initServerConfig() {
        // Try multiple methods to get config
        const staticConfig = await loadStaticConfig();
        if (staticConfig) return staticConfig;
        
        const runtimeConfig = await fetchRuntimeConfig();
        if (runtimeConfig) return runtimeConfig;
        
        // Fall back to defaults if both methods fail
        return setServerConfig(DEFAULT_CONFIG);
    }
    
    // Start the initialization process
    initServerConfig().catch(error => {
        console.error('Failed to initialize server configuration:', error);
        setServerConfig(DEFAULT_CONFIG);
    });
})(); 