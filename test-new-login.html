<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #0a4b78 0%, #003366 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            color: #333;
        }
        .login-container {
            width: 400px;
            max-width: 95%;
        }
        .login-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .login-header {
            background: #0066cc;
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .login-header h1 {
            margin: 0 0 5px 0;
            font-size: 28px;
        }
        .login-header .version {
            margin: 5px 0 0 0;
            opacity: 0.8;
            font-size: 14px;
        }
        .logo {
            width: 60px;
            height: 60px;
            margin-bottom: 10px;
        }
        .login-form {
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 14px;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .remember-me {
            display: flex;
            align-items: center;
        }
        .remember-me input {
            margin-right: 8px;
        }
        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .btn-primary {
            background: #0066cc;
            color: white;
        }
        .btn-primary:hover {
            background: #0055aa;
        }
        .btn-primary:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .login-footer {
            text-align: center;
            padding: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #777;
        }
        .copyright {
            margin-top: 10px;
            font-size: 11px;
        }
        .error-message {
            color: #e74c3c;
            margin: 10px 0;
            text-align: center;
            padding: 8px;
            border-radius: 4px;
            background-color: rgba(231, 76, 60, 0.1);
        }
        .status-message {
            color: #28a745;
            margin: 10px 0;
            text-align: center;
            padding: 8px;
            border-radius: 4px;
            background-color: rgba(40, 167, 69, 0.1);
        }
        .hidden {
            display: none !important;
        }
        .test-info {
            background: rgba(255,255,255,0.1);
            color: white;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
    <!-- Include our new standalone login.js -->
    <script src="login.js" defer></script>
</head>
<body>
    <div class="login-container">
        <div class="test-info">
            🧪 <strong>Testing New Login.js</strong><br>
            This page tests our standalone login.js file.<br>
            Check console for debug messages.
        </div>
        
        <div class="login-card">
            <div class="login-header">
                <div class="logo">🔐</div>
                <h1>PersistenceOS</h1>
                <p class="version">Version: 6.1.0 (Test)</p>
            </div>
            <div class="login-form">
                <form id="login-form" action="javascript:void(0);">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" value="root" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" placeholder="Enter password" required>
                    </div>
                    <div class="form-group">
                        <div class="remember-me">
                            <input type="checkbox" id="remember" name="remember">
                            <label for="remember">Remember me</label>
                        </div>
                    </div>
                    <div id="login-error" class="error-message hidden">Invalid username or password</div>
                    <div id="login-status" class="status-message hidden">Logging in...</div>
                    <div class="form-group">
                        <button type="button" id="login-button" class="btn btn-primary">Log In</button>
                    </div>
                </form>
            </div>
            <div class="login-footer">
                <p>PersistenceOS is a specialized hypervisor and NAS operating system based on SUSE Micro Leap 6.1.</p>
                <p>Test credentials: <strong>root</strong> / <strong>linux</strong></p>
                <p class="copyright">&copy; 2024 PersistenceOS Team</p>
            </div>
        </div>
    </div>
</body>
</html>
