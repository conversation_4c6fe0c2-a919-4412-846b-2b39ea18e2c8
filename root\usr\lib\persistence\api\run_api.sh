#!/bin/bash
# FILE:         run_api.sh
# PROJECT:      PersistenceOS
# COPYRIGHT:    (c) 2024 PersistenceOS Team
# AUTHOR:       PersistenceOS Team
# PACKAGE:      PersistenceOS
# LICENSE:      MIT
# PURPOSE:      Launches the PersistenceOS API service

# Configure environment
API_PORT="${API_PORT:-8080}"  # Default port for PersistenceOS FastAPI/Uvicorn
DEBUG_MODE="${DEBUG_MODE:-false}"
WEB_ROOT="${WEB_ROOT:-/usr/lib/persistence/web-ui}"
VAR_WEB_ROOT="/var/lib/persistence/web-ui"
API_CONFIG_PATH="${API_CONFIG_PATH:-/usr/lib/persistence/web-ui/api-config.json}"

# Ensure config directory exists
mkdir -p "$(dirname "$API_CONFIG_PATH")"

# Ensure we're in the correct directory
cd "$(dirname "$0")" || exit 1

# Set up Python environment
export PYTHONPATH="$PYTHONPATH:/usr/lib/persistence/python"

# Add user site-packages to Python path (for pip --user installations)
USER_SITE_PACKAGES=$(python3 -m site --user-site 2>/dev/null || echo "")
if [ -n "$USER_SITE_PACKAGES" ] && [ -d "$USER_SITE_PACKAGES" ]; then
    export PYTHONPATH="$PYTHONPATH:$USER_SITE_PACKAGES"
    echo "Added user site-packages to PYTHONPATH: $USER_SITE_PACKAGES"
fi

# Configure logging
TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
LOG_DIR="/var/log/persistenceos"
mkdir -p "$LOG_DIR"
LOG_FILE="$LOG_DIR/api-$TIMESTAMP.log"

# Create symlink to latest log
ln -sf "$LOG_FILE" "$LOG_DIR/api-latest.log"

# Ensure proper permissions
chmod 755 "$(dirname "$0")"
chmod 644 "$LOG_FILE"

# Create both web-ui directories and make sure they're linked
mkdir -p "$WEB_ROOT" "$VAR_WEB_ROOT"

# Function to ensure files exist in both locations
ensure_web_files() {
    echo "Ensuring web files are accessible from both locations..."

    # If login.html exists in one location but not the other, copy it
    if [ -f "$WEB_ROOT/login.html" ] && [ ! -f "$VAR_WEB_ROOT/login.html" ]; then
        echo "Copying login.html from $WEB_ROOT to $VAR_WEB_ROOT"
        cp "$WEB_ROOT/login.html" "$VAR_WEB_ROOT/login.html"
        chmod 644 "$VAR_WEB_ROOT/login.html"
    elif [ ! -f "$WEB_ROOT/login.html" ] && [ -f "$VAR_WEB_ROOT/login.html" ]; then
        echo "Copying login.html from $VAR_WEB_ROOT to $WEB_ROOT"
        cp "$VAR_WEB_ROOT/login.html" "$WEB_ROOT/login.html"
        chmod 644 "$WEB_ROOT/login.html"
    fi

    # Create static directories in both locations if they don't exist
    mkdir -p "$WEB_ROOT/static" "$VAR_WEB_ROOT/static"
    mkdir -p "$WEB_ROOT/static/css" "$WEB_ROOT/static/js" "$WEB_ROOT/static/img"
    mkdir -p "$VAR_WEB_ROOT/static/css" "$VAR_WEB_ROOT/static/js" "$VAR_WEB_ROOT/static/img"

    # Ensure subdirectories exist and are linked
    for dir in css js img config; do
        mkdir -p "$WEB_ROOT/$dir" "$VAR_WEB_ROOT/$dir"

        # Copy files from one location to the other if they exist
        if [ -d "$WEB_ROOT/$dir" ]; then
            for file in "$WEB_ROOT/$dir"/*; do
                if [ -f "$file" ]; then
                    base_name=$(basename "$file")
                    if [ ! -f "$VAR_WEB_ROOT/$dir/$base_name" ]; then
                        echo "Copying $dir/$base_name to VAR_WEB_ROOT"
                        cp "$file" "$VAR_WEB_ROOT/$dir/$base_name"
                        chmod 644 "$VAR_WEB_ROOT/$dir/$base_name"
                    fi

                    # Also copy to static directory
                    if [ ! -f "$WEB_ROOT/static/$dir/$base_name" ]; then
                        echo "Copying $dir/$base_name to WEB_ROOT/static"
                        cp "$file" "$WEB_ROOT/static/$dir/$base_name"
                        chmod 644 "$WEB_ROOT/static/$dir/$base_name"
                    fi

                    if [ ! -f "$VAR_WEB_ROOT/static/$dir/$base_name" ]; then
                        echo "Copying $dir/$base_name to VAR_WEB_ROOT/static"
                        cp "$file" "$VAR_WEB_ROOT/static/$dir/$base_name"
                        chmod 644 "$VAR_WEB_ROOT/static/$dir/$base_name"
                    fi
                fi
            done
        fi

        if [ -d "$VAR_WEB_ROOT/$dir" ]; then
            for file in "$VAR_WEB_ROOT/$dir"/*; do
                if [ -f "$file" ]; then
                    base_name=$(basename "$file")
                    if [ ! -f "$WEB_ROOT/$dir/$base_name" ]; then
                        echo "Copying $dir/$base_name to WEB_ROOT"
                        cp "$file" "$WEB_ROOT/$dir/$base_name"
                        chmod 644 "$WEB_ROOT/$dir/$base_name"
                    fi

                    # Also copy to static directory
                    if [ ! -f "$WEB_ROOT/static/$dir/$base_name" ]; then
                        echo "Copying $dir/$base_name to WEB_ROOT/static"
                        cp "$file" "$WEB_ROOT/static/$dir/$base_name"
                        chmod 644 "$WEB_ROOT/static/$dir/$base_name"
                    fi

                    if [ ! -f "$VAR_WEB_ROOT/static/$dir/$base_name" ]; then
                        echo "Copying $dir/$base_name to VAR_WEB_ROOT/static"
                        cp "$file" "$VAR_WEB_ROOT/static/$dir/$base_name"
                        chmod 644 "$VAR_WEB_ROOT/static/$dir/$base_name"
                    fi
                fi
            done
        fi
    done

    echo "Web files synchronization complete"
}

# Call the function to ensure files exist in both locations
ensure_web_files

# Ensure login.html exists in at least one location
if [ ! -f "${WEB_ROOT}/login.html" ] && [ ! -f "${VAR_WEB_ROOT}/login.html" ]; then
    echo "ERROR: login.html not found in ${WEB_ROOT} or ${VAR_WEB_ROOT}" >&2
    exit 1
fi

echo "Starting PersistenceOS API server..."
echo "Port: $API_PORT"
echo "Debug mode: $DEBUG_MODE"
echo "Web root: $WEB_ROOT"
echo "Var web root: $VAR_WEB_ROOT"
echo "API config path: $API_CONFIG_PATH"
echo "Log file: $LOG_FILE"

# Export environment variables for the API
export API_PORT
export DEBUG_MODE
export WEB_ROOT
export API_CONFIG_PATH

# Check if we should run in development mode
if [ "$DEBUG_MODE" = "true" ]; then
    echo "Running in DEBUG mode - enabling auto-reload"
    exec python3 main.py 2>&1 | tee -a "$LOG_FILE"
else
    # Run in production mode
    exec python3 main.py >> "$LOG_FILE" 2>&1
fi