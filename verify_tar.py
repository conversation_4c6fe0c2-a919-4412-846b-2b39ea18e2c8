#!/usr/bin/env python3
import tarfile
import os
from datetime import datetime

def verify_tar_gz():
    """Verify the contents of root.tar.gz"""
    try:
        # Check if file exists
        if not os.path.exists('root.tar.gz'):
            print("❌ root.tar.gz does not exist!")
            return False
        
        # Get file info
        stat = os.stat('root.tar.gz')
        size = stat.st_size
        mtime = datetime.fromtimestamp(stat.st_mtime)
        
        print(f"📁 File: root.tar.gz")
        print(f"📏 Size: {size} bytes ({size/1024/1024:.2f} MB)")
        print(f"🕒 Modified: {mtime}")
        print()
        
        # Open and verify tar file
        with tarfile.open('root.tar.gz', 'r:gz') as tar:
            members = tar.getnames()
            print(f"📦 Archive contains {len(members)} files/directories")
            print()
            
            # Look for auth.js specifically
            auth_files = [m for m in members if 'auth.js' in m]
            if auth_files:
                print("✅ Found auth.js files:")
                for auth_file in auth_files:
                    print(f"   - {auth_file}")
                    
                    # Extract and check first few lines of auth.js
                    if auth_file.endswith('auth.js'):
                        try:
                            member = tar.getmember(auth_file)
                            f = tar.extractfile(member)
                            if f:
                                content = f.read().decode('utf-8')
                                lines = content.split('\n')[:10]
                                print(f"   📄 First 10 lines of {auth_file}:")
                                for i, line in enumerate(lines, 1):
                                    print(f"      {i:2d}: {line}")
                                print()
                        except Exception as e:
                            print(f"   ⚠️  Could not read {auth_file}: {e}")
            else:
                print("❌ No auth.js files found in archive!")
            
            # Show some key directories
            key_dirs = [m for m in members if any(key in m for key in ['usr/lib/persistence', 'usr/src/packages'])]
            if key_dirs:
                print("📂 Key directories found:")
                for dir_name in sorted(set(['/'.join(d.split('/')[:3]) for d in key_dirs if '/' in d]))[:10]:
                    print(f"   - {dir_name}")
            
        print("\n✅ Archive verification completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error verifying archive: {e}")
        return False

if __name__ == "__main__":
    verify_tar_gz()
