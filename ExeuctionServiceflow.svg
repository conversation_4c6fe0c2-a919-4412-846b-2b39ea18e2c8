<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 868.8541870117188 1470" style="max-width: 868.8541870117188px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1"><style>#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .error-icon{fill:#a44141;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edge-thickness-normal{stroke-width:1px;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .marker.cross{stroke:lightgrey;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 p{margin:0;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .cluster-label text{fill:#F9FFFE;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .cluster-label span{color:#F9FFFE;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .cluster-label span p{background-color:transparent;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .label text,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 span{fill:#ccc;color:#ccc;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .node rect,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .node circle,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .node ellipse,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .node polygon,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .rough-node .label text,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .node .label text,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .image-shape .label,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .icon-shape .label{text-anchor:middle;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .rough-node .label,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .node .label,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .image-shape .label,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .icon-shape .label{text-align:center;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .node.clickable{cursor:pointer;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .arrowheadPath{fill:lightgrey;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .cluster text{fill:#F9FFFE;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .cluster span{color:#F9FFFE;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 rect.text{fill:none;stroke-width:0;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .icon-shape,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .icon-shape p,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .icon-shape rect,#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M428.422,86L428.422,90.167C428.422,94.333,428.422,102.667,428.492,110.417C428.562,118.167,428.703,125.334,428.773,128.917L428.843,132.501"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M340.972,326.55L305.012,347.292C269.051,368.034,197.13,409.517,161.169,459.592C125.208,509.667,125.208,568.333,125.208,627C125.208,685.667,125.208,744.333,125.208,781.167C125.208,818,125.208,833,125.208,840.5L125.208,848"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_D_2" d="M492.556,350.866L506.635,367.555C520.713,384.244,548.869,417.622,563.022,439.894C577.175,462.167,577.324,473.334,577.398,478.917L577.473,484.5"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M512.523,701.497L497.505,718.414C482.487,735.331,452.452,769.166,437.434,791.583C422.417,814,422.417,825,422.417,830.5L422.417,836"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_F_4" d="M642.529,701.497L657.38,718.414C672.231,735.331,701.933,769.166,716.784,793.583C731.635,818,731.635,833,731.635,840.5L731.635,848"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_G_5" d="M125.208,906L125.208,912.167C125.208,918.333,125.208,930.667,158.429,942.646C191.65,954.625,258.092,966.25,291.313,972.062L324.534,977.874"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_G_6" d="M422.417,918L422.417,922.167C422.417,926.333,422.417,934.667,422.417,942.333C422.417,950,422.417,957,422.417,960.5L422.417,964"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_7" d="M731.635,906L731.635,912.167C731.635,918.333,731.635,930.667,696.414,942.756C661.192,954.846,590.748,966.692,555.526,972.616L520.304,978.539"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_8" d="M422.417,1022L422.417,1026.167C422.417,1030.333,422.417,1038.667,422.417,1046.333C422.417,1054,422.417,1061,422.417,1064.5L422.417,1068"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_9" d="M422.417,1126L422.417,1130.167C422.417,1134.333,422.417,1142.667,422.417,1150.333C422.417,1158,422.417,1165,422.417,1168.5L422.417,1172"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_10" d="M422.417,1230L422.417,1234.167C422.417,1238.333,422.417,1246.667,422.417,1254.333C422.417,1262,422.417,1269,422.417,1272.5L422.417,1276"></path><path marker-end="url(#mermaid-47a842e5-eed9-4038-88b9-a2129206e9f1_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_11" d="M422.417,1334L422.417,1338.167C422.417,1342.333,422.417,1350.667,422.417,1358.333C422.417,1366,422.417,1373,422.417,1376.5L422.417,1380"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(125.20833587646484, 627)" class="edgeLabel"><g transform="translate(-11.328125, -12)" class="label"><foreignObject height="24" width="22.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(577.0260467529297, 451)" class="edgeLabel"><g transform="translate(-9.401041984558105, -12)" class="label"><foreignObject height="24" width="18.80208396911621"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g transform="translate(422.4166717529297, 803)" class="edgeLabel"><g transform="translate(-11.328125, -12)" class="label"><foreignObject height="24" width="22.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(731.6354217529297, 803)" class="edgeLabel"><g transform="translate(-9.401041984558105, -12)" class="label"><foreignObject height="24" width="18.80208396911621"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(428.42187881469727, 47)" id="flowchart-A-0" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Start ensure_systemd_services</p></span></div></foreignObject></g></g><g transform="translate(428.42187881469727, 275)" id="flowchart-B-1" class="node default"><polygon transform="translate(-139,139)" class="label-container" points="139,0 278,-139 139,-278 0,-139"></polygon><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Services in /usr/lib/systemd/system/?</p></span></div></foreignObject></g></g><g transform="translate(125.20833587646484, 879)" id="flowchart-C-3" class="node default"><rect height="54" width="234.4166717529297" y="-27" x="-117.20833587646484" style="" class="basic label-container"></rect><g transform="translate(-87.20833587646484, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="174.4166717529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>✅ Use existing services</p></span></div></foreignObject></g></g><g transform="translate(577.0260467529297, 627)" id="flowchart-D-5" class="node default"><polygon transform="translate(-139,139)" class="label-container" points="139,0 278,-139 139,-278 0,-139"></polygon><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Services in $SERVICES directory?</p></span></div></foreignObject></g></g><g transform="translate(422.4166717529297, 879)" id="flowchart-E-7" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>✅ Copy to /etc/systemd/system/</p></span></div></foreignObject></g></g><g transform="translate(731.6354217529297, 879)" id="flowchart-F-9" class="node default"><rect height="54" width="258.4375" y="-27" x="-129.21875" style="" class="basic label-container"></rect><g transform="translate(-99.21875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="198.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚠️ Create fallback services</p></span></div></foreignObject></g></g><g transform="translate(422.4166717529297, 995)" id="flowchart-G-11" class="node default"><rect height="54" width="187.8854217529297" y="-27" x="-93.94271087646484" style="" class="basic label-container"></rect><g transform="translate(-63.942710876464844, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.88542175292969"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create run_api.sh</p></span></div></foreignObject></g></g><g transform="translate(422.4166717529297, 1099)" id="flowchart-H-17" class="node default"><rect height="54" width="242.73959350585938" y="-27" x="-121.36979675292969" style="" class="basic label-container"></rect><g transform="translate(-91.36979675292969, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="182.73959350585938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create main.py if missing</p></span></div></foreignObject></g></g><g transform="translate(422.4166717529297, 1203)" id="flowchart-I-19" class="node default"><rect height="54" width="241.1041717529297" y="-27" x="-120.55208587646484" style="" class="basic label-container"></rect><g transform="translate(-90.55208587646484, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="181.1041717529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>systemctl daemon-reload</p></span></div></foreignObject></g></g><g transform="translate(422.4166717529297, 1307)" id="flowchart-J-21" class="node default"><rect height="54" width="243" y="-27" x="-121.5" style="" class="basic label-container"></rect><g transform="translate(-91.5, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="183"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>systemctl enable services</p></span></div></foreignObject></g></g><g transform="translate(422.4166717529297, 1423)" id="flowchart-K-23" class="node default"><rect height="78" width="260" y="-39" x="-130" style="" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>✅ Services guaranteed to work</p></span></div></foreignObject></g></g></g></g></g></svg>