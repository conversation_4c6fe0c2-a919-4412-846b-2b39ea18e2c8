<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="icon" href="img/favicon.ico" type="image/x-icon">
    <!-- Include the external login.js file -->
    <script src="js/login.js" defer></script>
    <!-- Fallback to embedded login functionality if the external script fails to load -->
    <script>
        window.addEventListener('error', function(e) {
            if (e.target.src && e.target.src.includes('js/login.js')) {
                console.log('External login.js failed to load, using embedded fallback...');
                // Embedded fallback login functionality
                document.addEventListener('DOMContentLoaded', function() {
                    const loginButton = document.getElementById('login-button');
                    const usernameField = document.getElementById('username');
                    const passwordField = document.getElementById('password');
                    const errorElement = document.getElementById('login-error');

                    if (loginButton && usernameField && passwordField) {
                        loginButton.addEventListener('click', function() {
                            const username = usernameField.value.trim();
                            const password = passwordField.value;

                            if (username === 'root' && password === 'linux') {
                                localStorage.setItem('authenticated', 'true');
                                localStorage.setItem('username', username);
                                window.location.href = '/app.html?from_login=true';
                            } else {
                                if (errorElement) {
                                    errorElement.textContent = 'Invalid username or password';
                                    errorElement.classList.remove('hidden');
                                }
                            }
                        });
                    }
                });
            }
        }, true);
    </script>
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <img src="img/logo.svg" alt="PersistenceOS Logo" class="logo">
                <h1>PersistenceOS</h1>
                <p class="version">Version: 6.1.0</p>
            </div>
            <div class="login-form">
                <form id="login-form" action="javascript:void(0);">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" placeholder="root" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" placeholder="Enter password" required>
                    </div>
                    <div class="form-group">
                        <div class="remember-me">
                            <input type="checkbox" id="remember" name="remember">
                            <label for="remember">Remember me</label>
                        </div>
                    </div>
                    <div id="login-error" class="error-message hidden">Invalid username or password</div>
                    <div class="form-group">
                        <button type="button" id="login-button" class="btn btn-primary" style="width: 100%;">Log In</button>
                    </div>
                </form>
            </div>
            <div class="login-footer">
                <p>PersistenceOS is a specialized hypervisor and NAS operating system based on SUSE Micro Leap 6.1.</p>
                <p>Default login: <strong>root</strong> / <strong>linux</strong> (same as system login)</p>
                <p class="copyright">&copy; 2024 PersistenceOS Team</p>
            </div>
        </div>
    </div>

    <!-- Login functionality is now in the external login.js file -->
</body>
</html>
