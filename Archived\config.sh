#!/bin/bash
#================
# FILE          : config.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Consolidated system configuration script
#================
# NOTE: This version incorporates:
# - Original config.sh functionality
# - image.sh welcome system
# - VM/snapshot management
# - Login hook integration
# - Health checker integration
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="2.1.0"  # Minor version bump for health checker integration

# Define paths
PERSISTENCE_VERSION="6.1.0"
PERSISTENCE_ROOT="/usr/lib/persistence"
PERSISTENCE_SCRIPTS="${PERSISTENCE_ROOT}/scripts"
PERSISTENCE_SERVICES="${PERSISTENCE_ROOT}/services"
PERSISTENCE_BIN="${PERSISTENCE_ROOT}/bin"
PERSISTENCE_TOOLS="${PERSISTENCE_ROOT}/tools"
PERSISTENCE_WEB_UI="${PERSISTENCE_ROOT}/web-ui"
PERSISTENCE_API="${PERSISTENCE_ROOT}/api"
PERSISTENCE_VAR="/var/lib/persistence"
PERSISTENCE_LOG="/var/log/persistence"
PERSISTENCE_MARKER="/var/lib/persistenceos"
NGINX_CONF_DIR="/etc/nginx"
PERSISTENCE_WEB_PORT="8080"  # Using port 8080 for web interface to avoid conflicts
WELCOME_LOG="/var/log/persistence-welcome.log"  # Added for welcome message functionality
HEALTH_CHECK_LOG="/var/log/persistence-health.log"  # Added for health checker functionality

# Logging functions
log_info() {
    echo "[INFO] $1"
}

log_success() {
    echo "[SUCCESS] $1"
}

log_warning() {
    echo "[WARNING] $1"
}

log_error() {
    echo "[ERROR] $1"
}

# Create directory if it doesn't exist
ensure_directory() {
    local dir="$1"
    if [ ! -d "${dir}" ]; then
        mkdir -p "${dir}"
        log_info "Created directory: ${dir}"
    fi
}

# Set permissions on scripts and directories
set_permissions() {
    log_info "Setting permissions on scripts and directories"

    # Set permissions on scripts
    find "${PERSISTENCE_SCRIPTS}" -type f -name "*.sh" -exec chmod 755 {} \; 2>/dev/null || true

    # Set permissions on tools
    find "${PERSISTENCE_TOOLS}" -type f -exec chmod 755 {} \; 2>/dev/null || true

    # Set permissions on bin directory
    find "${PERSISTENCE_BIN}" -type f -exec chmod 755 {} \; 2>/dev/null || true

    # Set permissions on service files
    find "${PERSISTENCE_SERVICES}" -type f -name "*.service" -exec chmod 644 {} \; 2>/dev/null || true

    # Set permissions on directories
    chmod 755 "${PERSISTENCE_ROOT}" 2>/dev/null || true
    chmod 755 "${PERSISTENCE_SCRIPTS}" 2>/dev/null || true
    chmod 755 "${PERSISTENCE_SERVICES}" 2>/dev/null || true
    chmod 755 "${PERSISTENCE_BIN}" 2>/dev/null || true
    chmod 755 "${PERSISTENCE_TOOLS}" 2>/dev/null || true

    # Set permissions on log directory
    mkdir -p "${PERSISTENCE_LOG}" 2>/dev/null || true
    chmod 755 "${PERSISTENCE_LOG}" 2>/dev/null || true

    # Set permissions on var directory
    mkdir -p "${PERSISTENCE_VAR}" 2>/dev/null || true
    chmod 755 "${PERSISTENCE_VAR}" 2>/dev/null || true

    # Set permissions on marker directory
    mkdir -p "${PERSISTENCE_MARKER}" 2>/dev/null || true
    chmod 755 "${PERSISTENCE_MARKER}" 2>/dev/null || true

    log_success "Permissions set successfully"
}

# ==========================================
# Network Detection Helper
# ==========================================
# Enhanced function to detect network interfaces and IPs
get_network_ips() {
    local log_prefix="$1"
    local found_ips=()
    local debug_info=()
    
    # Helper function for logging
    local _log_fn() {
        if [ -n "$log_prefix" ]; then
            echo "[$(date '+%Y-%m-%d %H:%M:%S')] ${log_prefix}: $1" >> "$WELCOME_LOG" 2>/dev/null || true
            [ "${VERBOSE:-false}" = "true" ] && echo "${log_prefix}: $1"
        fi
        debug_info+=("$1")
    }
    
    _log_fn "Detecting network interfaces"
    
    # Method 1: Check for specific interfaces commonly used in VMs and cloud environments
    local common_interfaces=("ens3" "eth0" "ens1" "ens5" "enp1s0" "enp0s3" "ens32" "ens33" "ens160")
    for iface in "${common_interfaces[@]}"; do
        if command -v ip &>/dev/null && ip -4 addr show dev "$iface" >/dev/null 2>&1; then
            local iface_ip=$(ip -4 addr show dev "$iface" 2>/dev/null | grep -oP 'inet \K[\d.]+' | head -1 || 
                              ip -4 addr show dev "$iface" 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 || echo "")
            
            if [ -n "$iface_ip" ] && [[ "$iface_ip" != "127."* ]] && [[ "$iface_ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                found_ips+=("$iface_ip")
                _log_fn "Found $iface interface with IP: ${iface_ip}"
            fi
        fi
    done
    
    # Method 2: Check default route interface if no specific interface was found
    if [ ${#found_ips[@]} -eq 0 ] && command -v ip &>/dev/null; then
        _log_fn "No common interfaces found, checking default route interface"
        local default_interface=$(ip route show default 2>/dev/null | grep -oP 'dev \K\S+' | head -1 || echo "")
        if [ -n "$default_interface" ] && [ "$default_interface" != "lo" ]; then
            _log_fn "Found default interface: $default_interface"
            local ip=$(ip -4 addr show dev "$default_interface" 2>/dev/null | grep -oP 'inet \K[\d.]+' | head -1 || 
                       ip -4 addr show dev "$default_interface" 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 || echo "")
            
            if [ -n "$ip" ] && [[ "$ip" != "127."* ]] && [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                found_ips+=("$ip")
                _log_fn "Found IP on default interface: $ip"
            else
                _log_fn "Default interface $default_interface exists but no valid IP found"
            fi
        else
            _log_fn "No default route interface found"
        fi
    fi
    
    # Method 3: Scan all interfaces as fallback
    if [ ${#found_ips[@]} -eq 0 ] && command -v ip &>/dev/null; then
        _log_fn "No IP found via default route, scanning all interfaces"
        local interfaces=$(ip -o link show 2>/dev/null | awk -F': ' '{print $2}' | grep -v "lo" || echo "")
        for iface in $interfaces; do
            local ip=$(ip -4 addr show dev "$iface" 2>/dev/null | grep -oP 'inet \K[\d.]+' | head -1 || 
                      ip -4 addr show dev "$iface" 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 || echo "")
            
            if [ -n "$ip" ] && [[ "$ip" != "127."* ]] && [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                found_ips+=("$ip")
                _log_fn "Found interface ${iface} with IP: ${ip}"
            else
                _log_fn "Interface $iface exists but no valid IP found"
            fi
        done
    fi
    
    # Method 4: Try hostname command as last resort
    if [ ${#found_ips[@]} -eq 0 ] && command -v hostname &>/dev/null; then
        _log_fn "No IP found via interfaces scan, trying hostname -I"
        local all_ips=$(hostname -I 2>/dev/null || echo "")
        for ip in $all_ips; do
            if [[ "$ip" != "127."* ]] && [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                found_ips+=("$ip")
                _log_fn "Found IP via hostname: $ip"
            fi
        done
    fi
    
    # Method 5: Last resort - check /etc/hosts file for non-loopback entries
    if [ ${#found_ips[@]} -eq 0 ] && [ -f "/etc/hosts" ]; then
        _log_fn "No IP found via hostname, checking /etc/hosts"
        local host_ips=$(grep -v '^#' /etc/hosts | grep -v "localhost" | grep -v "127.0.0.1" | grep -v "::1" | awk '{print $1}' || echo "")
        for ip in $host_ips; do
            if [[ "$ip" != "127."* ]] && [[ "$ip" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                found_ips+=("$ip")
                _log_fn "Found IP in /etc/hosts: $ip"
            fi
        done
    fi
    
    # Last resort - add loopback if we found absolutely nothing
    if [ ${#found_ips[@]} -eq 0 ]; then
        _log_fn "WARNING: No network interfaces with valid IPs found. Using loopback as last resort."
        found_ips+=("127.0.0.1")
        
        # Save extended debug info to help diagnose network issues
        {
            echo "================ NETWORK DEBUG INFO ================"
            echo "Date: $(date)"
            echo "Hostname: $(hostname 2>/dev/null || echo 'Unknown')"
            echo ""
            echo "--- IP Command Output ---"
            ip a 2>&1 || echo "ip command failed"
            echo ""
            echo "--- Route Table ---"
            ip route 2>&1 || echo "ip route command failed"
            echo ""
            echo "--- Interface List ---"
            ls -la /sys/class/net/ 2>&1 || echo "could not list network interfaces"
            echo ""
            echo "--- Debug Log ---"
            printf "%s\n" "${debug_info[@]}"
            echo "================ END DEBUG INFO ================"
        } >> "${WELCOME_LOG}" 2>/dev/null || true
    fi
    
    # Return the IPs
    echo "${found_ips[@]}"
}

# ==========================================
# Welcome Message Functions (from image.sh)
# ==========================================

welcome_log() {
    # Ensure log directory exists
    mkdir -p "$(dirname "$WELCOME_LOG")" 2>/dev/null || true
    
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$WELCOME_LOG" 2>/dev/null || true
    [ "${VERBOSE:-false}" = "true" ] && echo "$1"
}

get_system_info() {
    welcome_log "Gathering system information"
    
    # Get hostname with fallback
    local hostname=$(hostnamectl --pretty 2>/dev/null || hostname 2>/dev/null || echo "PersistenceOS")
    
    # Get kernel with fallback
    local kernel=$(uname -r 2>/dev/null || echo "Unknown")
    
    # Get uptime with fallback
    local uptime=$(uptime -p 2>/dev/null | sed 's/up //' || echo "Unknown")
    
    # Get CPU info with fallback
    local cpu=$(lscpu 2>/dev/null | grep 'Model name' | cut -d':' -f2 | xargs || echo "Unknown")
    
    # Get memory info with fallback
    local memory=$(free -h 2>/dev/null | awk '/Mem:/ {print $3 "/" $2}' || echo "Unknown")
    
    # Get storage info with fallback
    local storage=$(df -h / 2>/dev/null | awk 'NR==2 {print $3 "/" $2}' || echo "Unknown")
    
    # Build info array
    local info=(
        "System: ${hostname}"
        "OS: PersistenceOS ${PERSISTENCE_VERSION}"
        "Kernel: ${kernel}"
        "Uptime: ${uptime}"
        "CPU: ${cpu}"
        "Memory: ${memory}"
        "Storage: ${storage}"
    )
    
    # Get network interfaces with simplified function
    welcome_log "Getting network information"
    local network_ips=($(get_network_ips "motd"))
    
    if [ ${#network_ips[@]} -gt 0 ]; then
        for ip in "${network_ips[@]}"; do
            info+=("Network: Network Interface (${ip})")
        done
    else
        info+=("Network: Configuration required")
        welcome_log "No network interfaces detected"
    fi
    
    # Return the gathered information
    printf "%s\n" "${info[@]}"
    welcome_log "System information gathered successfully"
}

# ==========================================
# Generate PersistenceOS ASCII logo
# ==========================================
generate_ascii_logo() {
    welcome_log "Generating ASCII logo"
    local output_file="$1"
    
    # Try figlet with different fonts in order of preference
    if command -v figlet &>/dev/null; then
        # Try standard font first (most common)
        if figlet -f standard "PersistenceOS" > "$output_file" 2>/dev/null; then
            welcome_log "Generated logo with figlet standard font"
            return 0
        fi
        
        # Try slant font as fallback
        if figlet -f slant "PersistenceOS" > "$output_file" 2>/dev/null; then
            welcome_log "Generated logo with figlet slant font"
            return 0
        fi
        
        # Try any available font as last resort
        if figlet "PersistenceOS" > "$output_file" 2>/dev/null; then
            welcome_log "Generated logo with default figlet font"
            return 0
        fi
    fi
    
    # If figlet failed or isn't available, use our built-in ASCII art
    welcome_log "Using built-in ASCII art (figlet unavailable or failed)"
    cat > "$output_file" << 'EOFLOGO'
 ____               _     _                      ___  ____  
|  _ \ ___ _ __ ___(_)___| |_ ___ _ __   ___ ___|_ _|/ ___| 
| |_) / _ \ '__/ __| / __| __/ _ \ '_ \ / __/ _ \| | \___ \ 
|  __/  __/ |  \__ \ \__ \ ||  __/ | | | (_|  __/| |  ___) |
|_|   \___|_|  |___/_|___/\__\___|_| |_|\___\___|___||____/ 
EOFLOGO

    # Alternative smaller logo if the above doesn't display well
    if [ ! -s "$output_file" ]; then
        welcome_log "Using small fallback ASCII logo"
        cat > "$output_file" << 'EOFLOGO'
+---------------------+
| PersistenceOS 6.1.0 |
+---------------------+
EOFLOGO
    fi
    
    return 0
}

update_motd() {
    welcome_log "Updating MOTD and issue files"
    
    # Create temporary files with safer fallbacks
    local tmp_motd=$(mktemp 2>/dev/null || echo "/tmp/persistenceos_motd.$$")
    local tmp_issue=$(mktemp 2>/dev/null || echo "/tmp/persistenceos_issue.$$")
    
    # Generate the ASCII logo
    generate_ascii_logo "$tmp_motd"
    
    # Add version number under the logo
    echo "" >> "$tmp_motd" 2>/dev/null || true
    
    # Add welcome message
    echo -e "Welcome to PersistenceOS - Advanced Storage Management and Snapshot Functionality\n" >> "$tmp_motd" 2>/dev/null || true
    echo -e "System Information:" >> "$tmp_motd" 2>/dev/null || true

    # Append dynamic system info
    get_system_info >> "$tmp_motd" 2>/dev/null || echo "System information not available" >> "$tmp_motd"
    
    # Get web interface IPs using enhanced network detection
    local web_interfaces=($(get_network_ips "motd"))
    
    # Add decorative box for web interface section - make it more noticeable
    echo -e "\n╔════════════════════════════════════════════════════════════════╗" >> "$tmp_motd" 2>/dev/null || true
    echo -e "║                    \033[1;36mWEB INTERFACE ACCESS\033[0m                        ║" >> "$tmp_motd" 2>/dev/null || true
    echo -e "╚════════════════════════════════════════════════════════════════╝" >> "$tmp_motd" 2>/dev/null || true
    
    # Add interface information with improved color coding and formatting
    if [ ${#web_interfaces[@]} -gt 0 ]; then
        for ip in "${web_interfaces[@]}"; do
            # Display HTTP and HTTPS on separate lines with clear visual distinction
            echo -e "\n\033[1;32m▶ HTTP:  http://${ip}:${PERSISTENCE_WEB_PORT}\033[0m" >> "$tmp_motd" 2>/dev/null || true
            echo -e "\033[1;34m▶ HTTPS: https://${ip}:8443\033[0m  (secure connection)" >> "$tmp_motd" 2>/dev/null || true
        done
        
        # Add note about certificate
        echo -e "\n\033[1;33mNote: The web interface uses a self-signed certificate.\033[0m" >> "$tmp_motd" 2>/dev/null || true
    else
        # Fallback to showing loopback as a last resort with clear warning
        echo -e "\n\033[1;31m⚠ WARNING: Network interface not detected!\033[0m" >> "$tmp_motd" 2>/dev/null || true
        echo -e "\033[1;33mTemporary local access only (not accessible from other machines):\033[0m" >> "$tmp_motd" 2>/dev/null || true
        echo -e "\033[1;32m▶ HTTP:  http://127.0.0.1:${PERSISTENCE_WEB_PORT}\033[0m" >> "$tmp_motd" 2>/dev/null || true
        echo -e "\033[1;31mFor remote access, please configure networking and check with 'ip addr'.\033[0m" >> "$tmp_motd" 2>/dev/null || true
        echo -e "\033[1;31mRun 'systemctl restart persistenceos-config.service' after fixing network.\033[0m" >> "$tmp_motd" 2>/dev/null || true
    fi
    
    # Add decorative box for documentation section
    echo -e "\n╔════════════════════════════════════════════════════════════════╗" >> "$tmp_motd" 2>/dev/null || true
    echo -e "║ Documentation: persistenceos.org/docs                           ║" >> "$tmp_motd" 2>/dev/null || true
    echo -e "║ Support: persistenceos.org/support                              ║" >> "$tmp_motd" 2>/dev/null || true
    echo -e "╚════════════════════════════════════════════════════════════════╝" >> "$tmp_motd" 2>/dev/null || true
    
    # Copy to issue file (without ANSI color codes for console compatibility)
    if [ -f "$tmp_motd" ]; then
        cat "$tmp_motd" | sed -r "s/\x1B\[([0-9]{1,3}(;[0-9]{1,3})*)?[mGK]//g" > "$tmp_issue" 2>/dev/null || \
            cp "$tmp_motd" "$tmp_issue" 2>/dev/null || true
    else
        echo "PersistenceOS" > "$tmp_issue"
    fi
    
    # Add login prompt to issue file
    echo -e "\n\\l" >> "$tmp_issue" 2>/dev/null || true
    
    # Ensure target directories exist
    mkdir -p /etc 2>/dev/null || true
    
    # Move temporary files to final destinations with atomic operations when possible
    if [ -f "$tmp_motd" ]; then
        mv "$tmp_motd" /etc/motd 2>/dev/null || cp "$tmp_motd" /etc/motd 2>/dev/null || echo "PersistenceOS" > /etc/motd 2>/dev/null || true
        rm -f "$tmp_motd" 2>/dev/null || true  # Clean up if mv failed and cp was used
    fi
    
    if [ -f "$tmp_issue" ]; then
        mv "$tmp_issue" /etc/issue 2>/dev/null || cp "$tmp_issue" /etc/issue 2>/dev/null || echo "PersistenceOS" > /etc/issue 2>/dev/null || true
        rm -f "$tmp_issue" 2>/dev/null || true  # Clean up if mv failed and cp was used
    fi
    
    # Create additional files for various systems
    
    # For systems that use motd.dynamic
    if [ -d "/run" ]; then
        cp /etc/motd /run/motd.dynamic 2>/dev/null || true
    fi
    
    # For systems that use motd.d
    if [ -d "/etc/motd.d" ]; then
        ln -sf /etc/motd /etc/motd.d/persistenceos.motd 2>/dev/null || \
        cp /etc/motd /etc/motd.d/persistenceos.motd 2>/dev/null || true
    fi
    
    # Set correct permissions
    chmod 644 /etc/motd 2>/dev/null || true
    chmod 644 /etc/issue 2>/dev/null || true
    
    welcome_log "MOTD and issue files updated successfully"
    return 0
}

login_hook() {
    welcome_log "Login hook called"
    
    # Only display motd for interactive shells
    if [[ $- == *i* ]]; then
        local needs_update=false
        
        # Check if MOTD file exists and is readable
        if [ ! -f "/etc/motd" ] || [ ! -r "/etc/motd" ]; then
            welcome_log "MOTD file missing or not readable, forcing update"
            needs_update=true
        fi
        
        # Check MOTD content for issues
        if ! needs_update && grep -q "Network interface not detected" /etc/motd 2>/dev/null; then
            welcome_log "Network interface was previously not detected, checking again"
            # Get current IPs to check if network is now available
            local current_ips=($(get_network_ips "login_hook"))
            if [ ${#current_ips[@]} -gt 0 ] && [[ "${current_ips[0]}" != "127.0.0.1" ]]; then
                welcome_log "Network now available, updating MOTD"
                needs_update=true
            fi
        fi
        
        # Check if the MOTD is missing web interface information
        if ! needs_update && ! grep -q "http://" /etc/motd 2>/dev/null; then
            welcome_log "No web interface URL found in MOTD, forcing update"
            needs_update=true
        fi
        
        # Check if the MOTD shows the correct web port
        if ! needs_update && ! grep -q ":${PERSISTENCE_WEB_PORT}" /etc/motd 2>/dev/null; then
            welcome_log "MOTD contains incorrect web port, updating"
            needs_update=true
        fi
        
        # Check the age of MOTD file
        if ! needs_update && [ -f "/etc/motd" ]; then
            local motd_age=$(( $(date +%s) - $(stat -c %Y /etc/motd 2>/dev/null || echo 0) ))
            # Update if older than 1 day (86400 seconds)
            if [ $motd_age -gt 86400 ]; then
                welcome_log "MOTD is more than 1 day old, refreshing"
                needs_update=true
            fi
        fi
        
        # Update if needed
        if [ "$needs_update" = "true" ]; then
            welcome_log "Updating MOTD due to detected conditions"
            # Force update - sleep briefly to let network settle
            sleep 1
            update_motd
            
            # If update succeeded, display the updated MOTD
            if [ -f "/etc/motd" ] && [ -r "/etc/motd" ]; then
                welcome_log "Displaying updated MOTD"
                cat /etc/motd
            fi
        fi
    fi
    
    return 0
}

force_update_and_display() {
    welcome_log "Forcing update and display"
    update_motd
    cat /etc/motd
}

# ==========================================
# Health Checker Functions
# ==========================================

health_log() {
    # Ensure log directory exists
    mkdir -p "$(dirname "$HEALTH_CHECK_LOG")" 2>/dev/null || true
    
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$HEALTH_CHECK_LOG"
    [ "${VERBOSE:-false}" = "true" ] && echo "$1"
}

ensure_health_checker_dirs() {
    # Ensure required directories exist
    mkdir -p "/usr/libexec/health-checker" 2>/dev/null || true
    mkdir -p "/etc/health-checker.d" 2>/dev/null || true
    mkdir -p "${PERSISTENCE_ROOT}/health" 2>/dev/null || true
}

create_health_checker_hooks() {
    health_log "Creating health checker hooks"
    
    # Create the etc-overlayfs.sh hook that was failing
    if [ ! -f "/usr/libexec/health-checker/etc-overlayfs.sh" ]; then
        cat > "/usr/libexec/health-checker/etc-overlayfs.sh" << 'EOF'
#!/bin/bash
# Check overlayfs integrity

check() {
    # Check if /etc is mounted as overlay
    if ! findmnt -t overlay /etc > /dev/null 2>&1; then
        echo "ERROR: /etc is not mounted as overlay"
        return 1
    fi
    
    # Check if /etc has the correct permissions
    if [ "$(stat -c %a /etc)" != "755" ]; then
        echo "WARNING: /etc has incorrect permissions"
    fi
    
    # Check if we can write to /etc
    if ! touch /etc/.health-checker-test 2>/dev/null; then
        echo "ERROR: Cannot write to /etc"
        return 1
    else
        rm -f /etc/.health-checker-test 2>/dev/null
    fi
    
    return 0
}

# Handle command line argument
if [ "$1" = "check" ]; then
    check
fi

exit 0
EOF
        chmod 755 "/usr/libexec/health-checker/etc-overlayfs.sh"
        health_log "Created etc-overlayfs.sh hook"
    fi
    
    # Create our custom PersistenceOS health checker hook
    if [ ! -f "/usr/libexec/health-checker/persistenceos.sh" ]; then
        cat > "/usr/libexec/health-checker/persistenceos.sh" << 'EOF'
#!/bin/bash
# PersistenceOS health check

check() {
    # Check if core PersistenceOS directories exist
    if [ ! -d "/usr/lib/persistence" ]; then
        echo "ERROR: /usr/lib/persistence directory is missing"
        return 1
    fi
    
    if [ ! -d "/var/lib/persistence" ]; then
        echo "ERROR: /var/lib/persistence directory is missing"
        return 1
    fi
    
    # Check if nginx is installed and enabled
    if ! command -v nginx >/dev/null 2>&1; then
        echo "WARNING: nginx is not installed"
    elif ! systemctl is-enabled nginx >/dev/null 2>&1; then
        echo "WARNING: nginx is not enabled"
    fi
    
    # Check if web server is accessible (but don't fail the check on this)
    if command -v curl >/dev/null 2>&1; then
        if ! curl -f -s -m 2 http://localhost:8080 >/dev/null 2>&1; then
            echo "WARNING: Web interface is not accessible on port 8080"
        fi
    fi
    
    # All critical checks passed
    return 0
}

# Handle command line argument
if [ "$1" = "check" ]; then
    check
fi

exit 0
EOF
        chmod 755 "/usr/libexec/health-checker/persistenceos.sh"
        health_log "Created persistenceos.sh hook"
    fi
    
    # Create crio service handler hook
    if [ ! -f "/usr/libexec/health-checker/crio-handler.sh" ]; then
        cat > "/usr/libexec/health-checker/crio-handler.sh" << 'EOF'
#!/bin/bash
# Handle crio service dependency for health-checker

check() {
    # Check if crio service exists - if not, that's fine for PersistenceOS
    if ! systemctl list-unit-files crio.service >/dev/null 2>&1; then
        echo "INFO: crio.service not found - this is expected in PersistenceOS"
        return 0
    fi
    
    # If crio exists, check its status
    if systemctl is-active crio.service >/dev/null 2>&1; then
        echo "INFO: crio.service is active"
    else
        echo "WARNING: crio.service exists but is not active"
    fi
    
    return 0
}

# Handle command line argument
if [ "$1" = "check" ]; then
    check
fi

exit 0
EOF
        chmod 755 "/usr/libexec/health-checker/crio-handler.sh"
        health_log "Created crio-handler.sh hook to properly handle crio service dependency"
    fi
    
    health_log "Health checker hooks created"
}

configure_health_checker() {
    health_log "Configuring health checker"
    
    # Create health checker configuration file
    if [ ! -f "/etc/health-checker.d/persistenceos.conf" ]; then
        cat > "/etc/health-checker.d/persistenceos.conf" << 'EOF'
# PersistenceOS Health Checker configuration
CHECK_INTERVAL=600
SNAPSHOT_CHECK=yes
OVERLAY_CHECK=yes
UPDATE_CHECK=yes
SERVICE_CHECK=yes
BTRFS_CHECK=yes
CRIO_CHECK=no
EOF
        chmod 644 "/etc/health-checker.d/persistenceos.conf"
        health_log "Created health checker configuration"
    fi
    
    # Create an override file for the health-checker service to prevent it from failing due to crio
    mkdir -p "/etc/systemd/system/health-checker.service.d" 2>/dev/null || true
    cat > "/etc/systemd/system/health-checker.service.d/override.conf" << 'EOF'
[Service]
# Don't fail the service if crio isn't available
ExecStartPre=/bin/bash -c 'if ! systemctl list-unit-files crio.service >/dev/null 2>&1; then touch /run/crio-not-required; fi'
ExecStartPost=/bin/bash -c 'rm -f /run/crio-not-required 2>/dev/null || true'
EOF
    chmod 644 "/etc/systemd/system/health-checker.service.d/override.conf"
    health_log "Created health-checker service override to handle missing crio service"
    
    # Create PersistenceOS health report template
    if [ ! -f "${PERSISTENCE_ROOT}/health/report-template.html" ]; then
        cat > "${PERSISTENCE_ROOT}/health/report-template.html" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>PersistenceOS Health Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #2c3e50; }
        .status-ok { color: green; }
        .status-warning { color: orange; }
        .status-error { color: red; }
        table { border-collapse: collapse; width: 100%; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>PersistenceOS Health Report</h1>
    <p>Report generated: {{DATE}}</p>
    
    <h2>System Status: <span class="status-{{OVERALL_STATUS}}">{{OVERALL_STATUS_TEXT}}</span></h2>
    
    <table>
        <tr>
            <th>Component</th>
            <th>Status</th>
            <th>Details</th>
        </tr>
        {{HEALTH_ITEMS}}
    </table>
</body>
</html>
EOF
        chmod 644 "${PERSISTENCE_ROOT}/health/report-template.html"
        health_log "Created health report template"
    fi
    
    health_log "Health checker configuration complete"
}

run_health_check() {
    health_log "Running health check"
    
    # Ensure health checker directories exist
    ensure_health_checker_dirs
    
    # Create health checker hooks
    create_health_checker_hooks
    
    # Configure health checker
    configure_health_checker
    
    # Apply systemd changes
    health_log "Reloading systemd to apply changes"
    systemctl daemon-reload 2>/dev/null || true
    
    # Restart health-checker service to apply new configuration
    if systemctl is-active health-checker.service >/dev/null 2>&1; then
        health_log "Restarting health-checker service"
        systemctl restart health-checker.service 2>/dev/null || true
    else
        health_log "Starting health-checker service"
        systemctl start health-checker.service 2>/dev/null || true
    fi
    
    # Wait a moment for service to initialize
    sleep 2
    
    # Check service status
    if systemctl is-active health-checker.service >/dev/null 2>&1; then
        health_log "Health checker service is active"
        echo "Health checker service is active"
    else
        health_log "Health checker service failed to start. Checking logs..."
        echo "Health checker service failed to start. See journalctl output:"
        journalctl -u health-checker.service -n 20 --no-pager
    fi
    
    # Run the health-checker command with our hooks
    if command -v health-checker >/dev/null 2>&1; then
        echo "Running health-checker command..."
        health-checker check
        local result=$?
        if [ $result -eq 0 ]; then
            health_log "Health check passed successfully"
            echo "Health check passed successfully"
        else
            health_log "Health check returned exit code $result"
            echo "Health check returned exit code $result - check logs for details"
        fi
    else
        health_log "health-checker command not found"
        echo "health-checker command not found - ensure it is installed with 'zypper install health-checker'"
    fi
    
    health_log "Health check process completed"
}

# ==========================================
# Main command handling
# ==========================================

main() {
    # Check for flags
    case "$1" in
        --update)
            # Update welcome message
            update_motd
            log_info "Welcome message updated"
            ;;
        --login)
            # Display welcome message on login
            login_hook
            ;;
        --force)
            # Force update and display welcome message
            force_update_and_display
            ;;
        --health-check)
            # Run health checker
            run_health_check
            ;;
        --help)
            echo "Usage: $0 {--update|--login|--force|--health-check|--help}"
            echo
            echo "Options:"
            echo "  --update       Update the welcome message"
            echo "  --login        Display welcome message (for login hooks)"
            echo "  --force        Force update and display welcome message"
            echo "  --health-check Run health checker integration"
            echo "  --help         Show this help message"
            ;;
        *)
            # Run full configuration if no flags provided or invalid
            log_info "Running full system configuration"
            set_permissions
            configure_services
            configure_network_manager
            configure_nginx
            configure_fastapi
            create_version_file
            setup_vm_templates
            setup_snapshots
            setup_profile_script
            setup_agetty_issue
            setup_bashrc
            create_health_checker_hooks
            configure_health_checker
            install_utility_scripts
            update_motd
            log_success "System configuration completed successfully"
            ;;
    esac
}

# Configure systemd services
configure_services() {
    log_info "Configuring services"

    # Create systemd directory if it doesn't exist
    ensure_directory "/etc/systemd/system"

    # Copy service files to systemd directory if they exist
    for service in "${PERSISTENCE_SERVICES}"/*.service; do
        if [ -f "${service}" ]; then
            cp "${service}" "/etc/systemd/system/" 2>/dev/null || true
            log_info "Copied service: ${service} -> /etc/systemd/system/"
                    fi
                done

    # Copy timer files if they exist
    for timer in "${PERSISTENCE_SERVICES}"/*.timer; do
        if [ -f "${timer}" ]; then
            cp "${timer}" "/etc/systemd/system/" 2>/dev/null || true
            log_info "Copied timer: ${timer} -> /etc/systemd/system/"
                    fi
                done

    # Enable core services
    log_info "Enabling core services"
    if [ -f "/etc/systemd/system/persistenceos-core-services.service" ]; then
        systemctl enable persistenceos-core-services.service 2>/dev/null || true
        log_info "Enabled persistenceos-core-services.service"
        
        # Verify core services configuration
        if grep -q "welcome" "/etc/systemd/system/persistenceos-core-services.service" 2>/dev/null; then
            log_info "Welcome screen configuration found in core services"
        else
            log_warning "Welcome screen configuration not found in core services"
        fi
    else
        # Fallback to direct service enablement if core service file is missing
        log_warning "Core services file not found, enabling services directly"
        systemctl enable NetworkManager 2>/dev/null || true
        systemctl enable nginx 2>/dev/null || true
        log_info "Enabled NetworkManager and nginx directly"
    fi
    
    # Create systemd drop-in directory for graphical target
    ensure_directory "/etc/systemd/system/graphical.target.d"

    # Copy graphical target configuration if available
    if [ -d "${PERSISTENCE_SERVICES}/graphical.target.d" ]; then
        cp -r "${PERSISTENCE_SERVICES}/graphical.target.d"/* "/etc/systemd/system/graphical.target.d/" 2>/dev/null || true
        log_info "Copied graphical target configuration"
    fi

    log_success "Services configured successfully"
}

# Configure NetworkManager
configure_network_manager() {
    log_info "Configuring NetworkManager"

    # Create configuration directories
    ensure_directory "/etc/NetworkManager/conf.d"
    ensure_directory "/etc/NetworkManager/system-connections"

    # Configure global network management
    cat > "/etc/NetworkManager/conf.d/10-globally-managed-devices.conf" << 'EOF' 2>/dev/null || true
[keyfile]
unmanaged-devices=none
EOF

    # Create default ethernet connection profile
    cat > "/etc/NetworkManager/system-connections/ethernet-default.nmconnection" << 'EOF' 2>/dev/null || true
[connection]
id=ethernet-default
type=ethernet
interface-name=*
autoconnect=true

[ipv4]
method=auto

[ipv6]
method=auto
EOF

    # Secure the connection file
    chmod 600 "/etc/NetworkManager/system-connections/ethernet-default.nmconnection" 2>/dev/null || true

    log_success "NetworkManager configured successfully"
}

# Configure Nginx web server
configure_nginx() {
    log_info "Configuring Nginx"

    # Create configuration directories
    ensure_directory "${NGINX_CONF_DIR}/conf.d"
    ensure_directory "${NGINX_CONF_DIR}/ssl"

    # Generate self-signed SSL certificate if missing
    if [ ! -f "${NGINX_CONF_DIR}/ssl/persistenceos.crt" ]; then
        log_info "Generating self-signed SSL certificate"
        openssl req -x509 -nodes -days 3650 -newkey rsa:2048 \
            -keyout "${NGINX_CONF_DIR}/ssl/persistenceos.key" \
            -out "${NGINX_CONF_DIR}/ssl/persistenceos.crt" \
            -subj "/C=US/ST=State/L=City/O=PersistenceOS/CN=persistenceos.local" 2>/dev/null || \
            log_warning "SSL certificate generation failed"
        
        # Set secure permissions
        chmod 600 "${NGINX_CONF_DIR}/ssl/persistenceos.key" 2>/dev/null || true
        chmod 644 "${NGINX_CONF_DIR}/ssl/persistenceos.crt" 2>/dev/null || true
    fi

    # Create Nginx configuration
    cat > "${NGINX_CONF_DIR}/conf.d/persistenceos.conf" << 'EOF' 2>/dev/null || true
# PersistenceOS Nginx Configuration
# This file configures Nginx to serve the PersistenceOS web UI and proxy API requests

# Define upstream for FastAPI backend
upstream persistenceos_api {
    server 127.0.0.1:8000;
    keepalive 64;
}

# Main server configuration
server {
    listen 8080 default_server;
    listen [::]:8080 default_server;

    # Root directory for web UI files
    root /usr/lib/persistence/web-ui;
    
    # Default index files
    index login.html index.html;

    # Main location block for root access
    location = / {
        # First try login.html, then fall back to index.html
        try_files /login.html /index.html =404;
    }
    
    # Static files - serve all regular files
    location / {
        # Try exact match, then with .html extension, then directory index, then fall back to login page
        try_files $uri $uri.html $uri/ /login.html;
    }
    
    # Create specific location for the login page to ensure it's properly handled
    location = /login.html {
        # Add debug headers to see if this block is being accessed
        add_header X-Debug-Status "login.html served directly" always;
    }
    
    # Create specific location for the index page
    location = /index.html {
        # Add debug headers to see if this block is being accessed
        add_header X-Debug-Status "index.html served directly" always;
    }
    
    # Serve static assets
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri =404;
    }

    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_buffering off;
    }

    # Favicon
    location = /favicon.ico {
        access_log off;
        log_not_found off;
    }
    
    # Custom error pages
    error_page 404 /404.html;
    location = /404.html {
        root /usr/lib/persistence/web-ui;
        internal;
    }
    
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/lib/persistence/web-ui;
        internal;
    }
    
    # Enable debug logging for troubleshooting
    error_log /var/log/nginx/persistenceos-error.log debug;
    access_log /var/log/nginx/persistenceos-access.log;

    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
}

server {
    listen 8443 ssl http2;
    server_name _;

    ssl_certificate /etc/nginx/ssl/persistenceos.crt;
    ssl_certificate_key /etc/nginx/ssl/persistenceos.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    ssl_session_tickets off;
    
    add_header Strict-Transport-Security "max-age=63072000" always;
    
    root /usr/lib/persistence/web-ui;
    index login.html index.html;

    # Main location block for root access
    location = / {
        # First try login.html, then fall back to index.html
        try_files /login.html /index.html =404;
    }
    
    # Static files - serve all regular files
    location / {
        # Try exact match, then with .html extension, then directory index, then fall back to login page
        try_files $uri $uri.html $uri/ /login.html;
    }
    
    # Create specific location for the login page to ensure it's properly handled
    location = /login.html {
        # Add debug headers to see if this block is being accessed
        add_header X-Debug-Status "login.html served directly" always;
    }
    
    # Create specific location for the index page
    location = /index.html {
        # Add debug headers to see if this block is being accessed
        add_header X-Debug-Status "index.html served directly" always;
    }

    # Serve static assets
    location ~* \.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri =404;
    }
    
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_buffering off;
    }

    location /api/vm/console/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
        proxy_cache off;
        proxy_read_timeout 86400;
    }

    # Custom error pages
    error_page 404 /404.html;
    location = /404.html {
        root /usr/lib/persistence/web-ui;
        internal;
    }
    
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/lib/persistence/web-ui;
        internal;
    }
    
    # Enable debug logging for troubleshooting
    error_log /var/log/nginx/persistenceos-error.log debug;
    access_log /var/log/nginx/persistenceos-access.log;

    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; font-src 'self'; connect-src 'self'";
}
EOF

    log_success "Nginx configured successfully"
}

# Configure FastAPI for the web UI
configure_fastapi() {
    log_info "Configuring FastAPI service"

    # Create API directory structure
    ensure_directory "${PERSISTENCE_API}"
    ensure_directory "${PERSISTENCE_API}/persistenceos"
    ensure_directory "${PERSISTENCE_API}/persistenceos/routers"
    ensure_directory "${PERSISTENCE_API}/persistenceos/utils"

    # Create minimal API file if needed
    if [ ! -f "${PERSISTENCE_API}/app.py" ]; then
        log_warning "Main app.py not found in API directory, creating minimal version"
        cat > "${PERSISTENCE_API}/app.py" << 'EOF' 2>/dev/null || true
#!/usr/bin/env python3
from fastapi import FastAPI

app = FastAPI(
    title="PersistenceOS API",
    version="6.1.0",
    docs_url="/api/docs",
    redoc_url=None
)

@app.get("/api/health")
async def health_check():
    return {"status": "ok", "version": "6.1.0"}

@app.get("/api/vm")
async def list_vms():
    return {"vms": []}

@app.get("/api/storage")
async def storage_status():
    return {"storage": []}
EOF
        chmod 755 "${PERSISTENCE_API}/app.py" 2>/dev/null || true
    fi

    # Create systemd service if missing
    if [ ! -f "/etc/systemd/system/persistenceos-api.service" ]; then
        log_info "Creating API systemd service"
        cat > "/etc/systemd/system/persistenceos-api.service" << 'EOF' 2>/dev/null || true
[Unit]
Description=PersistenceOS API Service
After=network.target

[Service]
User=root
WorkingDirectory=/usr/lib/persistence/api
ExecStart=/usr/bin/uvicorn app:app --host 127.0.0.1 --port 8000
Restart=always
Environment="PYTHONPATH=/usr/lib/persistence/api"

[Install]
WantedBy=multi-user.target
EOF
        systemctl daemon-reload 2>/dev/null || true
        systemctl enable persistenceos-api.service 2>/dev/null || true
    fi

    log_success "FastAPI service configured successfully"
}

# Create version file with current version information
create_version_file() {
    log_info "Creating version file"

    # Create version file in /etc
    cat > "/etc/persistenceos-version" << EOF 2>/dev/null || true
PERSISTENCE_VERSION="${PERSISTENCE_VERSION}"
BUILD_DATE="$(date +%Y-%m-%d)"
BUILD_TYPE="production"
EOF
    
    # Create version file in /usr/lib/persistence
    cat > "${PERSISTENCE_ROOT}/version" << EOF 2>/dev/null || true
PERSISTENCE_VERSION="${PERSISTENCE_VERSION}"
BUILD_DATE="$(date +%Y-%m-%d)"
BUILD_TYPE="production"
EOF
    
    # Set proper permissions
    chmod 644 "/etc/persistenceos-version" 2>/dev/null || true
    chmod 644 "${PERSISTENCE_ROOT}/version" 2>/dev/null || true
    
    log_success "Version files created successfully"
}

# Set up VM template directories
setup_vm_templates() {
    log_info "Setting up VM templates"
    
    # Create VM template directory if it doesn't exist
    ensure_directory "${PERSISTENCE_VAR}/vm/templates"
    
    # Create a default VM template placeholder if missing
    if [ ! -f "${PERSISTENCE_VAR}/vm/templates/README.txt" ]; then
        log_info "Creating default VM template placeholder"
        cat > "${PERSISTENCE_VAR}/vm/templates/README.txt" << 'EOF' 2>/dev/null || true
Place VM template images (qcow2/raw) in this directory.
Recommended naming: <os>-<version>-<type>.<format>
EOF
    fi
    
    log_success "VM templates setup completed"
}

# Set up snapshot directories and configuration
setup_snapshots() {
    log_info "Setting up snapshot directory"
    
    # Create snapshot directory if it doesn't exist
    if [ ! -d "/.snapshots" ]; then
        mkdir -p /.snapshots 2>/dev/null || true
        chmod 750 /.snapshots 2>/dev/null || true
        log_info "Created snapshots directory"
    fi

    # Configure snapper if available
    if command -v snapper &> /dev/null; then
        if ! snapper list-configs 2>/dev/null | grep -q "root"; then
            log_info "Creating snapper configuration for root filesystem"
            snapper --no-dbus create-config / 2>/dev/null || log_warning "Snapper config creation failed"
        fi
    else
        log_info "Snapper not installed, skipping configuration"
    fi
    
    log_success "Snapshot setup completed"
}

# Setup profile.d script for welcome message on login
setup_profile_script() {
    log_info "Setting up login message integration"
    
    # Instead of creating a separate script, we'll directly modify the system profile
    if [ -f "/etc/profile" ]; then
        # Check if the profile already has our hook
        if ! grep -q "PersistenceOS welcome message" "/etc/profile" 2>/dev/null; then
            log_info "Adding welcome message hook to /etc/profile"
            cat >> "/etc/profile" << EOF 2>/dev/null || true

# Display PersistenceOS welcome message on login for interactive shells
if [[ \$- == *i* ]]; then
    if [[ -f /usr/lib/persistence/bin/image ]]; then
        /usr/lib/persistence/bin/image --login
    elif [[ -f /usr/lib/persistence/scripts/config.sh ]]; then
        /usr/lib/persistence/scripts/config.sh --login
    fi
fi
EOF
        fi
    fi
    
    log_success "Login message integration complete"
}

# Configure agetty to display the issue file
setup_agetty_issue() {
    log_info "Configuring agetty for pre-login display"
    
    # Configure getty to show issue file
    mkdir -p /etc/systemd/system/getty@.service.d/ 2>/dev/null || true
    cat > /etc/systemd/system/getty@.service.d/persistenceos.conf << EOF 2>/dev/null || true
[Service]
# Prevent screen clearing to show MOTD
ExecStart=
ExecStart=-/sbin/agetty --noclear --keep-baud %I 115200,38400,9600 \$TERM
EOF
    
    systemctl daemon-reload 2>/dev/null || true
    log_success "Agetty issue setup complete"
}

# Configure bashrc for welcome message
setup_bashrc() {
    log_info "Setting up bashrc integration"
    
    # Add to skeleton bashrc for new users
    if [ -f "/etc/skel/.bashrc" ]; then
        if ! grep -q "PersistenceOS welcome message" "/etc/skel/.bashrc" 2>/dev/null; then
            log_info "Adding welcome message hook to skeleton bashrc"
            cat >> "/etc/skel/.bashrc" << EOF 2>/dev/null || true

# Display PersistenceOS welcome message on login for interactive shells
if [[ \$- == *i* ]]; then
    if [[ -f /usr/lib/persistence/bin/image ]]; then
        /usr/lib/persistence/bin/image --login
    elif [[ -f /usr/lib/persistence/scripts/config.sh ]]; then
        /usr/lib/persistence/scripts/config.sh --login
    fi
fi
EOF
        fi
    fi
    
    # Apply to existing users
    for user_home in /home/<USER>/ /root/; do
        if [ -d "$user_home" ]; then
            if [ -f "${user_home}.bashrc" ]; then
                if ! grep -q "PersistenceOS welcome message" "${user_home}.bashrc" 2>/dev/null; then
                    log_info "Adding welcome message hook to ${user_home}.bashrc"
                    cat >> "${user_home}.bashrc" << EOF 2>/dev/null || true

# Display PersistenceOS welcome message on login for interactive shells
if [[ \$- == *i* ]]; then
    if [[ -f /usr/lib/persistence/bin/image ]]; then
        /usr/lib/persistence/bin/image --login
    elif [[ -f /usr/lib/persistence/scripts/config.sh ]]; then
        /usr/lib/persistence/scripts/config.sh --login
    fi
fi
EOF
                fi
            fi
        fi
    done
    
    log_success "Bashrc integration complete"
}

# Install utility scripts to system path
install_utility_scripts() {
    log_info "Installing utility scripts"
    
    # Install get-web-ip.sh if it exists
    if [ -f "${PERSISTENCE_SCRIPTS}/get-web-ip.sh" ]; then
        cp "${PERSISTENCE_SCRIPTS}/get-web-ip.sh" "${PERSISTENCE_BIN}/get-web-ip" 2>/dev/null || true
        chmod 755 "${PERSISTENCE_BIN}/get-web-ip" 2>/dev/null || true
        
        # Create a symlink in /usr/local/bin for system-wide access
        ln -sf "${PERSISTENCE_BIN}/get-web-ip" "/usr/local/bin/get-web-ip" 2>/dev/null || true
        log_info "Installed get-web-ip script"
    else
        log_warning "get-web-ip.sh script not found"
    fi
    
    # Add more utility scripts here as needed
    
    log_success "Utility scripts installed successfully"
}

# Trap to catch any errors and clean up
trap 'exit_code=$?; if [ $exit_code -ne 0 ]; then log_error "Script failed with exit code $exit_code"; else log_success "Script completed successfully"; fi; exit 0' EXIT

# Run the main function with arguments and ensure we exit with success
main "$@" || true
exit 0