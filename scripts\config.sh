#!/bin/bash
set -e
set -o pipefail

# PersistenceOS Configuration Script - CLEAN VERSION
# This script contains ONLY bash code, no Python/FastAPI content

# --- 0. Error Handling and Logging ---
log_info() {
    echo "[INFO] $1"
}

log_error() {
    echo "[ERROR] $1" >&2
    exit 1
}

# --- 1. Define Paths ---
PERSISTENCE_ROOT="/usr/lib/persistence"
BIN="${PERSISTENCE_ROOT}/bin"
SERVICES="${PERSISTENCE_ROOT}/services"
WEBUI="${PERSISTENCE_ROOT}/web-ui"
LOGDIR="/var/log/persistence"

# --- FUNCTION DEFINITIONS ---

# Deploy self-contained login
deploy_self_contained_login() {
    log_info "Deploying self-contained login.js (no HTML dependencies)..."

    # Create minimal login endpoint file for web server compatibility
    mkdir -p "/var/lib/persistence/web-ui" "/usr/lib/persistence/web-ui"

    # Create a minimal login.html that just loads the self-contained login.js
    cat > "/var/lib/persistence/web-ui/login.html" <<'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login</title>
    <link rel="icon" href="img/favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- Self-contained login.js will create the entire page -->
    <script src="js/login.js"></script>
</body>
</html>
EOF

    # Copy to usr location as well
    cp "/var/lib/persistence/web-ui/login.html" "/usr/lib/persistence/web-ui/login.html"

    # Set permissions
    chmod 644 "/var/lib/persistence/web-ui/login.html" "/usr/lib/persistence/web-ui/login.html"

    log_info "✅ Self-contained login deployment complete"
}

# Setup API directory and copy API files
setup_api_directory() {
    log_info "Setting up API directory structure and copying API files..."

    # Create API directory structure
    mkdir -p "$PERSISTENCE_ROOT/api"
    chmod 755 "$PERSISTENCE_ROOT/api"

    # Install API files from source if available
    for api_file in "run_api.sh" "main.py"; do
        log_info "Installing ${api_file}..."

        API_LOCATIONS=(
            "/usr/src/packages/SOURCES/${api_file}"
            "/usr/src/packages/SOURCES/@${api_file}"
            "/usr/src/packages/SOURCES/@usr/lib/persistence/api/${api_file}"
            "/usr/src/packages/SOURCES/usr/lib/persistence/api/${api_file}"
            "usr/lib/persistence/api/${api_file}"
            "${api_file}"
            "$(pwd)/${api_file}"
            "/image/${api_file}"
            "/usr/src/packages/BUILD/${api_file}"
            "/usr/src/packages/BUILDROOT/${api_file}"
            "/home/<USER>/rpmbuild/SOURCES/${api_file}"
            "/home/<USER>/${api_file}"
            "./usr/lib/persistence/api/${api_file}"
            "usr/src/packages/SOURCES/${api_file}"
            "/var/tmp/build-root/usr/src/packages/SOURCES/${api_file}"
        )

        API_COPIED="false"
        for api_location in "${API_LOCATIONS[@]}"; do
            if [ -f "$api_location" ]; then
                file_size=$(stat -c%s "$api_location" 2>/dev/null || echo "unknown")
                log_info "✅ ${api_file} found in SOURCES: $api_location ($file_size bytes)"
                cp "$api_location" "$PERSISTENCE_ROOT/api/${api_file}"
                if [ "${api_file}" = "run_api.sh" ]; then
                    chmod 755 "$PERSISTENCE_ROOT/api/${api_file}"
                else
                    chmod 644 "$PERSISTENCE_ROOT/api/${api_file}"
                fi
                API_COPIED="true"
                break
            fi
        done

        if [ "$API_COPIED" = "false" ]; then
            log_info "⚠️  ${api_file} not found in any location - will be created by ensure_systemd_services()"
        fi
    done

    log_info "✅ API directory setup complete"
}

# Setup static directory structure and copy JavaScript files
setup_static_directory() {
    log_info "Setting up static directory structure and copying JavaScript files..."

    # Create directory structure
    mkdir -p "$WEBUI/js" "$WEBUI/css" "$WEBUI/img"
    mkdir -p "/var/lib/persistence/web-ui/js" "/var/lib/persistence/web-ui/css" "/var/lib/persistence/web-ui/img"

    # Set permissions
    chmod 755 "$WEBUI" "$WEBUI/js" "$WEBUI/css" "$WEBUI/img"
    chmod 755 "/var/lib/persistence/web-ui" "/var/lib/persistence/web-ui/js" "/var/lib/persistence/web-ui/css" "/var/lib/persistence/web-ui/img"

    # Debug: List SOURCES directory contents if it exists
    if [ -d "/usr/src/packages/SOURCES" ]; then
        log_info "SOURCES directory exists, listing contents for debugging..."
        find /usr/src/packages/SOURCES -name "*.js" -type f 2>/dev/null | head -10 | while read jsfile; do
            log_info "  Found JS file: $jsfile ($(stat -c%s "$jsfile" 2>/dev/null || echo "unknown") bytes)"
        done

        # Check for @ prefix files (OBS notation)
        for js_file in "@app.js" "@auth.js" "@vue.js" "@login.js"; do
            if [ -f "/usr/src/packages/SOURCES/$js_file" ]; then
                file_size=$(stat -c%s "/usr/src/packages/SOURCES/$js_file" 2>/dev/null || echo "unknown")
                log_info "✅ Found OBS @ prefix file: $js_file ($file_size bytes)"
            fi
        done

        # Check for nested structure in SOURCES
        if [ -d "/usr/src/packages/SOURCES/usr" ]; then
            log_info "Found nested usr directory in SOURCES"
            find /usr/src/packages/SOURCES/usr -name "*.js" -type f 2>/dev/null | while read jsfile; do
                log_info "  Found JS file: $jsfile ($(stat -c%s "$jsfile" 2>/dev/null || echo "unknown") bytes)"
            done
        fi
    else
        log_info "SOURCES directory does not exist: /usr/src/packages/SOURCES"
    fi

    # Install JavaScript files - Check if already installed by KIWI overlay first
    log_info "Checking for JavaScript files..."

    for js_name in "app" "auth" "vue" "login"; do
        log_info "Processing ${js_name}.js..."

        # Check if file already exists from KIWI overlay (Option B)
        if [ -f "$WEBUI/js/${js_name}.js" ]; then
            file_size=$(stat -c%s "$WEBUI/js/${js_name}.js" 2>/dev/null || echo "unknown")
            log_info "✅ ${js_name}.js already installed by KIWI overlay ($file_size bytes)"
            # Ensure backup copy exists
            mkdir -p "/var/lib/persistence/web-ui/js"
            cp "$WEBUI/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || true
            chmod 644 "$WEBUI/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || true
            continue
        fi

        # Check if file exists in the build root (KIWI may have placed it here)
        if [ -f "/usr/lib/persistence/web-ui/js/${js_name}.js" ]; then
            file_size=$(stat -c%s "/usr/lib/persistence/web-ui/js/${js_name}.js" 2>/dev/null || echo "unknown")
            log_info "✅ ${js_name}.js found in build root ($file_size bytes)"
            # Copy to both locations
            mkdir -p "$WEBUI/js" "/var/lib/persistence/web-ui/js"
            cp "/usr/lib/persistence/web-ui/js/${js_name}.js" "$WEBUI/js/${js_name}.js"
            cp "/usr/lib/persistence/web-ui/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js"
            chmod 644 "$WEBUI/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js"
            continue
        fi

        # If not found, try to locate and install from various sources
        JS_LOCATIONS=(
            "/image/${js_name}.js"
            "/usr/src/packages/SOURCES/${js_name}.js"
            "${js_name}.js"
            "$(pwd)/${js_name}.js"
            "/usr/src/packages/SOURCES/usr/lib/persistence/web-ui/js/${js_name}.js"
            "usr/lib/persistence/web-ui/js/${js_name}.js"
            "/usr/src/packages/BUILD/${js_name}.js"
            "/usr/src/packages/BUILDROOT/${js_name}.js"
            "/home/<USER>/rpmbuild/SOURCES/${js_name}.js"
            "/home/<USER>/${js_name}.js"
            "./usr/lib/persistence/web-ui/js/${js_name}.js"
            "usr/src/packages/SOURCES/${js_name}.js"
            "/var/tmp/build-root/usr/src/packages/SOURCES/${js_name}.js"
            "/usr/src/packages/SOURCES/@${js_name}.js"
            "/usr/src/packages/SOURCES/@usr/lib/persistence/web-ui/js/${js_name}.js"
        )

        JS_COPIED="false"
        for js_location in "${JS_LOCATIONS[@]}"; do
            if [ -f "$js_location" ]; then
                file_size=$(stat -c%s "$js_location" 2>/dev/null || echo "unknown")
                log_info "✅ ${js_name}.js found in SOURCES: $js_location ($file_size bytes)"
                cp "$js_location" "$WEBUI/js/${js_name}.js"
                cp "$js_location" "/var/lib/persistence/web-ui/js/${js_name}.js"
                chmod 644 "$WEBUI/js/${js_name}.js" "/var/lib/persistence/web-ui/js/${js_name}.js"
                JS_COPIED="true"
                break
            fi
        done

        if [ "$JS_COPIED" = "false" ]; then
            log_info "⚠️  ${js_name}.js not found in any location"

            # Create fallback for login.js only
            if [ "$js_name" = "login" ]; then
                log_info "Creating self-contained fallback login.js..."
                cat << 'EOF' > "$WEBUI/js/login.js"
/**
 * PersistenceOS Self-Contained Login Script - FALLBACK VERSION
 */

// Configuration
const LOGIN_CONFIG = {
    API_BASE_URL: '/api',
    REDIRECT_URL: '/app.html',
    DEFAULT_USERNAME: 'root',
    DEFAULT_PASSWORD: 'linux',
    DEBUG: true,
    STANDALONE_MODE: true
};

// HTML Layout Generator
const LoginHTML = {
    createFullPage() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PersistenceOS - Login</title>
    <style>
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { font-family: Arial, sans-serif; background: linear-gradient(135deg, #0a4b78 0%, #003366 100%); height: 100vh; display: flex; justify-content: center; align-items: center; color: #333; }
        .login-container { width: 400px; max-width: 95%; }
        .login-card { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 10px 25px rgba(0,0,0,0.2); }
        .login-header { background: #0066cc; color: white; padding: 30px 20px; text-align: center; }
        .login-header h1 { margin: 0 0 5px 0; font-size: 28px; }
        .login-form { padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; font-size: 14px; }
        input[type="text"], input[type="password"] { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
        .btn { padding: 12px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; transition: background 0.3s; }
        .btn-primary { background: #0066cc; color: white; }
        .error-message { color: #e74c3c; margin-top: 10px; text-align: center; padding: 8px; border-radius: 4px; background-color: rgba(231, 76, 60, 0.1); }
        .hidden { display: none !important; }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>PersistenceOS</h1>
                <p>Version: 6.1.0</p>
            </div>
            <div class="login-form">
                <form id="login-form">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" value="root" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" placeholder="Enter password" required>
                    </div>
                    <div id="login-error" class="error-message hidden">Invalid username or password</div>
                    <div class="form-group">
                        <button type="button" id="login-button" class="btn btn-primary" style="width: 100%;">Log In</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        document.getElementById('login-button').addEventListener('click', async () => {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            if (username === 'root' && password === 'linux') {
                localStorage.setItem('authenticated', 'true');
                window.location.href = '/app.html?from_login=true';
            } else {
                document.getElementById('login-error').classList.remove('hidden');
            }
        });
    </script>
</body>
</html>`;
    }
};

// Initialize
if (LOGIN_CONFIG.STANDALONE_MODE) {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            document.open(); document.write(LoginHTML.createFullPage()); document.close();
        });
    } else {
        document.open(); document.write(LoginHTML.createFullPage()); document.close();
    }
}
EOF
                cp "$WEBUI/js/login.js" "/var/lib/persistence/web-ui/js/login.js"
                chmod 644 "$WEBUI/js/login.js" "/var/lib/persistence/web-ui/js/login.js"
                log_info "✅ Created self-contained fallback login.js"
            fi
        fi
    done

    log_info "✅ Static directory setup complete"
}

# Verify dependencies
verify_dependencies() {
    log_info "Verifying PersistenceOS dependencies..."

    # Check if directories exist
    for dir in "$PERSISTENCE_ROOT" "$BIN" "$SERVICES" "$WEBUI"; do
        if [ -d "$dir" ]; then
            log_info "✅ Directory exists: $dir"
        else
            log_info "⚠️  Directory missing: $dir"
        fi
    done

    # Check if JavaScript files exist
    for js_file in "app.js" "auth.js" "vue.js" "login.js"; do
        if [ -f "$WEBUI/js/$js_file" ]; then
            file_size=$(stat -c%s "$WEBUI/js/$js_file" 2>/dev/null || echo "unknown")
            log_info "✅ JavaScript file exists: $js_file ($file_size bytes)"
        else
            log_info "⚠️  JavaScript file missing: $js_file"
        fi
    done

    # Check if API files exist
    for api_file in "run_api.sh" "main.py"; do
        if [ -f "$PERSISTENCE_ROOT/api/$api_file" ]; then
            file_size=$(stat -c%s "$PERSISTENCE_ROOT/api/$api_file" 2>/dev/null || echo "unknown")
            log_info "✅ API file exists: $api_file ($file_size bytes)"
        else
            log_info "⚠️  API file missing: $api_file"
        fi
    done

    # Check if systemd services are installed
    for service_file in "persistenceos-api.service" "persistenceos-core-services.service"; do
        if [ -f "/etc/systemd/system/$service_file" ]; then
            log_info "✅ Systemd service installed: $service_file"
        else
            log_info "⚠️  Systemd service missing: $service_file"
        fi
    done

    # Check if services are enabled
    for service_name in "persistenceos-api" "persistenceos-core-services"; do
        if systemctl is-enabled "$service_name" >/dev/null 2>&1; then
            log_info "✅ Service enabled: $service_name"
        else
            log_info "⚠️  Service not enabled: $service_name"
        fi
    done

    log_info "✅ Dependency verification complete"
}

# --- MAIN EXECUTION ---

# Create directory structure
log_info "Creating PersistenceOS directory structure..."
mkdir -p "$PERSISTENCE_ROOT" "$BIN" "$SERVICES" "$WEBUI" "$LOGDIR"
mkdir -p "$WEBUI/js" "$WEBUI/css" "$WEBUI/img"
mkdir -p "/var/lib/persistence/web-ui"

# Get Primary IP for Configuration
PRIMARY_IP=$(hostname -I | awk '{print $1}')

# Deploy Web UI Components
log_info "Deploying web UI components..."

# Setup API directory and copy API files
setup_api_directory

# Deploy self-contained login (minimal HTML wrapper)
deploy_self_contained_login

# Setup static directory structure and copy JavaScript files
setup_static_directory

# Create API config file
cat > "$WEBUI/api-config.json" <<EOF
{
  "host": "${PRIMARY_IP:-localhost}",
  "http_port": 8080,
  "https_port": 8443,
  "api_base_url": "/api",
  "secure_api_base_url": "/api",
  "available_ips": ["${PRIMARY_IP:-127.0.0.1}", "127.0.0.1"]
}
EOF

# Create welcome message with IP display
create_welcome_message() {
    log_info "Creating welcome message with web UI IP addresses..."

    # Get primary IP
    local primary_ip="${PRIMARY_IP:-127.0.0.1}"

    # Create MOTD with ASCII logo and IP information
    cat > /etc/motd << EOF
╔═══════════════════════════════════════════════════════════════════════════════╗
║                                                                               ║
║  ██████╗ ███████╗██████╗ ███████╗██╗███████╗████████╗███████╗███╗   ██╗ ██████╗║
║  ██╔══██╗██╔════╝██╔══██╗██╔════╝██║██╔════╝╚══██╔══╝██╔════╝████╗  ██║██╔════╝║
║  ██████╔╝█████╗  ██████╔╝███████╗██║███████╗   ██║   █████╗  ██╔██╗ ██║██║     ║
║  ██╔═══╝ ██╔══╝  ██╔══██╗╚════██║██║╚════██║   ██║   ██╔══╝  ██║╚██╗██║██║     ║
║  ██║     ███████╗██║  ██║███████║██║███████║   ██║   ███████╗██║ ╚████║╚██████╗║
║  ╚═╝     ╚══════╝╚═╝  ╚═╝╚══════╝╚═╝╚══════╝   ╚═╝   ╚══════╝╚═╝  ╚═══╝ ╚═════╝║
║                                                                               ║
║                                    OS                                         ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝

Welcome to PersistenceOS - Advanced Storage Management and Snapshot Functionality

System Information:
  Hostname: $(hostname)
  Version: 6.1.0
  Kernel: $(uname -r)
  Uptime: $(uptime -p 2>/dev/null || uptime)

Web Interface Access:
  ▶ HTTP:  http://${primary_ip}:8080
  ▶ HTTPS: https://${primary_ip}:8443 (secure connection)

Default Login: root / linux

For support and documentation, visit: https://persistence-os.org

EOF

    # Also create /etc/issue for console login
    cat > /etc/issue << EOF

╔═══════════════════════════════════════════════════════════════════════════════╗
║                          PersistenceOS 6.1.0                                 ║
║                   Advanced Storage Management System                          ║
╚═══════════════════════════════════════════════════════════════════════════════╝

Web Interface: http://${primary_ip}:8080
Default Login: root / linux

EOF

    log_info "✅ Welcome message created with IP: ${primary_ip}"
}

# Create welcome script for dynamic IP display
create_welcome_script() {
    log_info "Creating dynamic welcome script..."

    # Create the welcome script (similar to older config.sh)
    cat > "$BIN/welcome.sh" <<'EOF'
#!/bin/bash
# PersistenceOS Welcome Message - Dynamic IP Display

# Get primary IP with multiple fallback methods
PRIMARY_IP=""
if command -v hostname >/dev/null 2>&1; then
    PRIMARY_IP=$(hostname -I 2>/dev/null | awk '{print $1}')
fi
if [ -z "$PRIMARY_IP" ] || [ "$PRIMARY_IP" = "" ]; then
    if command -v ip >/dev/null 2>&1; then
        PRIMARY_IP=$(ip route get ******* 2>/dev/null | awk '{print $7; exit}')
    fi
fi
if [ -z "$PRIMARY_IP" ] || [ "$PRIMARY_IP" = "" ]; then
    PRIMARY_IP="127.0.0.1"
fi

# Get service status with better error handling
NM_STATUS=$(systemctl is-active NetworkManager 2>/dev/null || echo "inactive")
API_STATUS=$(systemctl is-active persistenceos-api 2>/dev/null || echo "inactive")
CORE_STATUS=$(systemctl is-active persistenceos-core-services 2>/dev/null || echo "inactive")

echo "=============================================="
echo "       Welcome to PersistenceOS 6.1"
echo "=============================================="
echo ""
echo "System Status:"
echo "  Primary IP: $PRIMARY_IP"
echo "  NetworkManager: $NM_STATUS"
echo "  API Service: $API_STATUS"
echo "  Core Services: $CORE_STATUS"
echo ""
echo "🌐 Web Interface Access:"
if [ "$PRIMARY_IP" != "unknown" ] && [ "$PRIMARY_IP" != "" ]; then
    echo "  ▶ HTTP:  http://$PRIMARY_IP:8080"
    echo "  ▶ HTTPS: https://$PRIMARY_IP:8443 (secure connection)"
else
    echo "  ▶ HTTP:  http://localhost:8080"
    echo "  ▶ HTTPS: https://localhost:8443 (secure connection)"
fi
echo ""
echo "Default Login: root / linux"
echo ""
echo "📚 Quick Commands:"
echo "  systemctl status persistenceos-api              # Check API status"
echo "  systemctl status persistenceos-core-services    # Check core services"
echo "  journalctl -u persistenceos-api -f              # View API logs"
echo "  journalctl -u persistenceos-core-services -f    # View core service logs"
echo ""
echo "🔧 Troubleshooting:"
if [ "$API_STATUS" != "active" ]; then
    echo "  ⚠️  API service not running - try: systemctl start persistenceos-api"
fi
if [ "$CORE_STATUS" != "active" ]; then
    echo "  ⚠️  Core services not running - try: systemctl start persistenceos-core-services"
fi
if [ "$NM_STATUS" != "active" ]; then
    echo "  ⚠️  NetworkManager not running - try: systemctl start NetworkManager"
fi
echo "=============================================="
EOF

    chmod +x "$BIN/welcome.sh"
    log_info "✅ Dynamic welcome script created"
}

# Setup login hook for interactive shells
setup_login_hook() {
    log_info "Setting up login hook for interactive shells..."

    # Simple and reliable profile.d script (like older config.sh)
    cat > /etc/profile.d/persistenceos-welcome.sh <<'EOF'
#!/bin/bash
# Show PersistenceOS welcome message on login
if [ -f /usr/lib/persistence/bin/welcome.sh ]; then
    /usr/lib/persistence/bin/welcome.sh
fi
EOF

    chmod +x /etc/profile.d/persistenceos-welcome.sh
    log_info "✅ Login hook configured"
}

# Hybrid service management: Use existing services if available, create if missing
ensure_systemd_services() {
    log_info "Ensuring systemd services are properly installed (Hybrid Approach)..."

    # First, try to use existing service files from the services directory
    log_info "Checking for existing service files..."

    SERVICES_INSTALLED="false"

    # Check if services are already installed by spec file/KIWI
    if [ -f "/usr/lib/systemd/system/persistenceos-api.service" ] && [ -f "/usr/lib/systemd/system/persistenceos-core-services.service" ]; then
        log_info "✅ Service files already installed by build process"
        SERVICES_INSTALLED="true"
    elif [ -f "$SERVICES/persistenceos-api.service" ] && [ -f "$SERVICES/persistenceos-core-services.service" ]; then
        log_info "✅ Service files found in services directory, copying to systemd..."
        cp "$SERVICES/persistenceos-api.service" /etc/systemd/system/
        cp "$SERVICES/persistenceos-core-services.service" /etc/systemd/system/
        SERVICES_INSTALLED="true"
        log_info "✅ Copied existing service files to systemd"
    else
        log_info "⚠️  Service files not found, will create them as fallback"
    fi

    # Create run_api.sh script (always needed regardless of service source)
    log_info "Ensuring run_api.sh script exists..."
    cat > "$PERSISTENCE_ROOT/api/run_api.sh" <<'EOF'
#!/bin/bash
# PersistenceOS API Startup Script

set -e

# Configuration
API_PORT="${API_PORT:-8080}"
WEB_ROOT="${WEB_ROOT:-/usr/lib/persistence/web-ui}"
VAR_WEB_ROOT="${VAR_WEB_ROOT:-/var/lib/persistence/web-ui}"
DEBUG_MODE="${DEBUG_MODE:-false}"
API_CONFIG_PATH="${API_CONFIG_PATH:-/usr/lib/persistence/web-ui/api-config.json}"

# Logging
LOG_FILE="/var/log/persistenceos-api.log"
mkdir -p "$(dirname "$LOG_FILE")"

log_info() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $1" | tee -a "$LOG_FILE" >&2
}

# Verify Python and dependencies
log_info "Starting PersistenceOS API service..."
log_info "API Port: $API_PORT"
log_info "Web Root: $WEB_ROOT"
log_info "Debug Mode: $DEBUG_MODE"

# Check if Python 3.11 is available
if ! command -v python3.11 >/dev/null 2>&1; then
    if ! command -v python3 >/dev/null 2>&1; then
        log_error "Python 3 not found. Please install python311-base package."
        exit 1
    else
        PYTHON_CMD="python3"
        log_info "Using python3 command"
    fi
else
    PYTHON_CMD="python3.11"
    log_info "Using python3.11 command"
fi

# Verify main.py exists
MAIN_PY="/usr/lib/persistence/api/main.py"
if [ ! -f "$MAIN_PY" ]; then
    log_error "main.py not found at $MAIN_PY"
    exit 1
fi

# Create web directories if they don't exist
mkdir -p "$WEB_ROOT" "$VAR_WEB_ROOT"
mkdir -p "$WEB_ROOT/js" "$WEB_ROOT/css" "$WEB_ROOT/img"
mkdir -p "$VAR_WEB_ROOT/js" "$VAR_WEB_ROOT/css" "$VAR_WEB_ROOT/img"

# Set working directory
cd /usr/lib/persistence/api

# Export environment variables for the Python application
export API_PORT
export WEB_ROOT
export VAR_WEB_ROOT
export DEBUG_MODE
export API_CONFIG_PATH

# Start the FastAPI application
log_info "Starting FastAPI application with uvicorn..."
log_info "Command: $PYTHON_CMD -m uvicorn main:app --host 0.0.0.0 --port $API_PORT"

exec "$PYTHON_CMD" -m uvicorn main:app --host 0.0.0.0 --port "$API_PORT" --log-level info
EOF

    chmod +x "$PERSISTENCE_ROOT/api/run_api.sh"
    log_info "✅ Created run_api.sh script"

    # Create main.py if it doesn't exist (fallback)
    if [ ! -f "$PERSISTENCE_ROOT/api/main.py" ]; then
        log_info "Creating fallback main.py..."
        cat > "$PERSISTENCE_ROOT/api/main.py" <<'EOF'
#!/usr/bin/env python3
"""
PersistenceOS FastAPI Backend - Fallback Version
Main entry point for the PersistenceOS web interface and API
"""

import uvicorn
import sys
import os
import logging
from pathlib import Path

# Add the API directory to Python path
api_dir = Path(__file__).parent
sys.path.insert(0, str(api_dir))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("persistenceos")

def main():
    """Main entry point for PersistenceOS API server"""

    # Check if we're in debug mode
    debug_mode = "--debug" in sys.argv

    if debug_mode:
        logger.info("🔧 Starting PersistenceOS API in DEBUG mode")
        log_level = "debug"
        reload = True
    else:
        logger.info("🚀 Starting PersistenceOS API in PRODUCTION mode")
        log_level = "info"
        reload = False

    # Import the FastAPI app
    try:
        from app import app
        logger.info("✅ FastAPI app imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import FastAPI app: {e}")
        logger.info("Creating minimal FastAPI app as fallback...")

        # Create minimal FastAPI app
        from fastapi import FastAPI
        from fastapi.responses import JSONResponse

        app = FastAPI(title="PersistenceOS API", version="6.1.0")

        @app.get("/")
        async def root():
            return {"message": "PersistenceOS API - Fallback Mode"}

        @app.get("/api/health")
        async def health():
            return {"status": "healthy", "mode": "fallback"}

    # Get host and port from environment or use defaults
    host = os.getenv("PERSISTENCE_HOST", "0.0.0.0")
    port = int(os.getenv("PERSISTENCE_PORT", "8080"))

    logger.info(f"🌐 Server will bind to {host}:{port}")
    logger.info(f"📁 API directory: {api_dir}")
    logger.info(f"🔄 Auto-reload: {reload}")

    try:
        # Start the Uvicorn server
        uvicorn.run(
            app,
            host=host,
            port=port,
            log_level=log_level,
            reload=reload,
            access_log=True,
            server_header=False,
            date_header=False
        )
    except KeyboardInterrupt:
        logger.info("🛑 Server stopped by user")
    except Exception as e:
        logger.error(f"❌ Server error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
EOF
        chmod 644 "$PERSISTENCE_ROOT/api/main.py"
        log_info "✅ Created fallback main.py"
    else
        log_info "✅ main.py already exists"
    fi

    # If services weren't found, create them as fallback
    if [ "$SERVICES_INSTALLED" = "false" ]; then
        log_info "Creating fallback service files..."

        # Create persistenceos-api.service as fallback
        cat > "/etc/systemd/system/persistenceos-api.service" <<'EOF'
[Unit]
Description=PersistenceOS Web UI and API Service
After=network.target
Wants=network-online.target
Requires=network-online.target

[Service]
Type=simple
# Verify web-ui directory before starting API
ExecStartPre=/bin/bash -c 'mkdir -p /usr/lib/persistence/web-ui/js /usr/lib/persistence/web-ui/css /usr/lib/persistence/web-ui/img'
ExecStart=/usr/lib/persistence/api/run_api.sh
WorkingDirectory=/usr/lib/persistence/api
Environment=WEB_ROOT=/usr/lib/persistence/web-ui
Environment=API_PORT=8080
Environment=DEBUG_MODE=false
Environment=VAR_WEB_ROOT=/var/lib/persistence/web-ui
Environment=API_CONFIG_PATH=/usr/lib/persistence/web-ui/api-config.json
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
# Make sure we have enough file descriptors to handle multiple connections
LimitNOFILE=10000

[Install]
WantedBy=multi-user.target
EOF
        log_info "✅ Created fallback persistenceos-api.service"

        # Create minimal persistenceos-core-services.service as fallback
        cat > "/etc/systemd/system/persistenceos-core-services.service" <<'EOF'
[Unit]
Description=PersistenceOS Core Services Enablement (Fallback)
DefaultDependencies=no
Before=basic.target network.target
After=local-fs.target
Wants=local-fs.target
Conflicts=shutdown.target

[Service]
Type=oneshot
RemainAfterExit=yes
ExecStart=/bin/systemctl enable NetworkManager
ExecStart=/bin/systemctl enable persistenceos-api.service

[Install]
WantedBy=basic.target
EOF
        log_info "✅ Created fallback persistenceos-core-services.service"
        SERVICES_INSTALLED="true"
    fi

    # Always reload systemd daemon and enable services
    log_info "Configuring systemd services..."
    systemctl daemon-reload
    log_info "✅ Systemd daemon reloaded"

    # Enable services
    for service in "persistenceos-api" "persistenceos-core-services"; do
        if systemctl enable "${service}.service" 2>/dev/null; then
            log_info "✅ Enabled ${service}.service"
        else
            log_info "⚠️  Failed to enable ${service}.service (may already be enabled)"
        fi
    done

    log_info "✅ Systemd services ensured and configured successfully"
}

# Verify Setup
verify_dependencies

# Ensure systemd services are properly installed (hybrid approach)
ensure_systemd_services

# Create welcome message and login hooks
create_welcome_message
create_welcome_script
setup_login_hook

echo "[SUCCESS] PersistenceOS config complete."
echo "[INFO] Web UI available at: http://${PRIMARY_IP:-127.0.0.1}:8080"
