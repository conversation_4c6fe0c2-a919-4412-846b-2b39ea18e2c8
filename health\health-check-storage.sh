#!/bin/bash
#================
# FILE          : health-check-storage.sh
#----------------
# PROJECT       : PersistenceOS
# COPYRIGHT     : (c) 2024 PersistenceOS Team
# AUTHOR        : PersistenceOS Team
# PACKAGE       : PersistenceOS
# LICENSE       : MIT
# PURPOSE       : Storage health monitoring script
#================

# Set strict error handling
set -e
set -o pipefail

# Script version
VERSION="1.0.0"

# Source common functions
SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
if [ -f "${SCRIPT_DIR}/../scripts/util-common.sh" ]; then
    source "${SCRIPT_DIR}/../scripts/util-common.sh"
else
    echo "Error: util-common.sh not found"
    exit 1
fi

# Define log file
LOG_FILE="${PERSISTENCE_LOG}/health-check-storage.log"
export COMMON_LOG_FILE="${LOG_FILE}"

# Show usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --help, -h            Show this help message"
    echo "  --version, -v         Show version information"
    echo "  --verbose             Show detailed output"
    echo "  --output FORMAT       Output format (json, text)"
    echo ""
    echo "Examples:"
    echo "  $0                    Run storage health check"
    echo "  $0 --output json      Run storage health check and output in JSON format"
}

# Check filesystem health
check_filesystem_health() {
    log_info "Checking filesystem health"
    
    # Get filesystem information
    local filesystems=$(df -h -t btrfs -t xfs -t ext4 | grep -v "Filesystem" | awk '{print $1 "," $6}')
    
    # Initialize arrays
    local critical_filesystems=()
    local warning_filesystems=()
    local healthy_filesystems=()
    local filesystem_details=()
    
    # Check each filesystem
    while IFS=',' read -r device mountpoint; do
        log_info "Checking filesystem: ${device} (${mountpoint})"
        
        # Get usage information
        local usage=$(df -h "${mountpoint}" | awk 'NR==2 {print $5}' | tr -d '%')
        
        # Determine status
        local status="healthy"
        
        if [ "${usage}" -gt 90 ]; then
            status="critical"
            critical_filesystems+=("${mountpoint}")
        elif [ "${usage}" -gt 80 ]; then
            status="warning"
            warning_filesystems+=("${mountpoint}")
        else
            healthy_filesystems+=("${mountpoint}")
        fi
        
        # Check filesystem type
        local fs_type=$(df -T "${mountpoint}" | awk 'NR==2 {print $2}')
        
        # Add to filesystem details
        filesystem_details+=("{\"mountpoint\": \"${mountpoint}\", \"device\": \"${device}\", \"type\": \"${fs_type}\", \"usage\": ${usage}, \"status\": \"${status}\"}")
    done <<< "${filesystems}"
    
    # Determine overall status
    local status="healthy"
    
    if [ ${#critical_filesystems[@]} -gt 0 ]; then
        status="critical"
    elif [ ${#warning_filesystems[@]} -gt 0 ]; then
        status="warning"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${status}",
    "critical": [$(printf '"%s",' "${critical_filesystems[@]}" | sed 's/,$//')],
    "warning": [$(printf '"%s",' "${warning_filesystems[@]}" | sed 's/,$//')],
    "healthy": [$(printf '"%s",' "${healthy_filesystems[@]}" | sed 's/,$//')],
    "filesystems": [$(printf '%s,' "${filesystem_details[@]}" | sed 's/,$//')]
}
EOF
)
    
    log_success "Filesystem health check completed with status: ${status}"
    echo "${result}"
    return 0
}

# Check btrfs health
check_btrfs_health() {
    log_info "Checking btrfs health"
    
    # Check if btrfs is used
    if ! df -t btrfs &>/dev/null; then
        log_info "No btrfs filesystems found"
        
        # Create result
        local result=$(cat << EOF
{
    "status": "healthy",
    "message": "No btrfs filesystems found",
    "filesystems": []
}
EOF
)
        
        echo "${result}"
        return 0
    fi
    
    # Get btrfs filesystems
    local btrfs_filesystems=$(df -t btrfs | grep -v "Filesystem" | awk '{print $6}')
    
    # Initialize arrays
    local critical_filesystems=()
    local warning_filesystems=()
    local healthy_filesystems=()
    local filesystem_details=()
    
    # Check each btrfs filesystem
    for mountpoint in ${btrfs_filesystems}; do
        log_info "Checking btrfs filesystem: ${mountpoint}"
        
        # Run btrfs check
        local check_result
        if check_result=$(btrfs device stats "${mountpoint}" 2>&1); then
            # Check for errors
            local write_errors=$(echo "${check_result}" | grep "write_io_errs" | awk '{print $2}')
            local read_errors=$(echo "${check_result}" | grep "read_io_errs" | awk '{print $2}')
            local flush_errors=$(echo "${check_result}" | grep "flush_io_errs" | awk '{print $2}')
            local corruption_errors=$(echo "${check_result}" | grep "corruption_errs" | awk '{print $2}')
            local generation_errors=$(echo "${check_result}" | grep "generation_errs" | awk '{print $2}')
            
            # Determine status
            local status="healthy"
            local message="No errors found"
            
            if [ "${write_errors}" -gt 0 ] || [ "${read_errors}" -gt 0 ] || [ "${corruption_errors}" -gt 0 ]; then
                status="critical"
                message="Critical errors found"
                critical_filesystems+=("${mountpoint}")
            elif [ "${flush_errors}" -gt 0 ] || [ "${generation_errors}" -gt 0 ]; then
                status="warning"
                message="Warning errors found"
                warning_filesystems+=("${mountpoint}")
            else
                healthy_filesystems+=("${mountpoint}")
            fi
            
            # Add to filesystem details
            filesystem_details+=("{\"mountpoint\": \"${mountpoint}\", \"status\": \"${status}\", \"message\": \"${message}\", \"write_errors\": ${write_errors}, \"read_errors\": ${read_errors}, \"flush_errors\": ${flush_errors}, \"corruption_errors\": ${corruption_errors}, \"generation_errors\": ${generation_errors}}")
        else
            # Error running btrfs check
            local status="warning"
            local message="Error checking btrfs filesystem"
            
            warning_filesystems+=("${mountpoint}")
            
            # Add to filesystem details
            filesystem_details+=("{\"mountpoint\": \"${mountpoint}\", \"status\": \"${status}\", \"message\": \"${message}\"}")
        fi
    done
    
    # Determine overall status
    local status="healthy"
    
    if [ ${#critical_filesystems[@]} -gt 0 ]; then
        status="critical"
    elif [ ${#warning_filesystems[@]} -gt 0 ]; then
        status="warning"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${status}",
    "critical": [$(printf '"%s",' "${critical_filesystems[@]}" | sed 's/,$//')],
    "warning": [$(printf '"%s",' "${warning_filesystems[@]}" | sed 's/,$//')],
    "healthy": [$(printf '"%s",' "${healthy_filesystems[@]}" | sed 's/,$//')],
    "filesystems": [$(printf '%s,' "${filesystem_details[@]}" | sed 's/,$//')]
}
EOF
)
    
    log_success "Btrfs health check completed with status: ${status}"
    echo "${result}"
    return 0
}

# Check storage pools
check_storage_pools() {
    log_info "Checking storage pools"
    
    # Check if storage-manager.sh exists
    if [ ! -x "${PERSISTENCE_SCRIPTS}/storage-manager.sh" ]; then
        log_warning "storage-manager.sh not found, skipping storage pool check"
        
        # Create result
        local result=$(cat << EOF
{
    "status": "unknown",
    "message": "storage-manager.sh not found",
    "pools": []
}
EOF
)
        
        echo "${result}"
        return 0
    fi
    
    # Get storage pools
    local pools_output
    if pools_output=$("${PERSISTENCE_SCRIPTS}/storage-manager.sh" pool list --output json 2>&1); then
        # Parse pools
        local pools=$(echo "${pools_output}" | jq -c '.pools')
        
        # Check if pools exist
        if [ "${pools}" = "[]" ] || [ "${pools}" = "null" ]; then
            log_warning "No storage pools found"
            
            # Create result
            local result=$(cat << EOF
{
    "status": "warning",
    "message": "No storage pools found",
    "pools": []
}
EOF
)
            
            echo "${result}"
            return 0
        fi
        
        # Initialize arrays
        local critical_pools=()
        local warning_pools=()
        local healthy_pools=()
        
        # Check each pool
        local pool_count=$(echo "${pools}" | jq 'length')
        
        for ((i=0; i<pool_count; i++)); do
            local pool=$(echo "${pools}" | jq -c ".[${i}]")
            local pool_name=$(echo "${pool}" | jq -r '.name')
            local pool_status=$(echo "${pool}" | jq -r '.status')
            
            log_info "Checking storage pool: ${pool_name} (${pool_status})"
            
            # Determine status
            if [ "${pool_status}" = "DEGRADED" ] || [ "${pool_status}" = "FAULTED" ] || [ "${pool_status}" = "OFFLINE" ]; then
                critical_pools+=("${pool_name}")
            elif [ "${pool_status}" = "ONLINE" ]; then
                healthy_pools+=("${pool_name}")
            else
                warning_pools+=("${pool_name}")
            fi
        done
        
        # Determine overall status
        local status="healthy"
        
        if [ ${#critical_pools[@]} -gt 0 ]; then
            status="critical"
        elif [ ${#warning_pools[@]} -gt 0 ]; then
            status="warning"
        fi
        
        # Create result
        local result=$(cat << EOF
{
    "status": "${status}",
    "critical": [$(printf '"%s",' "${critical_pools[@]}" | sed 's/,$//')],
    "warning": [$(printf '"%s",' "${warning_pools[@]}" | sed 's/,$//')],
    "healthy": [$(printf '"%s",' "${healthy_pools[@]}" | sed 's/,$//')],
    "pools": ${pools}
}
EOF
)
        
        log_success "Storage pool check completed with status: ${status}"
        echo "${result}"
    else
        # Error getting storage pools
        log_error "Error getting storage pools: ${pools_output}"
        
        # Create result
        local result=$(cat << EOF
{
    "status": "warning",
    "message": "Error getting storage pools",
    "pools": []
}
EOF
)
        
        echo "${result}"
    fi
    
    return 0
}

# Check storage health
check_storage_health() {
    log_info "Checking storage health"
    
    # Check components
    local filesystem_health=$(check_filesystem_health)
    local btrfs_health=$(check_btrfs_health)
    local pool_health=$(check_storage_pools)
    
    # Determine overall status
    local filesystem_status=$(echo "${filesystem_health}" | grep -o '"status": *"[^"]*"' | head -n 1 | cut -d'"' -f4)
    local btrfs_status=$(echo "${btrfs_health}" | grep -o '"status": *"[^"]*"' | head -n 1 | cut -d'"' -f4)
    local pool_status=$(echo "${pool_health}" | grep -o '"status": *"[^"]*"' | head -n 1 | cut -d'"' -f4)
    
    local overall_status="healthy"
    
    if [ "${filesystem_status}" = "critical" ] || [ "${btrfs_status}" = "critical" ] || [ "${pool_status}" = "critical" ]; then
        overall_status="critical"
    elif [ "${filesystem_status}" = "warning" ] || [ "${btrfs_status}" = "warning" ] || [ "${pool_status}" = "warning" ]; then
        overall_status="warning"
    fi
    
    # Create result
    local result=$(cat << EOF
{
    "status": "${overall_status}",
    "details": {
        "filesystems": ${filesystem_health},
        "btrfs": ${btrfs_health},
        "pools": ${pool_health}
    }
}
EOF
)
    
    log_success "Storage health check completed with status: ${overall_status}"
    
    # Output health status
    if [ "${OUTPUT_FORMAT}" = "json" ]; then
        echo "${result}"
    else
        # Default to text format
        echo "Storage Health Status: ${overall_status}"
        echo ""
        echo "Filesystem Status: ${filesystem_status}"
        echo "Btrfs Status: ${btrfs_status}"
        echo "Storage Pool Status: ${pool_status}"
        echo ""
        
        # Show critical filesystems
        local critical_filesystems=$(echo "${filesystem_health}" | grep -o '"critical": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        if [ -n "${critical_filesystems}" ]; then
            echo "Critical Filesystems: ${critical_filesystems}"
        fi
        
        # Show warning filesystems
        local warning_filesystems=$(echo "${filesystem_health}" | grep -o '"warning": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        if [ -n "${warning_filesystems}" ]; then
            echo "Warning Filesystems: ${warning_filesystems}"
        fi
        
        # Show critical pools
        local critical_pools=$(echo "${pool_health}" | grep -o '"critical": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        if [ -n "${critical_pools}" ]; then
            echo "Critical Pools: ${critical_pools}"
        fi
        
        # Show warning pools
        local warning_pools=$(echo "${pool_health}" | grep -o '"warning": *\[[^]]*\]' | cut -d'[' -f2 | cut -d']' -f1)
        if [ -n "${warning_pools}" ]; then
            echo "Warning Pools: ${warning_pools}"
        fi
    fi
    
    return 0
}

# Main function
main() {
    # Default values
    VERBOSE="false"
    OUTPUT_FORMAT="text"
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --help|-h)
                show_usage
                exit 0
                ;;
            --version|-v)
                echo "health-check-storage.sh version ${VERSION}"
                echo "Part of PersistenceOS ${PERSISTENCE_VERSION}"
                exit 0
                ;;
            --verbose)
                VERBOSE="true"
                shift
                ;;
            --output)
                OUTPUT_FORMAT="$2"
                shift 2
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log_info "Starting storage health check (version ${VERSION})"
    
    # Run storage health check
    check_storage_health
    
    log_success "Storage health check completed successfully"
    return 0
}

# Run main function
main "$@"
exit $?
